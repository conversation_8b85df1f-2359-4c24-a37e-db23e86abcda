package seoclarity.backend.summary;

import java.util.HashMap;
import java.util.Map;

import seoclarity.backend.dao.clickhouse.internallink.InternalLinkServer1Dao;
import seoclarity.backend.utils.SpringBeanFactory;

public class InternalLinkDailySummaryForClarityDBFix {
	
	private InternalLinkServer1Dao internalLinkServer1Dao;
	
	
	//20190624_02
	
	//claritydb_upload_log id -- mdb010
//	private static Integer logId;
	
	public InternalLinkDailySummaryForClarityDBFix(){
		internalLinkServer1Dao = SpringBeanFactory.getBean("internalLinkServer1Dao");
	}
	

	public static void main(String[] args) {
		InternalLinkDailySummaryForClarityDBFix internalLinkDailySummaryForClarityDB = new InternalLinkDailySummaryForClarityDBFix();
		
		internalLinkDailySummaryForClarityDB.process();
	}
	
	
	private void process() {
		
		Map<Integer, Integer>  map = new HashMap<>();
		
		map.put(139237,765);
		map.put(139236,8708);
		map.put(139235,6457);
		map.put(139234,9696);
		map.put(139232,6457);
		map.put(139231,9695);
		map.put(139230,9754);
		map.put(139228,5994);
		map.put(139226,5994);
		map.put(139225,4);
		map.put(139224,765);
		map.put(139223,9695);
		map.put(139222,9695);
		map.put(139221,8514);
		map.put(139220,9695);
		map.put(139218,9696);
		map.put(139217,7048);
		map.put(139216,9465);
		map.put(139214,569);
		map.put(139211,1313);
		map.put(139208,4);
		map.put(139207,8514);
		map.put(139206,4);
		map.put(139205,4);
		map.put(139204,7733);
		map.put(139203,4);
		map.put(139201,4);
		map.put(139200,7431);
		map.put(139198,4);
		map.put(139197,9695);
		map.put(139196,9694);
		map.put(139193,6382);
		map.put(139186,7001);
		map.put(139185,7001);
		map.put(139184,7001);
		map.put(139183,7001);
		map.put(139182,7001);
		map.put(139181,7001);
		map.put(139180,7001);
		map.put(139179,6381);
		map.put(139178,4);
		map.put(139177,7001);
		map.put(139176,4);
		map.put(139175,4);
		map.put(139174,4);
		map.put(139173,7001);
		map.put(139172,7001);
		map.put(139171,7001);
		map.put(139170,7001);
		map.put(139169,7001);
		map.put(139168,765);
		map.put(139141,4);
		map.put(139140,7431);
		map.put(139139,4);
		map.put(139138,4);
		map.put(139137,4);
		map.put(139136,4);
		map.put(139135,4);
		map.put(139134,8514);
		map.put(139133,7733);
		map.put(139132,6457);
		map.put(139131,6457);
		map.put(139130,9879);
		map.put(139107,9803);
		map.put(139106,9803);
		map.put(139083,9803);
		map.put(139082,9803);
		map.put(139059,9803);
		map.put(139058,9803);
		map.put(139057,8958);
		map.put(139050,8958);
		map.put(139049,7431);
		map.put(139048,8514);
		map.put(139047,1701);
		map.put(139046,4);
		map.put(139045,7733);
		map.put(139022,7733);
		map.put(139021,7733);
		map.put(138997,7135);
		map.put(138996,7135);
		map.put(138971,4);
		map.put(138970,7431);
		map.put(138967,6381);
		map.put(138966,6998);
		map.put(138963,6060);
		map.put(138962,7431);
		map.put(138961,7431);
		map.put(138960,7431);
		map.put(138954,9738);
		map.put(138953,5994);
		map.put(138952,9843);
		map.put(138951,9696);
		map.put(138950,7135);
		map.put(138949,7733);
		map.put(138948,5994);
		map.put(138947,4);
		map.put(138946,7135);
		map.put(138945,8443);
		map.put(138944,4);
		map.put(138943,4);
		map.put(138942,4);
		map.put(138941,5994);
		map.put(138940,4);
		map.put(138939,4);
		map.put(138938,365);
		map.put(138937,4);
		map.put(138936,5592);
		map.put(138935,365);
		map.put(138934,365);
		map.put(138933,365);
		map.put(138932,7733);
		map.put(138909,9031);
		map.put(138908,9031);
		map.put(138907,9875);
		map.put(138906,4);
		map.put(138905,4);
		map.put(138904,1701);
		map.put(138903,4);
		map.put(138902,4);
		map.put(138901,4);
		map.put(138900,5994);
		map.put(138899,4);
		map.put(138898,4);
		map.put(138897,9738);
		map.put(138896,7464);
		map.put(138895,7464);
		map.put(138894,9803);
		map.put(138893,9738);
		map.put(138892,9463);
		map.put(138891,1701);
		map.put(138890,1701);
		map.put(138867,9511);
		map.put(138866,9511);
		map.put(138865,7733);
		map.put(138864,256);
		map.put(138860,9408);
		map.put(138859,6457);
		map.put(138853,9626);
		map.put(138852,9834);
		map.put(138851,5019);
		map.put(138850,570);
		map.put(138849,7001);
		map.put(138848,8422);
		map.put(138847,8422);
		map.put(138846,8422);
		map.put(138845,9696);
		map.put(138844,9694);
		map.put(138843,8625);
		map.put(138842,7585);
		map.put(138840,7585);
		map.put(138839,7585);
		map.put(138838,9834);
		map.put(138837,7585);
		map.put(138836,7585);
		map.put(138835,8976);
		map.put(138834,1909);
		map.put(138832,9696);
		map.put(138830,8976);
		map.put(138829,1909);
		map.put(138828,1351);
		map.put(138827,4609);
		map.put(138826,4609);
		map.put(138825,4609);
		map.put(138824,8422);
		map.put(138823,9821);
		map.put(138822,9463);
		map.put(138811,6464);
		map.put(138810,6464);
		map.put(138809,9463);
		map.put(138808,9622);
		map.put(138807,9821);
		map.put(138806,8425);
		map.put(138805,9833);
		map.put(138804,9803);
		map.put(138803,9803);
		map.put(138802,9485);
		map.put(138801,9821);
		map.put(138800,9821);
		map.put(138778,8810);
		map.put(138777,8810);
		map.put(138776,8810);
		map.put(138754,8810);
		map.put(138753,8810);
		map.put(138752,8810);
		map.put(138730,8810);
		map.put(138729,8810);
		map.put(138728,8810);
		map.put(138727,9463);
		map.put(138726,9463);
		map.put(138725,9463);
		map.put(138724,8614);
		map.put(138723,8614);
		map.put(138722,9824);
		map.put(138721,9696);
		map.put(138720,9696);
		map.put(138718,7585);
		map.put(138717,4);
		map.put(138716,1701);
		map.put(138710,9685);
		map.put(138704,9684);
		map.put(138698,9674);
		map.put(138692,9653);
		map.put(138686,9588);
		map.put(138680,9587);
		map.put(138674,9586);
		map.put(138668,9492);
		map.put(138662,9491);
		map.put(138656,9396);
		map.put(138650,8815);
		map.put(138644,7608);
		map.put(138638,7571);
		map.put(138632,7546);
		map.put(138626,7513);
		map.put(138620,7512);
		map.put(138614,7499);
		map.put(138608,7473);
		map.put(138602,7472);
		map.put(138596,7406);
		map.put(138590,7262);
		map.put(138584,7118);
		map.put(138578,6986);
		map.put(138572,6985);
		map.put(138566,6981);
		map.put(138560,6906);
		map.put(138554,6905);
		map.put(138548,6904);
		map.put(138542,6902);
		map.put(138536,6901);
		map.put(138530,6900);
		map.put(138524,6899);
		map.put(138518,6898);
		map.put(138512,6897);
		map.put(138506,6896);
		map.put(138500,6895);
		map.put(138494,6894);
		map.put(138488,6893);
		map.put(138482,6891);
		map.put(138476,6890);
		map.put(138470,6888);
		map.put(138464,6886);
		map.put(138458,6885);
		map.put(138452,6884);
		map.put(138446,6883);
		map.put(138440,6882);
		map.put(138434,6881);
		map.put(138428,6880);
		map.put(138422,6879);
		map.put(138416,6878);
		map.put(138410,6876);
		map.put(138404,6875);
		map.put(138398,6874);
		map.put(138392,6873);
		map.put(138386,6871);
		map.put(138380,6869);
		map.put(138374,6868);
		map.put(138368,6867);
		map.put(138362,6866);
		map.put(138356,6865);
		map.put(138350,6864);
		map.put(138344,6863);
		map.put(138338,6862);
		map.put(138332,6861);
		map.put(138326,6860);
		map.put(138320,6859);
		map.put(138314,6858);
		map.put(138308,6857);
		map.put(138302,6856);
		map.put(138296,6854);
		map.put(138290,6853);
		map.put(138284,6852);
		map.put(138278,6851);
		map.put(138272,6850);
		map.put(138266,6849);
		map.put(138260,6848);
		map.put(138254,6847);
		map.put(138248,6846);
		map.put(138242,6845);
		map.put(138236,6843);
		map.put(138230,6842);
		map.put(138224,6841);
		map.put(138218,6840);
		map.put(138212,6839);
		map.put(138206,6838);
		map.put(138200,6837);
		map.put(138194,6835);
		map.put(138188,6834);
		map.put(138182,6833);
		map.put(138176,6832);
		map.put(138170,6831);
		map.put(138164,6830);
		map.put(138158,6829);
		map.put(138152,6828);
		map.put(138146,6827);
		map.put(138140,6826);
		map.put(138134,6825);
		map.put(138128,6824);
		map.put(138122,6823);
		map.put(138116,6822);
		map.put(138110,6821);
		map.put(138104,6630);
		map.put(138103,8711);
		map.put(138101,8711);
		map.put(138076,9460);
		map.put(138075,9463);
		map.put(138074,9825);
		map.put(138073,569);
		map.put(138072,8422);
		map.put(138071,9829);
		map.put(138070,9792);
		map.put(138069,9830);
		map.put(138068,8711);
		map.put(138066,8711);
		map.put(138065,8711);
		map.put(138064,9696);
		map.put(138063,8763);
		map.put(138062,9799);
		map.put(138061,9800);
		map.put(138060,9793);
		map.put(138059,9794);
		map.put(138058,9807);
		map.put(138057,9808);
		map.put(138056,9809);
		map.put(138055,9810);
		map.put(138054,9798);
		map.put(138053,9797);
		map.put(138052,9696);
		map.put(138051,9796);
		map.put(138050,9795);
		map.put(138049,9792);
		map.put(138048,9791);
		map.put(138047,9740);
		map.put(138041,6680);
		map.put(138040,9829);
		map.put(138039,9740);
		map.put(138038,8514);
		map.put(138037,9696);
		map.put(138015,8873);
		map.put(138014,8873);
		map.put(138013,8873);
		map.put(138012,8873);
		map.put(138011,9829);
		map.put(137990,8863);
		map.put(137989,8863);
		map.put(137988,8863);
		map.put(137987,8863);
		map.put(137986,9696);
		map.put(137985,568);
		map.put(137984,9826);
		map.put(137983,9827);
		map.put(137977,6117);
		map.put(137971,9408);
		map.put(137969,2623);
		map.put(137956,7733);
		map.put(137955,7733);
		map.put(137954,7733);
		map.put(137953,7733);
		map.put(137952,7733);
		map.put(137951,9696);
		map.put(137950,9696);
		map.put(137949,9696);
		map.put(137948,6465);
		map.put(137947,9696);
		map.put(137945,9803);
		map.put(137944,9803);
		map.put(137943,9803);
		map.put(137942,9174);
		map.put(137941,7733);
		map.put(137940,9622);
		map.put(137939,2466);
		map.put(137937,4);
		map.put(137936,4);
		map.put(137914,7733);
		map.put(137913,7733);
		map.put(137912,7733);
		map.put(137911,6382);
		map.put(137910,6381);
		map.put(137909,6382);
		map.put(137889,8868);
		map.put(137888,8868);
		map.put(137887,8868);
		map.put(137886,8868);
		map.put(137885,8868);
		map.put(137884,9622);
		map.put(137883,9622);
		map.put(137882,9622);
		map.put(137881,2838);
		map.put(137880,1909);
		map.put(137879,1909);
		map.put(137878,1909);
		map.put(137877,8975);
		map.put(137876,4661);
		map.put(137875,1909);
		map.put(137874,1909);
		map.put(137873,8625);
		map.put(137872,9118);
		map.put(137852,8864);
		map.put(137851,8864);
		map.put(137850,8864);
		map.put(137849,8864);
		map.put(137848,8864);
		map.put(137847,8625);
		map.put(137845,8514);
		map.put(137825,9583);
		map.put(137824,9583);
		map.put(137823,9583);
		map.put(137822,9583);
		map.put(137821,9583);
		map.put(137801,9583);
		map.put(137800,9583);
		map.put(137799,9583);
		map.put(137798,9583);
		map.put(137797,9583);
		map.put(137777,9583);
		map.put(137776,9583);
		map.put(137775,9583);
		map.put(137774,9583);
		map.put(137773,9583);
		map.put(137753,9583);
		map.put(137752,9583);
		map.put(137751,9583);
		map.put(137750,9583);
		map.put(137749,9583);
		map.put(137729,9583);
		map.put(137728,9583);
		map.put(137727,9583);
		map.put(137726,9583);
		map.put(137725,9583);
		map.put(137705,9583);
		map.put(137704,9583);
		map.put(137703,9583);
		map.put(137702,9583);
		map.put(137701,9583);
		map.put(137681,9583);
		map.put(137680,9583);
		map.put(137679,9583);
		map.put(137678,9583);
		map.put(137677,9583);
		map.put(137657,9583);
		map.put(137656,9583);
		map.put(137655,9583);
		map.put(137654,9583);
		map.put(137653,9583);
		map.put(137633,9583);
		map.put(137632,9583);
		map.put(137631,9583);
		map.put(137630,9583);
		map.put(137629,9583);
		map.put(137609,9583);
		map.put(137608,9583);
		map.put(137607,9583);
		map.put(137606,9583);
		map.put(137605,9583);
		map.put(137585,9583);
		map.put(137584,9583);
		map.put(137583,9583);
		map.put(137582,9583);
		map.put(137581,9583);
		map.put(137561,9583);
		map.put(137560,9583);
		map.put(137559,9583);
		map.put(137558,9583);
		map.put(137557,9583);
		map.put(137537,9583);
		map.put(137536,9583);
		map.put(137535,9583);
		map.put(137534,9583);
		map.put(137533,9583);
		map.put(137513,9583);
		map.put(137512,9583);
		map.put(137511,9583);
		map.put(137510,9583);
		map.put(137509,9583);
		map.put(137489,9583);
		map.put(137488,9583);
		map.put(137487,9583);
		map.put(137486,9583);
		map.put(137485,9583);
		map.put(137465,9583);
		map.put(137464,9583);
		map.put(137463,9583);
		map.put(137462,9583);
		map.put(137461,9583);
		map.put(137441,9583);
		map.put(137440,9583);
		map.put(137439,9583);
		map.put(137438,9583);
		map.put(137437,9583);
		map.put(137417,9583);
		map.put(137416,9583);
		map.put(137415,9583);
		map.put(137414,9583);
		map.put(137413,9583);
		map.put(137393,9583);
		map.put(137392,9583);
		map.put(137391,9583);
		map.put(137390,9583);
		map.put(137389,9583);
		map.put(137369,9583);
		map.put(137368,9583);
		map.put(137367,9583);
		
		
		for (Integer crawlId: map.keySet()) {
			
			internalLinkServer1Dao.summaryFix(map.get(crawlId), crawlId);
		}
	}
	

}
