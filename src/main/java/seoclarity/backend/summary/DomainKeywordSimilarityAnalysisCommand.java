package seoclarity.backend.summary;

import info.debatty.java.stringsimilarity.Jaccard;
import lombok.extern.apachecommons.CommonsLog;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import seoclarity.backend.dao.clickhouse.rankingforecast.RankingForecastDao;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.clickhouse.RankingForecastEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.net.UnknownHostException;
import java.util.*;

/**
 * <AUTHOR>
 * @create 2018-07-08 21:46
 **/
@CommonsLog
public class DomainKeywordSimilarityAnalysisCommand implements Runnable {

    private RankingForecastDao rankingForecastDao;
    private RankingForecastEntity baseForecastEntity;
    private Date rankDate;
    private Date baseDate;
    private List<CLRankingDetailEntity> keywordEntities;
    private int matricsUrlSize = 10;
    private Jaccard jaccard = new Jaccard(3);


    public DomainKeywordSimilarityAnalysisCommand(final List<CLRankingDetailEntity> keywordEntities, Date baseDate, Date rankDate,
                                                  RankingForecastEntity baseForecastEntity) {
        rankingForecastDao = SpringBeanFactory.getBean("rankingForecastDao");
        this.baseForecastEntity = baseForecastEntity;
        this.baseDate = baseDate;
        this.rankDate = rankDate;
        this.keywordEntities = keywordEntities;
    }

    @Override
    public void run() {
        long startTime = System.currentTimeMillis();
        log.info("Thread Start :"+Thread.currentThread().getName());
        process();
        log.info("Thread End :"+Thread.currentThread().getName()+" total time :"+ (System.currentTimeMillis() - startTime)/1000);
    }

    public void process() {

        String baseDateString = FormatUtils.formatDate(baseDate, "yyyy-MM-dd");

        Map<Long, List<CLRankingDetailEntity>> keywordMap = new HashMap<>();
        List<RankingForecastEntity> forecastEntities = new ArrayList<>();

        for (CLRankingDetailEntity detailEntity : keywordEntities) {
            long rankCheckId = detailEntity.getKeywordRankcheckId();
            if(keywordMap.get(rankCheckId) == null){
                List<CLRankingDetailEntity> detailEntityList = new ArrayList<>();
                detailEntityList.add(detailEntity);
                keywordMap.put(detailEntity.getKeywordRankcheckId(), detailEntityList);
            }else {
                List<CLRankingDetailEntity> detailEntityList = keywordMap.get(rankCheckId);
                detailEntityList.add(detailEntity);
                keywordMap.put(detailEntity.getKeywordRankcheckId(), detailEntityList);
            }
        }

        for(Long rankCheckId : keywordMap.keySet()){
            List<CLRankingDetailEntity> detailList = keywordMap.get(rankCheckId);

            RankingForecastEntity rankingForecastEntity = new RankingForecastEntity();
            rankingForecastEntity.setOwnDomainId(baseForecastEntity.getOwnDomainId());
            rankingForecastEntity.setEngineId(baseForecastEntity.getEngineId());
            rankingForecastEntity.setLanguageId(baseForecastEntity.getLanguageId());
            rankingForecastEntity.setDevice(baseForecastEntity.getDevice());
            rankingForecastEntity.setLocationId(baseForecastEntity.getLocationId());
            rankingForecastEntity.setKeywordName(detailList.get(0).getKeywordName());
            rankingForecastEntity.setKeywordRankcheckId(rankCheckId.intValue());
            rankingForecastEntity.setSign(baseForecastEntity.getSign());
            rankingForecastEntity.setVersioning(baseForecastEntity.getVersioning());
            rankingForecastEntity.setRankingDate(rankDate);

            String baseUrls = null;
            String compareUrls = null;

//            log.info("===detailList size：" + detailList.size());
            for(CLRankingDetailEntity detail : detailList){

                List<String> tmpUrls = new ArrayList<>();
                String url = detail.getUrl();
                String domain = FormatUtils.getDomainByUrl(url, false);
                if (StringUtils.containsIgnoreCase(domain, ".google.")
                        || StringUtils.containsIgnoreCase(domain, ".bing.")
                        || StringUtils.containsIgnoreCase(domain, ".yahoo.")
                        || StringUtils.containsIgnoreCase(domain, ".baidu.")
                        || StringUtils.containsIgnoreCase(domain, ".yandex.")
                        || StringUtils.containsIgnoreCase(domain, ".naver.")) {
                    tmpUrls.add(domain);
                } else {
                    tmpUrls.add(url);
                }

                String rankingDate = detail.getRankingDate();
                if (StringUtils.equalsIgnoreCase(rankingDate, baseDateString)) {
                    baseUrls = StringUtils.join(tmpUrls, " ");
                } else {
                    compareUrls = StringUtils.join(tmpUrls, " ");
                }

            }

            if(baseUrls == null || compareUrls == null) {
                rankingForecastEntity.setScore(-1.0);
                log.error("Keyword : "+ rankCheckId +" url is null, baseUrls:"+baseUrls+" compareUrls :"+compareUrls);
            } else {
                rankingForecastEntity.setScore(jaccard.similarity(baseUrls, compareUrls));
            }
            forecastEntities.add(rankingForecastEntity);

            if(forecastEntities.size() >= 1000){
                try {
                    log.info("Insert1 Size :"+forecastEntities.size());
                    rankingForecastDao.inserBatch(forecastEntities);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                forecastEntities.clear();
            }

        }

        if(CollectionUtils.isNotEmpty(forecastEntities)){
            try {
                log.info("Insert2 Size :"+forecastEntities.size());
                rankingForecastDao.inserBatch(forecastEntities);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }


    }

}
