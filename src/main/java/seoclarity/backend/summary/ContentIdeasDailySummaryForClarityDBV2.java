package seoclarity.backend.summary;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.time.DateUtils;

import seoclarity.backend.dao.clickhouse.contentidea.ContentIdeaImpl;
import seoclarity.backend.dao.clickhouse.contentidea.ContentIdeaPreSummaryDao;
import seoclarity.backend.dao.clickhouse.contentidea.ContentIdeaPreSummaryServer2Dao;
import seoclarity.backend.dao.clickhouse.contentidea.ContentIdeaPreSummaryServer3Dao;
import seoclarity.backend.dao.clickhouse.contentidea.ContentIdeaPreSummaryServer4Dao;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.ContentIdeasDailySummaryForClarityDBV2" -Dexec.args=""
public class ContentIdeasDailySummaryForClarityDBV2 {
	
	private ContentIdeaPreSummaryDao contentIdeaPreSummaryDao;
	private ContentIdeaPreSummaryServer2Dao contentIdeaPreSummaryServer2Dao;
	private ContentIdeaPreSummaryServer3Dao contentIdeaPreSummaryServer3Dao;
	private ContentIdeaPreSummaryServer4Dao contentIdeaPreSummaryServer4Dao;
	
	public ContentIdeasDailySummaryForClarityDBV2(){
		contentIdeaPreSummaryDao = SpringBeanFactory.getBean("contentIdeaPreSummaryDao");
		contentIdeaPreSummaryServer2Dao = SpringBeanFactory.getBean("contentIdeaPreSummaryServer2Dao");
		contentIdeaPreSummaryServer3Dao = SpringBeanFactory.getBean("contentIdeaPreSummaryServer3Dao");
		contentIdeaPreSummaryServer4Dao = SpringBeanFactory.getBean("contentIdeaPreSummaryServer4Dao");
	}
	
	
	private static String DEFAULT_VERSION = "9";
	private static String PROCESS_VERSION = DEFAULT_VERSION;
	
	private static final String FINAL_TABLE_NAME = "actonia_content_idea_summary_template";
	private static final String TMP_TABLE_NAME = "actonia_content_idea_summary_" + FormatUtils.formatDate(new Date(), "yyyyMMdd");
	private static final String HOURLY_TMP_LOCAL_TABLE_NAME = "local_actonia_content_idea_summary_" + FormatUtils.formatDate(new Date(), "yyyyMMdd");
	private static final String LOCAL_TABLE_NAME_BACKUP = "local_actonia_content_idea_summary_bk" + FormatUtils.formatDate(new Date(), "yyyyMMdd");
	private static final String LOCAL_TABLE_NAME = "local_actonia_content_idea_summary";
	
	//claritydb_upload_log id -- mdb010
//	private static Integer logId;
	


	public static void main(String[] args) {
		ContentIdeasDailySummaryForClarityDBV2 contentIdeasDailySummaryForClarityDB = new ContentIdeasDailySummaryForClarityDBV2();
		contentIdeasDailySummaryForClarityDB.process();
	}
	
	
//	private boolean checkIsProcessingUpload(){
//		
//		List<ClarityDBUploadLogEntity> list = clarityDBUploadLogDAO.getProcessingRecords(ClarityDBUploadLogEntity.UPLOAD_TYPE_INTERNAL_LINK_SUMMARY);
//		if (!CollectionUtils.isEmpty(list)) {
//			return true;
//		}
//		return false;
//	}
	
	private static List<ContentIdeaImpl> SERVER_LIST = new ArrayList<>();
	
	private static String START_DATE = FormatUtils.formatDate(DateUtils.addMonths(new Date(), -12), "yyyy-MM-dd");
	private static String END_DATE = FormatUtils.formatDate(new Date(), "yyyy-MM-dd");
	
	private static Integer pageSize = 200;
	
	private void process() {
		
		SERVER_LIST.add(contentIdeaPreSummaryDao);
		SERVER_LIST.add(contentIdeaPreSummaryServer2Dao);
		SERVER_LIST.add(contentIdeaPreSummaryServer3Dao);
		SERVER_LIST.add(contentIdeaPreSummaryServer4Dao);
		
		// create dis table
		System.out.println(" Create dis summary table on server !!!");
		contentIdeaPreSummaryDao.createInsertDistributeTable(TMP_TABLE_NAME, FINAL_TABLE_NAME, HOURLY_TMP_LOCAL_TABLE_NAME);
		
		// summary
		try {
			for(int i = 0; i < pageSize; i ++) {
				int num = 0;
				System.out.println("==== processing on " + i + " of " + pageSize);
				while (true) {
					try {
						num ++;
						long a = System.currentTimeMillis();
						contentIdeaPreSummaryDao.summaryV2(TMP_TABLE_NAME, i, pageSize, START_DATE, END_DATE);
						long b = System.currentTimeMillis();
						System.out.println(" time:" + (b - a) * 1.0 / 1000 + "s");

						break;
					} catch (Exception e) {
						// TODO: handle exception
						e.printStackTrace();
						
						try {
							Thread.sleep(2 * 60 * 1000);
						} catch (InterruptedException e2) {
							// TODO: handle exception
							e2.printStackTrace();
						}
						
						System.out.println("!!! backprocess " + num + " time!");
					}
					
				}
				
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			return ;
		}
		
		
		/**
		 * 1. check if new local table already exist
		 * 1. create new local table daily
		 * 2. create new dis table daily
		 * 3. summary data daily
		 * 4. drop old local table and rename new local table
		 * 5. drop new dis table
		 */
		
		for(ContentIdeaImpl contentIdeaImpl : SERVER_LIST) {
			
			System.out.println("Rename local table to backup table : " + contentIdeaImpl.getClass());
			System.out.println(contentIdeaImpl.renameTable(LOCAL_TABLE_NAME_BACKUP, LOCAL_TABLE_NAME));
			
			System.out.println("Rename tmp table to local table : " + contentIdeaImpl.getClass());
			System.out.println(contentIdeaImpl.renameTable(LOCAL_TABLE_NAME, HOURLY_TMP_LOCAL_TABLE_NAME));
			
		}
		
//		for(ContentIdeaImpl contentIdeaImpl : SERVER_LIST) {
//			
//			try {
//				contentIdeaImpl.dropTable(LOCAL_TABLE_NAME_BACKUP);
//			} catch (Exception e) {
//				e.printStackTrace();
//			}
//			
//		}
		
		try {
			contentIdeaPreSummaryDao.dropTable(TMP_TABLE_NAME);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	
}
