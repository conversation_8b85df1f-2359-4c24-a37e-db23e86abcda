package seoclarity.backend.summary;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.util.CollectionUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.ClarityDBUploadLogDAO;
import seoclarity.backend.dao.actonia.CrawlRequestLogDAO;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.OwnDomainSettingEntityDAO;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkImpl;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer1Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer2Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer3Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer4Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer5Dao;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSiteCrawlDoc1Dao;
import seoclarity.backend.dao.mdbkeywordsuggest.InternalLinkPageRankInstanceEntityDAO;
import seoclarity.backend.entity.NodeStatusVO;
import seoclarity.backend.entity.actonia.ClarityDBUploadLogEntity;
import seoclarity.backend.entity.actonia.CrawlRequestLog;
import seoclarity.backend.entity.actonia.OwnDomainSettingEntity;
import seoclarity.backend.entity.clickhouse.internallink.ProcessListVO;
import seoclarity.backend.utils.ClarityDBAPIUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.HttpRequestUtils;
import seoclarity.backend.utils.SpringBeanFactory;
// mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.InternalLinDailySummaryV2ClarityDB" -Dexec.args="true"
// mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.InternalLinDailySummaryV2ClarityDB" -Dexec.args="true true 9985713"
public class InternalLinDailySummaryV2ClarityDB {
	
	private InternalLinkNewClusterServer1Dao internalLinkNewClusterServer1Dao;
	private InternalLinkNewClusterServer2Dao internalLinkNewClusterServer2Dao;
	private InternalLinkNewClusterServer3Dao internalLinkNewClusterServer3Dao;
	private InternalLinkNewClusterServer4Dao internalLinkNewClusterServer4Dao;
	private InternalLinkNewClusterServer5Dao internalLinkNewClusterServer5Dao;
	
	private InternalLinkPageRankInstanceEntityDAO internalLinkPageRankInstanceEntityDAO;
	
	private CrawlRequestLogDAO crawlRequestLogDAO;
	private DisSiteCrawlDoc1Dao disSiteCrawlDoc1Dao;
	private ClarityDBUploadLogDAO clarityDBUploadLogDAO;
	private OwnDomainEntityDAO ownDomainEntityDAO;
	private OwnDomainSettingEntityDAO  ownDomainSettingEntityDAO;
	
//	private CrawlRequestModifylog crawlRequestModifylog;
	
	private List<ProcessListVO> finalProssList = new ArrayList<>();
	private List<ProcessListVO> tmpProcessList = new ArrayList<>();
	
	private Gson gson = new Gson();
	
	private final static Integer DEFAULT_VERSION_FOR_INTERNAL_LINK = 0;
//	private static Integer version = DEFAULT_VERSION_FOR_INTERNAL_LINK;
//	private static Integer sepCrawlLogId;
//	private static Integer sepDomainId;
	
//	private static boolean reprocessFlag = false;
	
	private static final String databaseName = "actonia_internal_link";
	
	//RE-create this distribute table for api to query for temp data only
	
	//claritydb_upload_log id -- mdb010
	private static Integer logId;
	private static List<Integer> crawlFinishedList = new ArrayList<>();
	
	public static List<Integer> ignoreCrawlIdList = new ArrayList<>();
	static {
		
		ignoreCrawlIdList.add(10101010);
		ignoreCrawlIdList.add(10101011);
		ignoreCrawlIdList.add(10101013);
		ignoreCrawlIdList.add(10101016);
		ignoreCrawlIdList.add(10101017);
		ignoreCrawlIdList.add(10101018);
		ignoreCrawlIdList.add(10101019);
		ignoreCrawlIdList.add(10101020);
		ignoreCrawlIdList.add(10101091);
		ignoreCrawlIdList.add(11111157);
		ignoreCrawlIdList.add(11122233);
		ignoreCrawlIdList.add(99923100);
		ignoreCrawlIdList.add(99923102);
		ignoreCrawlIdList.add(99923103);
		ignoreCrawlIdList.add(101010118);
		ignoreCrawlIdList.add(101013121);
		ignoreCrawlIdList.add(101013130);
		ignoreCrawlIdList.add(101023121);
		ignoreCrawlIdList.add(555445565);
		ignoreCrawlIdList.add(555445566);
		ignoreCrawlIdList.add(555445568);
		ignoreCrawlIdList.add(555577777);
		ignoreCrawlIdList.add(991346011);
		ignoreCrawlIdList.add(991758011);
		ignoreCrawlIdList.add(991758012);
		ignoreCrawlIdList.add(991758611);
		ignoreCrawlIdList.add(1000263483);
		ignoreCrawlIdList.add(1234134657);
		ignoreCrawlIdList.add(10002932);
		ignoreCrawlIdList.add(10002722);
		
		//https://www.wrike.com/open.htm?id=775219263
		ignoreCrawlIdList.add(9917580);
		ignoreCrawlIdList.add(9917586);
		
		crawlFinishedList.add(9940736);
		crawlFinishedList.add(9942184);
		crawlFinishedList.add(9942586);
		crawlFinishedList.add(9942643);
		crawlFinishedList.add(9944305);
		crawlFinishedList.add(9944365);
		crawlFinishedList.add(9944455);
		crawlFinishedList.add(9944539);
		crawlFinishedList.add(9944540);
		crawlFinishedList.add(9944541);
		crawlFinishedList.add(9944970);
		crawlFinishedList.add(9945054);
		crawlFinishedList.add(9945135);
		crawlFinishedList.add(9945173);
		crawlFinishedList.add(9945186);
		crawlFinishedList.add(9945229);
		crawlFinishedList.add(9945238);
		crawlFinishedList.add(9945308);
		crawlFinishedList.add(9945313);
		crawlFinishedList.add(9945335);
		crawlFinishedList.add(9945336);
		crawlFinishedList.add(9945357);
		crawlFinishedList.add(9945440);
		crawlFinishedList.add(9945606);
		crawlFinishedList.add(9945960);
		crawlFinishedList.add(9945968);
		crawlFinishedList.add(9946112);
		crawlFinishedList.add(9946756);
		crawlFinishedList.add(9946768);
		crawlFinishedList.add(9946872);
		crawlFinishedList.add(9947783);
		crawlFinishedList.add(9948527);
		crawlFinishedList.add(9948740);
		crawlFinishedList.add(9949029);
		crawlFinishedList.add(9949030);
		crawlFinishedList.add(9949054);
		crawlFinishedList.add(9953276);
		crawlFinishedList.add(9953301);
		crawlFinishedList.add(9953335);
		crawlFinishedList.add(9953497);
		crawlFinishedList.add(9954256);
		crawlFinishedList.add(9954704);
		crawlFinishedList.add(9954742);
		crawlFinishedList.add(9955598);
		crawlFinishedList.add(9955646);
		crawlFinishedList.add(9955647);
		crawlFinishedList.add(9955649);
		crawlFinishedList.add(9955705);
		crawlFinishedList.add(9956660);
		crawlFinishedList.add(9957941);
		crawlFinishedList.add(9958066);
		crawlFinishedList.add(9958719);
		crawlFinishedList.add(9959205);
		crawlFinishedList.add(9959206);
		crawlFinishedList.add(9959494);
		crawlFinishedList.add(9959592);
		crawlFinishedList.add(9959610);
		crawlFinishedList.add(9959759);
		crawlFinishedList.add(9959762);
		crawlFinishedList.add(9959930);
		crawlFinishedList.add(9959938);
		crawlFinishedList.add(9960182);
		crawlFinishedList.add(9960184);
		crawlFinishedList.add(9960666);
		crawlFinishedList.add(9960670);
		crawlFinishedList.add(9961322);
		crawlFinishedList.add(9964790);
		crawlFinishedList.add(9961173);
	}
	
	public InternalLinDailySummaryV2ClarityDB(){
		internalLinkNewClusterServer1Dao = SpringBeanFactory.getBean("internalLinkNewClusterServer1Dao");
		internalLinkNewClusterServer2Dao = SpringBeanFactory.getBean("internalLinkNewClusterServer2Dao");
		internalLinkNewClusterServer3Dao = SpringBeanFactory.getBean("internalLinkNewClusterServer3Dao");
		internalLinkNewClusterServer4Dao = SpringBeanFactory.getBean("internalLinkNewClusterServer4Dao");
		internalLinkNewClusterServer5Dao = SpringBeanFactory.getBean("internalLinkNewClusterServer5Dao");
		internalLinkPageRankInstanceEntityDAO = SpringBeanFactory.getBean("internalLinkPageRankInstanceEntityDAO");
		crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
//		crawlRequestModifylog = SpringBeanFactory.getBean("crawlRequestModifylog");
		clarityDBUploadLogDAO = SpringBeanFactory.getBean("clarityDBUploadLogDAO");
		disSiteCrawlDoc1Dao = SpringBeanFactory.getBean("disSiteCrawlDoc1Dao");
		ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
		ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
	}
	
	private static boolean processBigCrawl = false;
	
	private static boolean onlyRunForFinalHourly = false;
	
	private static Integer reprocessCrawlLogId = 0;
	private static boolean isBackprocess = false;

	public static void main(String[] args) {
		
		if (args != null && args.length >= 1) {
			onlyRunForFinalHourly = BooleanUtils.toBoolean(args[0]);
			System.out.println("====== start process on onlyRunForFinalHourly : " + onlyRunForFinalHourly);
			
			if (args.length >= 2) {
				processBigCrawl = BooleanUtils.toBoolean(args[1]);
			}
			
			// only suppport in hourly
			if (args.length >= 3) {
				reprocessCrawlLogId = NumberUtils.toInt(args[2]);
				isBackprocess = true;
				
				System.out.println(" reprocessCrawlLogId : " + reprocessCrawlLogId + ", isBackprocess:" + isBackprocess);
				if(reprocessCrawlLogId == 0) {
					System.out.println("Incorrect reprcoess crawl ID!");
					return;
				}
			}
			
		}
		
		InternalLinDailySummaryV2ClarityDB internalLinkDailySummaryForClarityDB = new InternalLinDailySummaryV2ClarityDB();
		
		internalLinkDailySummaryForClarityDB.init();
		
		if(onlyRunForFinalHourly) {
			internalLinkDailySummaryForClarityDB.processHourly();
		} else {
			internalLinkDailySummaryForClarityDB.process();
		}
	}
	
	private void init() {
		ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();
		
		clarityDBUploadLogEntity.setTmpTableName(FINAL_TABLE_NAME);
		clarityDBUploadLogEntity.setDatabaseName(databaseName);
		
		clarityDBUploadLogEntity.setFinalTableName(" ");
		clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_NEW);
		clarityDBUploadLogEntity.setUploadType(ClarityDBUploadLogEntity.UPLOAD_TYPE_INTERNAL_LINK_SUMMARY_NEW_CLUSTER);
		try {
			clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
		} catch (UnknownHostException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		
		clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
		clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_PROCESSING);
		
		logId = clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
	}
	
	private static final Integer CUT_OVER_CRAWL_ID = 9937973;
//	private static final Long CUT_OVER_CRAWL_DATE_LONG = 1677628800L;//03/01/2023
	private static final Long CUT_OVER_CRAWL_DATE_LONG = 1681344000L;//04/13/2023
	
	private static String STR_NODE_STATUS_API = ClarityDBAPIUtils.API_ENDPOINT_IBM_INTRANET + "seoClarity/siteClarity/getCrawlerStats?accessToken=c09yxv13-opr3-d745-9734-8pu48420nj67&crawl_request_log_id_i=%d&clusterCrawl=true&request=backend";
//	private static String STR_NODE_STATUS_API = "https://api-gw.seoclarity.net/clarityapi/getCrawlerStats?crawl_request_log_id_i=%d&clusterCrawl=true";
	private static float percentage = 0.999f;

	
	private void process() {
		
		long startTime = System.currentTimeMillis();
		
		SERVER_LIST.add(internalLinkNewClusterServer1Dao);
		SERVER_LIST.add(internalLinkNewClusterServer2Dao);
		SERVER_LIST.add(internalLinkNewClusterServer3Dao);
		SERVER_LIST.add(internalLinkNewClusterServer4Dao);
		SERVER_LIST.add(internalLinkNewClusterServer5Dao);
		
		//step1. check if current table exist in db
		for(InternalLinkImpl internalLinkDao : SERVER_LIST) {
			String tableName = "";
			try {
				tableName = internalLinkDao.getTableList(HOURLY_TMP_LOCAL_TABLE_NAME);
			} catch (Exception e) {
				System.out.println(" Table is not found: " + HOURLY_TMP_LOCAL_TABLE_NAME);
			}
			
			if (StringUtils.isNotBlank(tableName)) {
				System.out.println(" Table :" + HOURLY_TMP_LOCAL_TABLE_NAME + " already exist in server : " + internalLinkDao);
				return;
			} else {
				System.out.println("Create table :" + HOURLY_TMP_LOCAL_TABLE_NAME + ", on server : " + internalLinkDao);
				try {
					System.out.println(internalLinkDao.createLocalTable(HOURLY_TMP_LOCAL_TABLE_NAME, templateTableName));
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
		}
		
		//step2. load summary data 
		try {
			
			List<CrawlRequestLog> needProcessCrawler = crawlRequestLogDAO.getCrawlNeedSummary();
			
//			List<ProcessListVO> needProcessCrawler = internalLinkNewClusterServer4Dao.getSummaryListV2(CUT_OVER_CRAWL_ID, ignoreCrawlIdList, CUT_OVER_CRAWL_DATE_LONG);
			
//			List<ProcessListVO> needProcessCrawler = new ArrayList<>();
//			ProcessListVO ProcessListVO = new ProcessListVO();
//			ProcessListVO.setCrawl_request_log_id_i(9947130);
//			ProcessListVO.setDomain_id_i(11217);
//			ProcessListVO.setCnt(4772115);
//			ProcessListVO.setToday("2023-05-29");
//			needProcessCrawler.add(ProcessListVO);
			
			System.out.println("needProcessCrawler size : " + (needProcessCrawler!= null ? needProcessCrawler.size() : 0));
			
			for(CrawlRequestLog crawlRequestLog : needProcessCrawler) {
				
				if (crawlRequestLog == null) {
					System.out.println("=== SKIP Crawl not found!");
					continue;
				}
				
				ProcessListVO vo = internalLinkNewClusterServer4Dao.getProcessVOById(crawlRequestLog.getId());
				
				if (!processBigCrawl && vo.getCnt() >= LIMITATION_3E) {
					System.out.println("====== skip crawl over 1E, ID:" + vo.getCrawl_request_log_id_i());
					continue;
				}
				
				if (processBigCrawl && vo.getCnt() < LIMITATION_3E) {
					System.out.println("====== skip crawl less than 1E, ID:" + vo.getCrawl_request_log_id_i());
					continue;
				}
				
				Integer ownDomainId = vo.getDomain_id_i();
				Integer crawlRequestId = vo.getCrawl_request_log_id_i();
				
				System.out.println("===crawlRequestId:" + crawlRequestId + ", contains:" + ignoreCrawlIdList.contains(crawlRequestId));
				if (ignoreCrawlIdList.contains(crawlRequestId)) {
					System.out.println("==== Skip for crawl in the ignore list, cid:" + crawlRequestId);
					continue;
				}
				
				if(crawlFinishedList.contains(crawlRequestLog.getId())) {
					finalProssList.add(vo);
				} else {
					String webServiceUrl = String.format(STR_NODE_STATUS_API, crawlRequestId);
					
					//response: {"totalNodes":13,"completedNodes":13,"completed":true}
					String response = HttpRequestUtils.queryWebServiceFunctionByMethod(webServiceUrl, "GET", null);
					System.out.println("response:" + response);
					
					System.out.println("CrawlRequestDate:" + crawlRequestLog.getCrawlRequestDate() + ", now:" + FormatUtils.formatDateToYyyyMmDd(new Date()));
					System.out.println(DateUtils.addMonths(FormatUtils.toDate(crawlRequestLog.getCrawlRequestDate() + "", "yyyyMMdd"), 1).before(new Date()));
					if (DateUtils.addMonths(FormatUtils.toDate(crawlRequestLog.getCrawlRequestDate() + "", "yyyyMMdd"), 1).before(new Date())) {
						System.out.println("Crawl not updated in one Month, summary into final table!");
						finalProssList.add(vo);
						continue;
					}
					
					NodeStatusVO nodeStatusVO = gson.fromJson(response, NodeStatusVO.class);
					if (nodeStatusVO != null && nodeStatusVO.getCompleted() != null && nodeStatusVO.getCompleted()) {
						
						Long count = disSiteCrawlDoc1Dao.getCountOfSiteHealthPageLinkV2(ownDomainId, crawlRequestId);
						
						Long totalCount = internalLinkNewClusterServer4Dao.getUniqueDocumentCountByCrawlRequestIdAndDomainId(ownDomainId, crawlRequestId);
						// if we have documents more than percentage of total count, then it's finished
						if (count != null && 
								totalCount != null && 
								totalCount >= count * percentage) {
							System.out.println("==== crawler is finished, sumamry in final table : " + crawlRequestId + ", ttSH:" + count + ", ttITL:" + totalCount);
							finalProssList.add(vo);
							continue;
						}
						
						System.out.println("CrawlRequestDate:" + crawlRequestLog.getCrawlRequestDate() + ", now:" + FormatUtils.formatDateToYyyyMmDd(new Date()));
						System.out.println(DateUtils.addMonths(FormatUtils.toDate(crawlRequestLog.getCrawlRequestDate() + "", "yyyyMMdd"), 1).before(new Date()));
						if (DateUtils.addMonths(FormatUtils.toDate(crawlRequestLog.getCrawlRequestDate() + "", "yyyyMMdd"), 1).before(new Date())) {
							System.out.println("Crawl not updated in one Month, summary into final table!");
							finalProssList.add(vo);
						} else {
							System.out.println("==== crawler is not finished, sumamry in tmp table");
							tmpProcessList.add(vo);
						}
						
					} else {
						System.out.println("==== crawler is not finished, sumamry in tmp table");
						tmpProcessList.add(vo);
					}
				}
				
			}
			
			if (CollectionUtils.isEmpty(finalProssList) && CollectionUtils.isEmpty(tmpProcessList)) {
//			if (CollectionUtils.isEmpty(finalProssList)) {
				System.out.println("No more crawler need to process today!!!");
			} else {
				
				
//				System.out.println("==== start summary, final process list : " + finalProssList.size() + ", tmp process list :" + tmpProcessList.size());
				System.out.println("==== start summary, final process list : " + finalProssList.size());
				System.out.println("=== final list:" + gson.toJson(finalProssList));
//				System.out.println("=== tmp list:" + gson.toJson(tmpProcessList));
				summary();
				
				try {
					crawlRequestLogDAO.updateAdditionalStatusV2(finalProssList);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			
			if (CollectionUtils.isEmpty(tmpProcessList)) {
				//step3. delete tmp table and rename the new tmp table for this hour
				for(InternalLinkImpl internalLinkDao : SERVER_LIST) {
					
					//drop the local table for all table which prefix is HOURLY_LOCAL_TABLE_NAME_PREFIX(local_internal_link_sampled_view_2%)
					try {
						List<String> tableNames = internalLinkDao.getTableListV2(HOURLY_LOCAL_TABLE_NAME_PREFIX);
						
						for(String table : tableNames) {
							if (StringUtils.isNotBlank(table)) {
								try {
									internalLinkDao.dropTable(table);
								} catch (Exception e) {
									// TODO Auto-generated catch block
									e.printStackTrace();
								}
							}
						}
					} catch (Exception e) {
						System.out.println("No local table is founded, prefix:" + HOURLY_LOCAL_TABLE_NAME_PREFIX);
					}
					
					// rename from new_ to local_ table
					System.out.println("Rename tmp table to local table : ");
					try {
						System.out.println(internalLinkDao.renameTable(HOURLY_LOCAL_TABLE_NAME, HOURLY_TMP_LOCAL_TABLE_NAME));
					} catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
				
				String tableName = internalLinkNewClusterServer4Dao.getTableList(TMP_TABLE_NAME_FOR_QUERY);
				if (StringUtils.isNotBlank(tableName)) {
					System.out.println("!!!! find " + TMP_TABLE_NAME_FOR_QUERY + ", waiting drop, recreate and point to the hourly local table");
					try {
						internalLinkNewClusterServer4Dao.dropTable(TMP_TABLE_NAME_FOR_QUERY);
					} catch (Exception e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
				
				System.out.println("!!! creating tmp dis table for query : TMP_TABLE_NAME_FOR_QUERY ---------------------   ");
				
				try {
					internalLinkNewClusterServer4Dao.createInsertDistributeTable(TMP_TABLE_NAME_FOR_QUERY, FINAL_TABLE_NAME, HOURLY_LOCAL_TABLE_NAME);
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
					return;
				}
			}
			long endTime = System.currentTimeMillis();
			int elapsedSeconds = (int) (endTime - startTime);
			
			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, 0, elapsedSeconds, logId);
			
		} catch (Exception e) {
			e.printStackTrace();
			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
		}
		
		
	}
	
	// result divide 100
	private static final Integer LIMITATION_3M = 30000;
	private static final Integer LIMITATION_20M = 200000;
	private static final Integer LIMITATION_3E = 3000000;
	
	private static final String FINAL_TABLE_NAME = "dis_internal_link_sampled_view_final";
	//RE-create this distribute table for api to query for temp data only
	private static final String TMP_TABLE_NAME_FOR_QUERY = "dis_internal_link_sampled_view_temp";
	private static final String TMP_DIS_TABLE_NAME = "dis_internal_link_sampled_intermediate_view";
	private static final String templateTableName = "local_internal_link_sampled_template";
	//20190624_02
	private static final String HOURLY_TMP_LOCAL_TABLE_NAME = "new_local_internal_link_sampled_view_" + FormatUtils.formatDate(new Date(), "yyyyMMdd_HH");
	private static final String HOURLY_LOCAL_TABLE_NAME = "local_internal_link_sampled_view_" + FormatUtils.formatDate(new Date(), "yyyyMMdd_HH");
	private static final String HOURLY_LOCAL_TABLE_NAME_PREFIX = "local_internal_link_sampled_view_2%";
	/*
	 * daily summary for each crawl request id
	 */
	
	private static final String EXPEDIA_COMPANY_PREFIX = "Expedia";
	
	private static List<Integer> failedList = new ArrayList<>();
	private void summary() {
		// -------------------------------  final  -----------------------------------
		
		System.out.println("@@@ processing to final table ");
		for (ProcessListVO vo : finalProssList) {
			Date startDate = new Date();
			try {
				
				if (processBigCrawl) {
					summaryBigCrawl(vo);
				} else {
					int partitionNum = 0;
					if (vo.getCnt() >= LIMITATION_3M) {
						partitionNum = (vo.getCnt() / LIMITATION_3M) + 1;
					}
					
					System.out.println("============== processing on crawlRequestId:" + vo.getCrawl_request_log_id_i() + ", ownDomainId:" + vo.getDomain_id_i());
					
					if (partitionNum == 0) {
						internalLinkNewClusterServer4Dao.summary(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), FINAL_TABLE_NAME);
					} else {
						for(int i = 0; i < partitionNum; i ++) {
							System.out.println("Summary :" + (i + 1) + " of " + partitionNum);
							internalLinkNewClusterServer4Dao.summaryByHash(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), 
									FINAL_TABLE_NAME, partitionNum, i);
						}
					}
				}
				
				try {
					
					OwnDomainSettingEntity ownDomainSettingEntity = ownDomainSettingEntityDAO.getSettingByDomainId(vo.getDomain_id_i());
					if (ownDomainSettingEntity != null && StringUtils.contains(ownDomainSettingEntity.getCompanyName(), EXPEDIA_COMPANY_PREFIX)) {
						internalLinkPageRankInstanceEntityDAO.insert(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), vo.getCnt(), "expedia");
					}
					
				} catch (Exception e) {
					e.printStackTrace();
				}
				
				Date endDate = new Date();
				try {
					//Integer crawlId, Integer internalLinkCnt, Integer siteHealthCnt
					crawlRequestLogDAO.updateInternalLinkProcessDate(vo.getCrawl_request_log_id_i(), startDate, endDate, "");
				} catch (Exception e) {
					e.printStackTrace();
				}
		
			} catch (Exception e) {
				e.printStackTrace();
				failedList.add(vo.getCrawl_request_log_id_i());
				Date endDate = new Date();
				try {
					//Integer crawlId, Integer internalLinkCnt, Integer siteHealthCnt
					crawlRequestLogDAO.updateInternalLinkProcessDate(vo.getCrawl_request_log_id_i(), startDate, endDate, StringUtils.substring(e.getMessage(), 0, 256));
				} catch (Exception e2) {
					e2.printStackTrace();
				}
			}
			
			try {
				crawlRequestLogDAO.updateAdditionalStatusV3(vo.getCrawl_request_log_id_i());
			} catch (Exception e) {
				e.printStackTrace();
			}
			
		}
		// -------------------------------  final  -----------------------------------
		
		
		if (!onlyRunForFinalHourly) {
			// -------------------------------  tmp  -----------------------------------
			
			System.out.println("@@@ processing to tmp table ");
			System.out.println("===== create tmp table if not exist");
			
			String tableName = internalLinkNewClusterServer4Dao.getTableList(TMP_DIS_TABLE_NAME);
			if (StringUtils.isNotBlank(tableName)) {
				System.out.println("!!!! find the table, waiting drop, recreate and point to the daily local table");
				try {
					internalLinkNewClusterServer4Dao.dropTable(TMP_DIS_TABLE_NAME);
				} catch (Exception e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			
			System.out.println("!!! creating tmp dis table ---------------------   ");
			
			try {
				internalLinkNewClusterServer4Dao.createInsertDistributeTable(TMP_DIS_TABLE_NAME, FINAL_TABLE_NAME, HOURLY_TMP_LOCAL_TABLE_NAME);
			} catch (Exception e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
				return;
			}
			
			
			for (ProcessListVO vo : tmpProcessList) {
				
				if (vo.getCnt() >= LIMITATION_3E) {
//					summaryBigCrawl(vo);
					continue;
				} 

				int partitionNum = 0;
				if (vo.getCnt() >= LIMITATION_3M) {
					partitionNum = (vo.getCnt() / LIMITATION_3M) + 1;
				}
				
				System.out.println("============== processing on crawlRequestId:" + vo.getCrawl_request_log_id_i() + ", ownDomainId:" + vo.getDomain_id_i());
				try {
					if (partitionNum == 0) {
						internalLinkNewClusterServer4Dao.summary(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), TMP_DIS_TABLE_NAME);
					} else {
						for(int i = 0; i < partitionNum; i ++) {
							System.out.println("Summary :" + (i + 1) + " of " + partitionNum);
							internalLinkNewClusterServer4Dao.summaryByHash(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), 
									TMP_DIS_TABLE_NAME, partitionNum, i);
						}
					}
					
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			// -------------------------------  tmp  -----------------------------------
		}
		
	}
	
	
	private void summaryBigCrawl(ProcessListVO vo) {
		
		int partitionNum = 0;
		if (vo.getCnt() >= LIMITATION_20M) {
			partitionNum = (vo.getCnt() / LIMITATION_20M) + 1;
		}
		
		//step 1 summary to dis_summary_outbound_detail
		System.out.println(" ============= summary dis_summary_outbound_detail ");
		for(int i = 0; i < partitionNum; i ++) {
			System.out.println("Summary :" + (i + 1) + " of " + partitionNum);
			
			internalLinkNewClusterServer4Dao.summaryBigDocDetail(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), FINAL_TABLE_NAME, partitionNum, i);
		}
		
		//step 2 summary to dis_summary_detail
		System.out.println(" ============== summary dis_summary_detail ");
		for(int i = 0; i < partitionNum; i ++) {
			System.out.println("Summary :" + (i + 1) + " of " + partitionNum);
			internalLinkNewClusterServer4Dao.summaryBigDocOutBoundDetail(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), FINAL_TABLE_NAME, partitionNum, i);
		}
		
		//step 3 summary to dis_internal_link_sampled_view_final
		System.out.println(" ============== summary dis_internal_link_sampled_view_final ");
		for(int i = 0; i < partitionNum; i ++) {
			System.out.println("Summary :" + (i + 1) + " of " + partitionNum);
			internalLinkNewClusterServer4Dao.summaryBigDocIntoFinalTable(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), FINAL_TABLE_NAME, partitionNum, i);
		}
		
	}
	
	private static List<InternalLinkImpl> SERVER_LIST = new ArrayList<>();
	
	private void processHourly() {
		
		SERVER_LIST.add(internalLinkNewClusterServer1Dao);
		SERVER_LIST.add(internalLinkNewClusterServer2Dao);
		SERVER_LIST.add(internalLinkNewClusterServer3Dao);
		SERVER_LIST.add(internalLinkNewClusterServer4Dao);
		SERVER_LIST.add(internalLinkNewClusterServer5Dao);
		
		long startTime = System.currentTimeMillis();
		
		//step2. load summary data 
		try {
			if (isBackprocess) {
				Integer cntInFinalTalbe = internalLinkNewClusterServer4Dao.getTotalCount(reprocessCrawlLogId);
				if (cntInFinalTalbe > 0) {
					System.out.println("Data already exist in final table! cnt:" + cntInFinalTalbe);
					return;
				}
			}
			
			List<CrawlRequestLog> needProcessCrawler = new ArrayList<CrawlRequestLog>();
			
			
//			List<ProcessListVO> needProcessCrawler = new ArrayList<ProcessListVO>();
//			if (!isBackprocess) {
//				needProcessCrawler = internalLinkNewClusterServer4Dao.getSummaryListV2(CUT_OVER_CRAWL_ID, ignoreCrawlIdList, CUT_OVER_CRAWL_DATE_LONG);
//				
//			} else {
//				needProcessCrawler = internalLinkNewClusterServer4Dao.getBackprocessListV2(reprocessCrawlLogId);
//			}
			
			if (!isBackprocess) {
				needProcessCrawler = crawlRequestLogDAO.getCrawlNeedSummary();
			} else {
				CrawlRequestLog crawlRequestLog = crawlRequestLogDAO.getById(reprocessCrawlLogId);
				needProcessCrawler.add(crawlRequestLog);
			}
			System.out.println("needProcessCrawler size : " + (needProcessCrawler!= null ? needProcessCrawler.size() : 0));
			
			for(CrawlRequestLog crawlRequestLog : needProcessCrawler) {
				
				if (crawlRequestLog == null) {
					System.out.println("=== SKIP Crawl not found!");
					continue;
				}
				
				ProcessListVO vo = internalLinkNewClusterServer4Dao.getProcessVOById(crawlRequestLog.getId());
				
				if (vo == null) {
					System.out.println("Data not found in internal link table ! ID:" + crawlRequestLog.getId());
					continue;
				}
				
				if (!processBigCrawl && vo.getCnt() >= LIMITATION_3E) {
					System.out.println("====== skip crawl over 3E, ID:" + vo.getCrawl_request_log_id_i());
					continue;
				}
				
				if (processBigCrawl && vo.getCnt() < LIMITATION_3E) {
					System.out.println("====== skip crawl less than 3E, ID:" + vo.getCrawl_request_log_id_i());
					continue;
				}
				Date lastDate = FormatUtils.toDate(vo.getToday(), "yyyy-MM-dd");
				
				Date last1Day = DateUtils.addDays(new Date(), -1);
				
				boolean haveDataInLast1Days = lastDate.after(last1Day);
				
				Integer ownDomainId = vo.getDomain_id_i();
				Integer crawlRequestId = vo.getCrawl_request_log_id_i();
				
				System.out.println("===crawlRequestId:" + crawlRequestId + ", contains:" + ignoreCrawlIdList.contains(crawlRequestId));
				if (ignoreCrawlIdList.contains(crawlRequestId)) {
					System.out.println("==== Skip for crawl in the ignore list, cid:" + crawlRequestId);
					continue;
				}
				
				if (!isBackprocess && crawlRequestLog != null && crawlRequestLog.getId() > 0 && crawlRequestLog.getAdditionalStatusV2() != null
						&& crawlRequestLog.getAdditionalStatusV2() == CrawlRequestLog.INTERNALLINK_DATA_STATUS_COMPLETE) {
					continue;
					
				} else if (crawlRequestLog != null && crawlRequestLog.getId() > 0 && (isBackprocess || crawlRequestLog.getAdditionalStatusV2() == null
						|| crawlRequestLog.getAdditionalStatusV2() != CrawlRequestLog.INTERNALLINK_DATA_STATUS_COMPLETE)) {
					
					if(crawlFinishedList.contains(crawlRequestLog.getId())) {
						finalProssList.add(vo);
					} else {
						
						String webServiceUrl = String.format(STR_NODE_STATUS_API, crawlRequestId);
						
						//response: {"totalNodes":13,"completedNodes":13,"completed":true}
						String response = HttpRequestUtils.queryWebServiceFunctionByMethod(webServiceUrl, "GET", null);
						
						System.out.println("response:" + response);
						
						NodeStatusVO nodeStatusVO = gson.fromJson(response, NodeStatusVO.class);
						if (nodeStatusVO != null && nodeStatusVO.getCompleted() != null && nodeStatusVO.getCompleted()) {
//							InternalLinkNodeStatus nodeStatus = internalLinkNodeStatusDAO.getAggInfoByCrawlRequestId(crawlRequestId);
//							System.out.println("===========================");
//							System.out.println("nodeStatus.getCnt().intValue():" + nodeStatus.getCnt().intValue());
//							System.out.println("nodeStatusVO.getCompletedNodes().intValue():" + nodeStatusVO.getCompletedNodes().intValue());
//							System.out.println(nodeStatusVO.getCompletedNodes().intValue() >= nodeStatus.getCnt().intValue());
//							System.out.println("===========================");
//							
//							if (nodeStatus!= null && nodeStatus.getCnt() != null && nodeStatus.getCnt() > 0
//									&& nodeStatusVO.getCompletedNodes() != null 
//									&& nodeStatus.getCnt().intValue() == nodeStatusVO.getCompletedNodes().intValue()) {
//								Long totalDocumentCnt = nodeStatus.getPageLinkItem();
//								Long totalCount = internalLinkServer1Dao.getUniqueDocumentCountByCrawlRequestIdAndDomainId(ownDomainId, crawlRequestId);
//							
//								// if we have documents more than percentage of total count, then it's finished
//								if (totalDocumentCnt != null && totalCount != null 
//										&& totalDocumentCnt >= totalCount * percentage) {
//									System.out.println("==== crawler is finished, summary in final table");
//									finalProssList.add(vo);
//									continue;
//								} else {
//									System.out.println("==== crawler is not finished, summary in tmp table");
//								}
//							} else {
//								System.out.println("==== not found in node status");
//							}
							
							Long siteHealthUniqueCnt = disSiteCrawlDoc1Dao.getCountOfSiteHealthPageLinkV2(ownDomainId, crawlRequestId);
							Long internalLinkUniqueCnt = internalLinkNewClusterServer4Dao.getUniqueDocumentCountByCrawlRequestIdAndDomainId(ownDomainId, crawlRequestId);
							
//							https://www.wrike.com/open.htm?id=**********
//							if internal link consumer dont have data incoming for last 1 day and DB already have more than 99.99% data, then summary can go
//							if internal link consumer dont have data incoming for last 3 day and DB already have more than 99% data, then summary can go
//							if internal link consumer dont have data incoming for last 5 day, then summary can go and we will log this and send alert email for it.
							
							try {
								//Integer crawlId, Integer internalLinkCnt, Integer siteHealthCnt
								crawlRequestLogDAO.updateCountOfInternalLinkAndSitehealth(crawlRequestId, internalLinkUniqueCnt, siteHealthUniqueCnt);
							} catch (Exception e) {
								e.printStackTrace();
							}

							if (siteHealthUniqueCnt != null && 
									internalLinkUniqueCnt != null && 
									internalLinkUniqueCnt >= siteHealthUniqueCnt * percentage) {
								System.out.println("==== LEVEL0 crawler is finished, sumamry in final table : " + crawlRequestId + ", ttSH:" + siteHealthUniqueCnt + ", ttITL:" + internalLinkUniqueCnt);
								finalProssList.add(vo);
								continue;
							} else if (!haveDataInLast1Days && (siteHealthUniqueCnt != null && 
									internalLinkUniqueCnt != null && 
									internalLinkUniqueCnt >= siteHealthUniqueCnt * percentage)) {
								System.out.println("==== LEVEL1 crawler is finished, sumamry in final table : " + crawlRequestId + ", ttSH:" + siteHealthUniqueCnt + ", ttITL:" + internalLinkUniqueCnt);
								finalProssList.add(vo);
								continue;
							}
//							else if(!haveDataInLast3Days && (count != null && 
//									totalCount != null && 
//									totalCount >= count * percentage_level2)) {
//								System.out.println("==== LEVEL2 crawler is finished, sumamry in final table : " + crawlRequestId + ", ttSH:" + count + ", ttITL:" + totalCount);
//								finalProssList.add(vo);
//								continue;
//							} else if(!haveDataInLast5Days) {
//								System.out.println("==== LEVEL3 crawler is finished, sumamry in final table : " + crawlRequestId + ", ttSH:" + count + ", ttITL:" + totalCount);
//								
//								//TODO add message alert
//								finalProssList.add(vo);
//								continue;
//							}
							 else {
								System.out.println("==== crawler is not finished, ttSH:" + siteHealthUniqueCnt + ", ttITL:" + internalLinkUniqueCnt);
							}
						}
						
					}
					
				} else {
					System.out.println("==== crawl not found, skiped!!! cid:" + crawlRequestLog.getId());
				}
				
			}
			
			if (CollectionUtils.isEmpty(finalProssList)) {
				System.out.println("No more crawler need to process today!!!");
			} else {
				
				List<Integer> successList = finalProssList.stream().map(var -> var.getCrawl_request_log_id_i()).collect(Collectors.toList());
				successList.removeAll(failedList);
				
				System.out.println("==== start summary, final process list : " + finalProssList.size());
				System.out.println("=== final list:" + gson.toJson(finalProssList));
				summary();
				
				
			}
			
			long endTime = System.currentTimeMillis();
			int elapsedSeconds = (int) (endTime - startTime);
			
			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, needProcessCrawler.size(), elapsedSeconds, logId);
		} catch (Exception e) {
			e.printStackTrace();
			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
		}
	}

}
