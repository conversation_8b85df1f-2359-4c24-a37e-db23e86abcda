package seoclarity.backend.summary;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.model.*;
import com.google.gson.Gson;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import seoclarity.backend.dao.actonia.OwnDomainEntityDAO;
import seoclarity.backend.dao.actonia.ScriptRunningDetailJdbcDAO;
import seoclarity.backend.dao.actonia.UploadFileDetailDao;
import seoclarity.backend.dao.actonia.bot.BotDirectoryDao;
import seoclarity.backend.dao.actonia.bot.HostVerificationDAO;
import seoclarity.backend.dao.clickhouse.bot.BotDetailDao;
import seoclarity.backend.entity.UploadFileDetailEntity;
import seoclarity.backend.entity.actonia.OwnDomainEntity;
import seoclarity.backend.entity.actonia.ScriptRunningDetailEntity;
import seoclarity.backend.entity.actonia.bot.HostVerificationEntity;
import seoclarity.backend.entity.clickhouse.bot.BotJsonVO;
import seoclarity.backend.multithread.BackendThreadFactory;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.amazon.S3Utils;

import java.io.*;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by Leo on 2017/2/27.
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.ClarityDBBotDailyUploadThreadMain" -Dexec.cleanupDaemonThreads=false -Dexec.args="/home/<USER>/bot_file/ 2019-04-22 2019-05-21 5"
 */
public class ClarityDBBotDailyUploadThreadMain {
    private static int threadCount = 1;
    private ExecutorService newFixedThreadPool =
            new ThreadPoolExecutor(threadCount, threadCount, 60L, TimeUnit.MILLISECONDS, new LinkedBlockingQueue<Runnable>(), new BackendThreadFactory());

    private OwnDomainEntityDAO ownDomainEntityDAO;
    private BotDirectoryDao botDirectoryDao;
    private HostVerificationDAO hostVerificationDAO;
    private BotDetailDao botDetailDao;

    private static Gson gson = new Gson();

    public static Integer SAVE_VERSION = null;
    public static Set<String> verificationSyncIPList = Collections.synchronizedSet(new HashSet<String>());
    private static boolean isRerun = false;


    public static Long zipFileSize = 0L;
    public static Long unzippedFileSize = 0L;
    public static Integer dataCount = 0;
    public static Integer loadCount = 0;

    public static Map<String, OwnDomainEntity> domainMap = Collections.synchronizedMap(new HashMap<String, OwnDomainEntity>());
    public static Map<Integer, OwnDomainEntity> domainTargetIdMap = Collections.synchronizedMap(new HashMap<Integer, OwnDomainEntity>());
    public static Map<Integer, Map<String, OwnDomainEntity>> urlPointDomainMap = Collections.synchronizedMap(new HashMap<>());
    public static Map<Integer, Map<String, Integer>> urlPointDomainIdMap = new HashMap<>();
    public static Set<Integer> expediaDomainIdSet = new HashSet<>();
    private Map<String, Integer> domainIdMap = new HashMap<>();
    public static List<String> emptyDomainList = Collections.synchronizedList(new ArrayList<String>());
    private static String baseLocation = "/opt/bot/";
    private static final String uploadFileSizeStatisticsLocation = "/opt/bot/uploadFileTemp/";
    private static final String BUCKET_NAME = "flex-seo-access-log-consumer-lambda-prod";
    private static final String S3_FILE_PREFIX = "LZ/signals/google-bot/";
    private static final int DEFAULT_DOMAIN_ID = 4765;
    private static final int DEFAULT_CONFIG_ID = 8;
    private static final long DEFAULT_CLEAR_CACHE_DAYS = 3;
    private static final int MISS_FILE_ALERT_DELAY_DAYS = -3;
    private UploadFileDetailDao uploadFileDetailDao;
    private ScriptRunningDetailJdbcDAO scriptRunningDetailJdbcDAO;
    private ZeptoMailSenderComponent zeptoMailSenderComponent;
    private static final String[] bccTo = {"<EMAIL>", "<EMAIL>", "<EMAIL>"};
    private static final String emailTo = "<EMAIL>";
    private static final String CACHE_SERVICE_URL = "https://s11-dev.seoclarity.dev/seoClarity/cacheService/cleanCache?access_token=c09yxv13-opr3-d745-9734-8pu48420nj67&type=bot&oid=";
    private static final String SUMMARY_SERVICE_URL = "https://s11-dev.seoclarity.dev/seoClarity/cacheService/cleanSummaryCache?access_token=c09yxv13-opr3-d745-9734-8pu48420nj67&type=bot&oid=";
    private static final String AND = "&";

    public ClarityDBBotDailyUploadThreadMain() {
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        botDirectoryDao = SpringBeanFactory.getBean("botDirectoryDao");
        hostVerificationDAO = SpringBeanFactory.getBean("hostVerificationDAO");
        botDetailDao = SpringBeanFactory.getBean("botDetailDao");
        uploadFileDetailDao = SpringBeanFactory.getBean("uploadFileDetailDao");
        scriptRunningDetailJdbcDAO = SpringBeanFactory.getBean("scriptRunningDetailJdbcDAO");
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");

        List<HostVerificationEntity> verificationEntities = hostVerificationDAO.getAll();
        if (CollectionUtils.isNotEmpty(verificationEntities)) {
            for (HostVerificationEntity verificationEntity : verificationEntities) {
                verificationSyncIPList.add(verificationEntity.getIp());
            }
        }

        //init expedia domain name
        domainIdMap.put("cheaptickets.com", 550);
        domainIdMap.put("www.cheaptickets.com", 550);
        domainIdMap.put("ebookers.ch", 1180);
        domainIdMap.put("ebookers.com", 551);
        //Leo - https://www.wrike.com/open.htm?id=211327052
        domainIdMap.put("www.ebookers.com", 551);
        domainIdMap.put("ebookers.ie", 898);
        domainIdMap.put("orbitz.com", 522);
        domainIdMap.put("www.orbitz.com", 522);
        domainIdMap.put("thailand.airasiago.com", 5085);
        domainIdMap.put("www.airasiago.com.my", 5084);
        domainIdMap.put("www.ebookers.at", 593);
        domainIdMap.put("www.ebookers.be", 560);
        domainIdMap.put("www.ebookers.ch", 561);
        domainIdMap.put("www.ebookers.de", 553);
        domainIdMap.put("www.ebookers.fi", 555);
        domainIdMap.put("www.ebookers.fr", 552);
        domainIdMap.put("www.ebookers.ie", 562);
        domainIdMap.put("www.ebookers.nl", 558);
        domainIdMap.put("www.ebookers.no", 554);
        domainIdMap.put("www.expedia.at", 4754);
        domainIdMap.put("www.expedia.be", 4746);
        domainIdMap.put("www.expedia.ca", 4762);
        domainIdMap.put("www.expedia.ch", 4749);
        domainIdMap.put("www.expedia.cn", 4741);
        domainIdMap.put("www.expedia.co.id", 4742);
        domainIdMap.put("www.expedia.com.my", 4728);
        domainIdMap.put("www.expedia.co.in", 5070);
        domainIdMap.put("www.expedia.co.jp", 5071);
        domainIdMap.put("www.expedia.co.kr", 4727);
        domainIdMap.put("www.expedia.co.nz", 4730);
        domainIdMap.put("www.expedia.co.th", 4738);
        domainIdMap.put("www.expedia.co.uk", 4739);
        domainIdMap.put("www.expedia.com", 4765);
        domainIdMap.put("www.expedia.com.ar", 4753);
        domainIdMap.put("www.expedia.com.au", 4744); // https://www.wrike.com/open.htm?id=1280974963 5069 -> 4744
        domainIdMap.put("www.expedia.com.br", 4747);
        domainIdMap.put("www.expedia.com.hk", 4764);
        domainIdMap.put("www.expedia.com.ph", 4734);
        domainIdMap.put("www.expedia.com.sg", 4735);
        domainIdMap.put("www.expedia.com.tw", 4737);
        domainIdMap.put("www.expedia.com.vn", 4740);
        domainIdMap.put("www.expedia.de", 4763);
        domainIdMap.put("www.expedia.dk", 4750);
        domainIdMap.put("www.expedia.es", 4736);
        domainIdMap.put("www.expedia.fi", 4751);
        domainIdMap.put("www.expedia.fr", 4752);
        domainIdMap.put("www.expedia.ie", 4743);
        domainIdMap.put("www.expedia.it", 4761);
        domainIdMap.put("www.expedia.mx", 4755);
        domainIdMap.put("www.expedia.nl", 4729);
        domainIdMap.put("www.expedia.no", 4733);
        domainIdMap.put("www.expedia.se", 4756);
        domainIdMap.put("www.flights.com", 5157);
        domainIdMap.put("www.lastminute.co.nz", 4732);
        domainIdMap.put("www.lastminute.com.au", 4745);
        domainIdMap.put("www.mrjet.dk", 557);
        domainIdMap.put("www.mrjet.se", 556);
        domainIdMap.put("www.travelocity.ca", 4748);
        domainIdMap.put("www.travelocity.com", 4758);
        domainIdMap.put("www.wotif.co.nz", 4731);
        domainIdMap.put("www.wotif.com", 10666);
//        domainIdMap.put("www.walgreens.com", 3458); // https://www.wrike.com/open.htm?id=1615114719 -> Ryan think this domain is not expedia domain at 2025-03-20 23:42
        domainIdMap.put("www.crossfinity.co.jp", 1508);
        domainIdMap.put("pharma.mynavi.jp", 5903);
        domainIdMap.put("kango.mynavi.jp", 5461);
        domainIdMap.put("us.expedia.cn", 12830);
        domainIdMap.put("th.hotels.com", 12287);
        domainIdMap.put("kr.hotels.com", 12288);

        // https://www.wrike.com/open.htm?id=1207120311 start
        domainIdMap.put("at.hotels.com", 9275);
        domainIdMap.put("au.hotels.com", 9276);
        domainIdMap.put("ch.hotels.com", 9279);
        domainIdMap.put("fi.hotels.com", 9286);
        domainIdMap.put("fr.hotels.com", 9287);
        domainIdMap.put("ie.hotels.com", 9295);
        domainIdMap.put("it.hotels.com", 9296);
        domainIdMap.put("jp.hotels.com", 9297);
        domainIdMap.put("nl.hotels.com", 9298);
        domainIdMap.put("no.hotels.com", 9299);
        domainIdMap.put("nz.hotels.com", 9300);
        domainIdMap.put("pl.hotels.com", 9301);
        domainIdMap.put("ru.hotels.com", 9303);
        domainIdMap.put("sg.hotels.com", 9304);
        domainIdMap.put("sv.hotels.com", 9305);
        domainIdMap.put("tr.hotels.com", 9306);
        domainIdMap.put("tw.hotels.com", 9307);
        domainIdMap.put("za.hotels.com", 9308);
        domainIdMap.put("zh.hotels.com", 9309);
        domainIdMap.put("he.hotels.com", 9331);
        domainIdMap.put("uk.hotels.com", 9332);
        domainIdMap.put("da.hotels.com", 9281);
        domainIdMap.put("de.hotels.com", 9283);
        domainIdMap.put("ar.hotels.com", 9311);
        domainIdMap.put("ca.hotels.com", 9277);
        domainIdMap.put("www.vrbo.com", 8644);
        domainIdMap.put("www.hoteles.com", 9290);
        domainIdMap.put("www.hotels.com", 9294);
        domainIdMap.put("ar.hoteles.com", 9271);
        domainIdMap.put("www.hoteis.com", 9288);
        domainIdMap.put("www.hotels.cn", 12839);

        domainIdMap.put("be.hotels.com", 12822);
        domainIdMap.put("hu.hotels.com", 12823);
        domainIdMap.put("id.hotels.com", 12824);
        domainIdMap.put("in.hotels.com", 12825);
        domainIdMap.put("is.hotels.com", 12826);
        domainIdMap.put("ms.hotels.com", 12827);
        domainIdMap.put("ph.hotels.com", 12828);
        domainIdMap.put("pt.hoteis.com", 12829);
        domainIdMap.put("vi.hotels.com", 12832);
        domainIdMap.put("vacation.hotwire.com", 12831);

        domainIdMap.put("www.stayz.com.au", 8649);
        domainIdMap.put("www.bookabach.co.nz", 8650);
        domainIdMap.put("www.homelidays.com", 9529);
        domainIdMap.put("www.homeaway.nl", 8743);
        domainIdMap.put("www.homeaway.com.au", 9522);
        domainIdMap.put("www.homeaway.com", 8645);
        domainIdMap.put("www.fewo-direkt.de", 8647);
        domainIdMap.put("www.aluguetemporada.com.br", 8744);
        domainIdMap.put("www.abritel.fr", 8648);
        // https://www.wrike.com/open.htm?id=1207120311 end

        expediaDomainIdSet = new HashSet<>(Arrays.asList(
                550,550,1180,551,551,898,522,522,5085,5084,593,560,561,
                553,555,552,562,558,554,4754,4746,4762,4749,4741,4742,4728,
                5070,5071,4727,4730,4738,4739,4765,4753,4744,4747,4764,4734,
                4735,4737,4740,4763,4750,4736,4751,4752,4743,4761,4755,4729,
                4733,4756,5157,4732,4745,557,556,4748,4758,4731,4744,3458,
                1508,5903,5461,12830));

        //Leo - init www.newegg.com
        domainIdMap.put("www.newegg.com", 5671);
        domainIdMap.put("m.newegg.com", 5672);
        domainIdMap.put("promotions.newegg.com", 5673);
        domainIdMap.put("flash.newegg.com", 5674);
        domainIdMap.put("kb.newegg.com", 5675);
        domainIdMap.put("community.newegg.com", 5676);
        domainIdMap.put("blog.newegg.com", 5677);
        domainIdMap.put("www.gamecrate.com", 5678);
        domainIdMap.put("unlocked.newegg.com", 5679);
        domainIdMap.put("www.neweggbusiness.com", 5680);
        domainIdMap.put("blog.neweggbusiness.com", 5681);

        for (Map.Entry<String, Integer> entry : domainIdMap.entrySet()) {
            //System.out.println(" will insert domain into map :"+entry.getValue());
            OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(entry.getValue());
            if (ownDomainEntity == null) {
                System.out.println("Skip domain :" + entry.getValue() + ", this domain didn't exsit anymore.");
                continue;
            }
            domainMap.put(entry.getKey(), ownDomainEntity);
        }

        // https://www.wrike.com/open.htm?id=1207120311
        initUrlDomainMap();
        for (Integer domainId : urlPointDomainIdMap.keySet()) {
            Map<String, Integer> subDomainIdMap = urlPointDomainIdMap.get(domainId);
            Map<String, OwnDomainEntity> subDomainMap = new HashMap<>();
            subDomainIdMap.keySet().forEach(url -> {
                OwnDomainEntity ownDomainEntity = ownDomainEntityDAO.getById(subDomainIdMap.get(url));
                subDomainMap.put(url, ownDomainEntity);
            });
            urlPointDomainMap.put(domainId, subDomainMap);
        }
    }

    // init copyDomainMap
    private void initUrlDomainMap() {
        Map<String, Integer> zhHotelsMap = new HashMap<>();

        zhHotelsMap.put("zh.hotels.com/en/", 9333);
        urlPointDomainIdMap.put(9309, zhHotelsMap);

        Map<String, Integer> caHotelsMap = new HashMap<>();
        caHotelsMap.put("ca.hotels.com/fr/", 9278);
        urlPointDomainIdMap.put(9277, caHotelsMap);

        Map<String, Integer> vrboMap = new HashMap<>();
        vrboMap.put("vrbo.com/en-gb", 8646);
        vrboMap.put("vrbo.com/it-it", 8651);
        vrboMap.put("vrbo.com/de-at", 9523);
        vrboMap.put("vrbo.com/pt-br", 9524);
        vrboMap.put("vrbo.com/en-ca", 9525);
        vrboMap.put("vrbo.com/fr-ca", 9526);
        vrboMap.put("vrbo.com/da-dk", 9527);
        vrboMap.put("vrbo.com/fi-fi", 9528);
        vrboMap.put("vrbo.com/el-gr", 9532);
        vrboMap.put("vrbo.com/ja-jp", 9534);
        vrboMap.put("vrbo.com/es-mx", 9535);
        vrboMap.put("vrbo.com/no-no", 9536);
        vrboMap.put("vrbo.com/pl-pl", 9537);
        vrboMap.put("vrbo.com/pt-pt", 9538);
        vrboMap.put("vrbo.com/en-sg", 9540);
        vrboMap.put("vrbo.com/es-es", 9541);
        vrboMap.put("vrbo.com/sv-se", 9543);
        vrboMap.put("vrbo.com/nl-nl", 12286);
        vrboMap.put("vrbo.com/en-au", 12835);
        vrboMap.put("vrbo.com/en-nz", 12836);
        urlPointDomainIdMap.put(8644, vrboMap);
    }

    public void waitForThreadPool() throws InterruptedException {
        while (!newFixedThreadPool.isShutdown()) {
            Thread.sleep(10 * 1000);
            int aliveCount = ((ThreadPoolExecutor) newFixedThreadPool).getActiveCount();
            System.out.println("Thread aliveCount : " + aliveCount);
            if (aliveCount == 0) {
                newFixedThreadPool.shutdown();
            }
        }
    }

    public void loadTargetFile(Date date, String targetFile) {
        File file = new File(targetFile);
        if (!file.exists() || !file.isFile()) {
            System.out.println("can't find this File, or not File.");
            return;
        }
        /*if (!StringUtils.endsWithIgnoreCase(file.getName(), "json.gz")) {
            System.out.println("File name not end with json.gz will skip unzip logic");
            //Leo - we can handle txt file now
//            return;
        }*/
        execThread(date, file);
    }

    private void loadJsonFromFile(Date date) {
        //init (thread must be 1)
        zipFileSize = 0L;
        unzippedFileSize = 0L;
        dataCount = 0;
        loadCount = 0;

        int dateInt = FormatUtils.formatDateToYyyyMmDd(date);
        String folderName = baseLocation + dateInt;
        File folderFile = new File(folderName);
        if (!folderFile.isDirectory()) {
            System.out.println("can't find this folder, or not folder.");
            return;
        }
        //https://www.wrike.com/open.htm?id=577078308
//        uploadFileSizeStatistics(folderFile, date);
        List<File> allFiles = getAllFile(folderFile);
        for (File file : allFiles) {
            String fileName = file.getName();
            insertUploadFileInfo(dateInt, fileName);
            if (StringUtils.endsWithIgnoreCase(file.getName(), "gzip")) {
                file = FileUtil.rename(file, StringUtils.removeEndIgnoreCase(file.getName(), ".gzip") + ".gz", false, true);
                System.out.println("After update file name, new name is : " + file.getAbsolutePath() + " before: " + fileName);
            }
            if (!StringUtils.endsWithIgnoreCase(file.getName(), ".gz")) {
                continue;
            }
            System.out.println("will process file : " + file.getAbsolutePath());
            execThread(date, file);
        }

        insertUploadFileDetailInfo(DEFAULT_DOMAIN_ID, "expedia-" + dateInt + "-log", date, zipFileSize, unzippedFileSize, dataCount, loadCount);
    }

    private void insertUploadFileInfo(int logDate, String fileName) {
        ScriptRunningDetailEntity scriptRunningDetailEntity = new ScriptRunningDetailEntity();
        scriptRunningDetailEntity.setConfigId(DEFAULT_CONFIG_ID);
        scriptRunningDetailEntity.setOwnDomainId(DEFAULT_DOMAIN_ID);
        scriptRunningDetailEntity.setStatus(ScriptRunningDetailEntity.STATUS_FINISHED);
        scriptRunningDetailEntity.setLogDate(logDate);
        scriptRunningDetailEntity.setSymbol(fileName);
        scriptRunningDetailEntity.setRerunNo(0);
        scriptRunningDetailEntity.setCreateDate(new Date());
        scriptRunningDetailJdbcDAO.insert(scriptRunningDetailEntity);
    }

    private void execThread(Date date, File file) {
        newFixedThreadPool.execute(new ClarityDBBotDailyUploadThread(ownDomainEntityDAO, botDirectoryDao, hostVerificationDAO, botDetailDao, file, date));
        //if all threads are active, need wait!
        int activeCount = ((ThreadPoolExecutor) newFixedThreadPool).getActiveCount();
        while (threadCount - activeCount == 0) {
            try {
//                System.out.println("activeCount: " + activeCount + ", sleep 3s ");
                Thread.sleep(500);
                activeCount = ((ThreadPoolExecutor) newFixedThreadPool).getActiveCount();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    //统计expedia上传文件大小
//    @Deprecated
//    private void uploadFileSizeStatistics(File folderFile, Date logDate) {
//        try {
//            File uploadFileSizeStatisticsFile = new File(uploadFileSizeStatisticsLocation);
//            if (!uploadFileSizeStatisticsFile.exists()) {
//                uploadFileSizeStatisticsFile.mkdirs();
//            }
//            FileUtils.copyDirectoryToDirectory(folderFile, uploadFileSizeStatisticsFile);
//            System.out.println("-------->copy file from " + folderFile.getAbsolutePath() + " to " + uploadFileSizeStatisticsLocation + " success");
//
//            File lastDayUploadFileSizeStatisticsFile = new File(uploadFileSizeStatisticsLocation + "/" + FormatUtils.formatDateToYyyyMmDd(logDate));
//            System.out.println("$EC=>parseCnt for dir: " + lastDayUploadFileSizeStatisticsFile.getAbsolutePath());
//
//            List<File> allFiles = getAllFile(lastDayUploadFileSizeStatisticsFile);
//            Long zipFileSize = 0L;
//            Long unzippedFileSize = 0L;
//            Integer dataCount = 0;
//            for (File file : allFiles) {
//                zipFileSize += file.length();
//                File unZipFile = unZipFile(file);
//                if (null != unZipFile) {
//                    unzippedFileSize += unZipFile.length();
//                    List<BotJsonVO> botJsonVOS = getEntityFromJsonFile(unZipFile);
//                    if (botJsonVOS != null && botJsonVOS.size() > 0) {
//                        dataCount += botJsonVOS.size();
//                    }
//                }
//                System.out.println("$EC=>parseCnt=>dataCount: " + dataCount);
//                file.delete();
//                unZipFile.delete();
//            }
//            int dateInt = FormatUtils.formatDateToYyyyMmDd(logDate);
//            insertUploadFileDetailInfo(4765, "expedia-" + dateInt + "-log", logDate, zipFileSize, unzippedFileSize, dataCount, loadCount);
//            uploadFileSizeStatisticsFile.delete();
//        } catch (IOException e) {
//            e.printStackTrace();
//            System.out.println("-------->copy file from " + folderFile.getAbsolutePath() + " to " + uploadFileSizeStatisticsLocation + " occur IOException!");
//        } catch (Exception e) {
//            e.printStackTrace();
//            System.out.println("-------->copy file from " + folderFile.getAbsolutePath() + " to " + uploadFileSizeStatisticsLocation + " failed!");
//        }
//    }

    @Deprecated
    private List<BotJsonVO> getEntityFromJsonFile(File jsonFile){
        List<BotJsonVO> botJsonVOs = new ArrayList<>();
        List<String> lines;
        try (FileInputStream inputStream = new FileInputStream(jsonFile)) {
            lines = IOUtils.readLines(inputStream);
        } catch (Exception e) {
            return null;
        }
        for (int i = 0, length = lines.size(); i < length; i++) {
            try {
                BotJsonVO botJsonVO;
                if (StrUtil.startWith(lines.get(i), "[")) {
                    JSONArray jsonArray = JSONUtil.parseArray(lines.get(i));
                    for (JSONObject jsonObject : jsonArray.jsonIter()) {
                        botJsonVO = jsonObject.toBean(BotJsonVO.class);
                        botJsonVOs.add(botJsonVO);
                    }
                } else {
                    botJsonVO = gson.fromJson(lines.get(i), BotJsonVO.class);
                    botJsonVOs.add(botJsonVO);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return botJsonVOs;
    }


    protected void insertUploadFileDetailInfo(int domainId, String fileName, Date logDate, Long zipFileSize, Long unzippedFileSize, Integer dataCount, Integer loadCount) {
        try {
            UploadFileDetailEntity fileDetail = new UploadFileDetailEntity();
            fileDetail.setOwnDomainId(domainId);
            fileDetail.setUploadType(UploadFileDetailEntity.UPLOAD_TYPE_BOT);
            fileDetail.setFileName(fileName);
            fileDetail.setFileNameDay(FormatUtils.formatDateToYyyyMmDd(logDate));
            fileDetail.setZipFileSize(zipFileSize);
            fileDetail.setUnzippedFileSize(unzippedFileSize);
            fileDetail.setDataCount(dataCount);
            fileDetail.setLoadedCount(loadCount);
            fileDetail.setCreateDate(new Date());
            UploadFileDetailEntity uploadFileDetail = uploadFileDetailDao.getUploadFileDetailByUniqueKey(fileDetail);
            if (null == uploadFileDetail) {
                uploadFileDetailDao.insert(fileDetail);
                System.out.println("--------------> insert new uploadFileDetail info for " + fileName);
            } else {
                System.out.println("--------------> uploadFileDetail has existed, No need to insert new data for " + fileName);
            }
        } catch (Exception e) {
            System.out.println(e.getMessage());
            System.out.println("--------------> insert new uploadFileDetail info for " + fileName + " failed, need to check data");
        }
    }

    private File unZipFile(File file) {
        try {
            byte[] in = seoclarity.backend.utils.FileUtils.getBytesFromFile(file);
            File jsonFile = new File(file.getAbsolutePath().replaceAll(".gz", ""));
            if (jsonFile.exists()) {
                jsonFile.delete();
            }
            return GZipUtil.unGzipBigFile(in, jsonFile);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private static List<File> getAllFile(File folderFile) {
        List<File> files = new ArrayList<File>();
        for (File file : folderFile.listFiles()) {
            if (file.isDirectory()) {
                files.addAll(getAllFile(file));
            } else {
                files.add(file);
            }
        }
        return files;
    }

    public void checkMissBotDate(Date logDate) {
        Date checkStartDate = DateUtils.addDays(logDate, MISS_FILE_ALERT_DELAY_DAYS);
        int checkStartDateInt = Integer.parseInt(FormatUtils.formatDate(checkStartDate, "yyyyMMdd"));


        boolean sendEmail = false;
        String emailSubject = "Bot File Missing Alert(check date:" + checkStartDateInt + ")";
        StringBuilder msg = new StringBuilder();

        List<ScriptRunningDetailEntity> detailEntityList = scriptRunningDetailJdbcDAO.checkExistsByByConfigId(DEFAULT_CONFIG_ID, ScriptRunningDetailEntity.STATUS_ERROR, checkStartDateInt, false, null);
        if (detailEntityList != null && !detailEntityList.isEmpty()) {
            Map<Integer, List<ScriptRunningDetailEntity>> domainGroup = detailEntityList.stream().collect(Collectors.groupingBy(ScriptRunningDetailEntity::getOwnDomainId));
            System.out.println("==missBotUnExpediaDomainCnt:" + domainGroup.size());

            for (Integer domainId : domainGroup.keySet()) {
                ScriptRunningDetailEntity runningDetailEntity = domainGroup.get(domainId).get(0);
                msg.append("&nbsp;&nbsp;&nbsp;&nbsp;").append(domainId).append("&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;").append(runningDetailEntity.getDomainName()).append("<br/>");
            }
            sendEmail = true;
        }





        List<String> missDomainList = new ArrayList<>();
        Set<String> fileNameSet = scriptRunningDetailJdbcDAO.checkExistsList(DEFAULT_DOMAIN_ID, DEFAULT_CONFIG_ID, checkStartDateInt);
        if (fileNameSet == null || fileNameSet.isEmpty()) {
            missDomainList.addAll(domainMap.keySet());
        } else {
            List<String> fileNameList = new ArrayList<>(fileNameSet);
            for (String domainName : domainMap.keySet()) {
                boolean isFind = false;
                for (String fileName : fileNameList) {
                    if (StringUtils.containsIgnoreCase(fileName, domainName)) {
                        isFind = true;
                        break;
                    }
                }
                if (!isFind) {
                    missDomainList.add(domainName);
                }
            }
        }
        System.out.println("==missBotFileInfo checkDate:" + checkStartDateInt + " totalDomainCnt:" + domainIdMap.size() + " missDomainCnt:" + missDomainList.size());
        if (!missDomainList.isEmpty()) {
            if (msg.length() > 0) {
                msg.append("=======expedia domain=======<br>");
            }
            for (String domainName : missDomainList) {
                msg.append("&nbsp;&nbsp;&nbsp;&nbsp;").append(domainIdMap.get(domainName)).append("&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;").append(domainName).append("<br/>");
            }
        }
        if (sendEmail) {
            sendErrorAlertEmail(checkStartDateInt, emailSubject, msg.toString());
        }
    }

    public Map<Integer, List<String>> checkMissBotDateLocal(List<Date> logDates) {
        if (logDates == null || logDates.isEmpty()) {
            return null;
        }
        Map<Integer, List<String>> dateMap = new TreeMap<>();
        for (Date checkStartDate : logDates) {
            int checkStartDateInt = Integer.parseInt(FormatUtils.formatDate(checkStartDate, "yyyyMMdd"));

            List<String> missDomainStrList = new ArrayList<>();
            
            List<String> missDomainList = new ArrayList<>();
            Set<String> fileNameSet = scriptRunningDetailJdbcDAO.checkExistsList(DEFAULT_DOMAIN_ID, DEFAULT_CONFIG_ID, checkStartDateInt);
            if (fileNameSet == null || fileNameSet.isEmpty()) {
                missDomainList.addAll(domainMap.keySet());
            } else {
                List<String> fileNameList = new ArrayList<>(fileNameSet);
                for (String domainName : domainMap.keySet()) {
                    boolean isFind = false;
                    for (String fileName : fileNameList) {
                        if (StringUtils.containsIgnoreCase(fileName, domainName)) {
                            isFind = true;
                            break;
                        }
                    }
                    if (!isFind) {
                        missDomainList.add(domainName);
                    }
                }
            }
            System.out.println("==missBotFileInfo checkDate:" + checkStartDateInt + " totalDomainCnt:" + domainIdMap.size() + " missDomainCnt:" + missDomainList.size());
            if (!missDomainList.isEmpty()) {
                StringBuilder msg = new StringBuilder();
                for (String domainName : missDomainList) {
                    missDomainStrList.add(domainIdMap.get(domainName) + "\t" + domainName);
                }
            }
            dateMap.put(checkStartDateInt, missDomainStrList);
        }
        return dateMap;
    }

    private void sendErrorAlertEmail(int dateInt, String emailSubject, String msg) {
        Map<String, Object> reportMap = new HashMap<>();
        reportMap.put("userName", "ALL");
        reportMap.put("errMsg", "miss bot file date:" + dateInt + "<br/>miss bot file domains:<br>" + msg);
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(emailTo, bccTo, emailSubject, "mail_alert_missing_bot_files.txt", "mail_alert_missing_bot_files.html",
                reportMap, null, ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

    public void reprocessMissFile(Date startDate, Date endDate, String baseLocationPath) {
        List<Date> logDates = new ArrayList<>();
        Date currentDate = new Date();
        Date checkEndDate = DateUtils.addDays(currentDate, -1);

        if (startDate == null || endDate == null) {
            endDate = DateUtils.addDays(currentDate, -2);
            startDate = DateUtils.addDays(endDate, -30);
        }

        while (startDate.compareTo(endDate) <= 0) {
            logDates.add(startDate);
            startDate = DateUtils.addDays(startDate, 1);
        }
        // init parent file path
        String parentPath = "";
        if (StringUtils.isEmpty(baseLocationPath)) {
            parentPath = baseLocation + "reprocessMissFile_" + FormatUtils.formatDate(currentDate, "yyyyMMdd") + "/";
        } else {
            parentPath = baseLocationPath + "reprocessMissFile_" + FormatUtils.formatDate(currentDate, "yyyyMMdd") + "/";
        }
        System.out.println("==reprocessMissFile parentPath:" + parentPath);

        Set<Integer> clearCacheDomainList = new HashSet<>();
        String hourStr = "";
        String s3FileSuffix = "";
        // download s3 file
        for (Date logDate : logDates) {
            String dateSuffix = FormatUtils.formatDate(logDate, "yyyy/MM/dd");
            int dateInt = Integer.parseInt(FormatUtils.formatDate(logDate, "yyyyMMdd"));
            String logDateParentPath = parentPath + dateInt + "/";

            // already process file set
            Set<String> fileNameSet = scriptRunningDetailJdbcDAO.checkExistsList(DEFAULT_DOMAIN_ID, DEFAULT_CONFIG_ID, dateInt);
            System.out.println("will process for date : " + dateInt + " already process file size : " +  fileNameSet.size());
            for (int i = 0; i < 24; i++) {
                if (i <= 9) {
                    hourStr = "/0" + i;
                } else {
                    hourStr = "/" + i;
                }
                s3FileSuffix = S3_FILE_PREFIX + dateSuffix + hourStr + "/";
                downloadFileFromS3(logDateParentPath, s3FileSuffix, fileNameSet, dateInt);
            }

            // reprocess file
            File folderFile = new File(logDateParentPath);
            if (!folderFile.isDirectory()) {
                System.out.println("can't find this folder, or not folder will skip. date:" + dateInt + " path:" + logDateParentPath);
                continue;
            }
            List<File> allFiles = getAllFile(folderFile);
            if (allFiles.isEmpty()) {
                continue;
            }
            for (File file : allFiles) {
                String fileName = file.getName();
                insertUploadFileInfo(dateInt, fileName);
                if (StringUtils.endsWithIgnoreCase(file.getName(), "gzip")) {
                    file = FileUtil.rename(file, StringUtils.removeEndIgnoreCase(file.getName(), ".gzip") + ".gz", false, true);
                    System.out.println("After update file name, new name is : " + file.getAbsolutePath() + " before : " + fileName);
                }
                if (!StringUtils.endsWithIgnoreCase(file.getName(), ".gz")) {
                    continue;
                }
                System.out.println("will reprocess file : " + file.getAbsolutePath());
                execThread(logDate, file);
                checkIsNeedClearCache(fileName, checkEndDate, logDate, clearCacheDomainList); // https://www.wrike.com/open.htm?id=1529967569
            }
        }

        // https://www.wrike.com/open.htm?id=1529967569
        if (!clearCacheDomainList.isEmpty()) {
            clearCache(clearCacheDomainList);
        }

        // delete file
        File file = new File(parentPath);
        if (file.exists()) {
            System.out.println("==delete file : " + file.getAbsolutePath());
            file.delete();
        } else {
            System.out.println("==no need to delete file : " + file.getAbsolutePath());
        }
    }

    public void checkIsNeedClearCache(String fileName, Date checkEndDate, Date reprocessDate, Set<Integer> clearCacheDomainList) {
        LocalDate recentDate = checkEndDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        LocalDate oldDate = reprocessDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        long daysBetween = ChronoUnit.DAYS.between(recentDate, oldDate);
        daysBetween = Math.abs(daysBetween);
        if (daysBetween >= DEFAULT_CLEAR_CACHE_DAYS) {
            boolean isMatch = false;
            int matchDomainId = 0;
            for (String domainName : domainMap.keySet()) {
                int domainId = domainMap.get(domainName).getId();
                if (urlPointDomainIdMap.containsKey(domainId) && !clearCacheDomainList.contains(domainId)) {
                    Map<String, Integer> domainNamePIdMap = urlPointDomainIdMap.get(domainId);
                    clearCacheDomainList.addAll(domainNamePIdMap.values());
                    matchDomainId = domainId;
                    isMatch = true;
                    break;
                } else {
                    if (StringUtils.containsIgnoreCase(fileName, domainName)) {
                        isMatch = true;
                        matchDomainId = domainId;
                        break;
                    }
                }

            }
            if (isMatch) {
                clearCacheDomainList.add(matchDomainId);
            }
        }
    }

    public void clearCache(Set<Integer> clearCacheDomainList) {
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            for (Integer oid : clearCacheDomainList) {
                if (oid != null) {
                    System.out.println("===clearDomainCache domainId:" + oid);
                    // Construct URLs
                    String cacheUrl = CACHE_SERVICE_URL + oid;
                    String summaryUrl = SUMMARY_SERVICE_URL + oid;

                    sendRequest(client, cacheUrl);
                    sendRequest(client, summaryUrl);
                    try {
                        TimeUnit.MILLISECONDS.sleep(300);
                    }catch (Exception ee) {
                        ee.printStackTrace();
                    }
                }
            }
        } catch (IOException e) {
            System.out.println("==clearCacheError");
            e.printStackTrace();
        }
    }

    // https://www.wrike.com/open.htm?id=1615114719
    public static void clearCacheWithMonth(int oid, List<ScriptRunningDetailEntity> detailEntities) {
        if (detailEntities == null || detailEntities.isEmpty()) {
            return;
        }
        Set<String> clearYearMonthSet = new HashSet<>();
        for (ScriptRunningDetailEntity detailEntity : detailEntities) {
            String yearMonth = String.valueOf(detailEntity.getLogDate()).substring(0, 6);
            clearYearMonthSet.add(yearMonth);
        }

        try (CloseableHttpClient client = HttpClients.createDefault()) {
            System.out.println("===clearDomainCache domainId:" + oid + " month:" + clearYearMonthSet);

            String cacheUrl = CACHE_SERVICE_URL + oid;
            sendRequest(client, cacheUrl);


            for (String yearMonth : clearYearMonthSet) {
                String summaryUrl = SUMMARY_SERVICE_URL + oid + AND + yearMonth;;
                sendRequest(client, summaryUrl);
                try {
                    TimeUnit.MILLISECONDS.sleep(300);
                }catch (Exception ee) {
                    ee.printStackTrace();
                }
            }
        } catch (IOException e) {
            System.out.println("==clearCacheError");
            e.printStackTrace();
        }
    }

    private static void sendRequest(CloseableHttpClient client, String url) {
        HttpGet request = new HttpGet(url);
        try (CloseableHttpResponse response = client.execute(request)) {
            HttpEntity entity = response.getEntity();
            if (entity != null) {
                String result = EntityUtils.toString(entity);
                System.out.println("Response for URL " + url + ": " + response.getStatusLine().getStatusCode() + " res:" + result);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void downloadFileFromS3(String parentPath, String s3FileSuffix, Set<String> alreadyProcessFileSet, int dateInt) {
        int fileCont = 0;
        try {
            AmazonS3 s3client = S3Utils.getAmazonS3WithDevUser();
            ListObjectsRequest request_file = new ListObjectsRequest().withBucketName(BUCKET_NAME);
            request_file.setPrefix(s3FileSuffix);
            ObjectListing objects_file = s3client.listObjects(request_file);
            /*循环获取文件*/
            List<S3ObjectSummary> filePathObjectSummaryList = new ArrayList<>(objects_file.getObjectSummaries());
            while (objects_file.isTruncated()) {
                objects_file = s3client.listNextBatchOfObjects(objects_file);
                filePathObjectSummaryList.addAll(objects_file.getObjectSummaries());
            }
            if (filePathObjectSummaryList.isEmpty()) {
                System.out.println("s3 file is empty, skip. date:" + dateInt + " alreadyProcessFileCnt:" + alreadyProcessFileSet.size() + " path:" + s3FileSuffix);
                return;
            }
            for (S3ObjectSummary s3ObjectSummary : filePathObjectSummaryList) {
                String remoteFileName = s3ObjectSummary.getKey();
                String[] split = remoteFileName.split("/");
                String fileName = split[split.length - 1];
                if (alreadyProcessFileSet != null && !alreadyProcessFileSet.isEmpty()) {
                    if (alreadyProcessFileSet.contains(fileName)) {
                        continue;
                    }
                }
                String filePath = parentPath + fileName;
                GetObjectRequest rangeObjectRequest = new GetObjectRequest(BUCKET_NAME, remoteFileName);
                S3Object objectPortion = s3client.getObject(rangeObjectRequest);

                /** 解决文件父路径不存在问题 */
                File outFile = new File(filePath);
                File parentFile = outFile.getParentFile();
                if (!parentFile.exists()) {
                    parentFile.mkdirs();
                }

                if (objectPortion != null && objectPortion.getObjectContent() != null) {
                    InputStream objectData = objectPortion.getObjectContent();
                    FileOutputStream fos = new FileOutputStream(outFile);
                    byte[] readBuf = new byte[1024];
                    int readLen;
                    while ((readLen = objectData.read(readBuf)) > 0) {
                        fos.write(readBuf, 0, readLen);
                    }
                    objectData.close();
                    fos.close();
                }
                fileCont++;
            }
        } catch (Exception e) {
            System.out.println("==downloadFileFromS3Error:" + e.getMessage());
            e.printStackTrace();
        }
        System.out.println("===> download file count: " + fileCont + " date:" + dateInt + " s3FileSuffix:" + s3FileSuffix);
    }

    public static void main(String[] args) throws InterruptedException, ParseException, IOException {
        List<Date> logDates = new ArrayList<>();
        if (args != null && args.length > 0) {
            baseLocation = args[0];
        }
        System.out.println("baseLocation : " + baseLocation);

        if (args != null && args.length > 2) {
            Date sDate = DateUtils.parseDate(args[1], new String[]{"yyyy-MM-dd", "yyyyMMdd", "yyyy/MM/dd"});
            Date eDate = DateUtils.parseDate(args[2], new String[]{"yyyy-MM-dd", "yyyyMMdd", "yyyy/MM/dd"});
            while (sDate.compareTo(eDate) <= 0) {
                logDates.add(sDate);
                sDate = DateUtils.addDays(sDate, 1);
            }
        } else {
            logDates.add(DateUtils.addDays(new Date(), -1));
        }

        System.out.println("process for date : " + gson.toJson(logDates));
        if (args != null && args.length > 3) {
            threadCount = NumberUtils.toInt(args[3], 5);
        }
        if (args != null && args.length > 4) {
            SAVE_VERSION = NumberUtils.toInt(args[4], -1);
            System.out.println("SAVE_VERSION : " + SAVE_VERSION);
        }
        if (args != null && args.length > 5) {
            isRerun = BooleanUtils.toBoolean(args[5]);
        }
        System.out.println("isRerun : " + isRerun);
        System.out.println("threadCount : " + threadCount);
        Thread.sleep(20000);

        ClarityDBBotDailyUploadThreadMain clarityDBBotDailyUpload = new ClarityDBBotDailyUploadThreadMain();

//        BearyChatUtils.sendMessage("Start Bot Common Daily Uploading...", BearyChatUtils.LOG_LEVEL.INFO);

        for (Date logDate : logDates) {
            System.out.println("will process for date : " + logDate);
//            BearyChatUtils.sendMessage("Start Bot Common Daily Uploading For Date :"+FormatUtils.formatDate(logDate, "yyyy-MM-dd"), BearyChatUtils.LOG_LEVEL.INFO);
            clarityDBBotDailyUpload.loadJsonFromFile(logDate);
        }
        // 重跑不需要检查重新处理
        // https://www.wrike.com/open.htm?id=1363032191
        if (!isRerun) {
            try {
                clarityDBBotDailyUpload.reprocessMissFile(null, null, null);
            } catch (Exception e) {
                e.printStackTrace();
                Thread.interrupted();
            }

            // https://www.wrike.com/open.htm?id=1529967569
            /*try {
                clarityDBBotDailyUpload.checkMissBotDate(logDates.get(0));
            } catch (Exception e) {
                e.printStackTrace();
                Thread.interrupted();
            }*/
        }
        clarityDBBotDailyUpload.waitForThreadPool();
//        BearyChatUtils.sendMessage("Finished Bot Common Daily Uploading.", BearyChatUtils.LOG_LEVEL.INFO);
    }


}
