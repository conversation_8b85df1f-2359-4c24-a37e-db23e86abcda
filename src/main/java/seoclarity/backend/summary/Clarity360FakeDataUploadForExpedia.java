package seoclarity.backend.summary;

import java.io.FileReader;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;

import seoclarity.backend.dao.clickhouse.clarity360.lweb05.Clarity360Lweb05DAO;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.Clarity360FakeDataUploadForExpedia" -Dexec.args=""
public class Clarity360FakeDataUploadForExpedia {
	
	private Clarity360Lweb05DAO clarity360Lweb05DAO;

	public Clarity360FakeDataUploadForExpedia() {
		clarity360Lweb05DAO = SpringBeanFactory.getBean("clarity360Lweb05DAO");
	}

	public static void main(String[] args) throws IOException {
		
		Clarity360FakeDataUploadForExpedia clarity360FakeDataUpload = new  Clarity360FakeDataUploadForExpedia();
		clarity360FakeDataUpload.process();
	}
	
	private static String targetDate = "2024-02-21";
	
	
	public static final String[] fileHeader = new String[] {
			"url", 
			"status", 
			"depth", 
//			"date_crawled", 
			"title", 
			"h1", 
			"h2", 
			"h3", 
			"description", 
			"indexable", 			//compliant=false means 0
			"canonical_type", 		//compliant_reason_canonical
			"index_flg", 			//compliant_reason_noindex
			"outlink_count", 		//number_of_outlinks
			"server_response_time", //delay_first_byte
			"download_time", 		//delay_last_byte
			
			"Flight Card Count", 
			"Activities Card Count", 
			"template-id", 
			"template-fm-id", 
			"Packages Card Count", 
			"Hotel Card Count", 
			"Car Card Count", 
			
//			"title_length", 
//			"h1_length",
//			"custom_data.selector_type", 
//			"custom_data.match_found", 
//			"custom_data.selector", 
//			"domain", 
//			"domain_name", 
//			"folder_level_1", 
//			"folder_level_2", 
//			"folder_level_3", 
//			"folder_level_count", 
//			"create_time", 
//			"domain_id", 
//			"report_id", 
//			"crawl_request_id", 
//			"sources", 
//			"target_date", 
	 };
	 
	private CSVFormat csvFormat = CSVFormat.DEFAULT.withDelimiter('\t').withRecordSeparator('\n')
				.withHeader(fileHeader);
		
	
	private static Integer maxInsertCount = 100000;
	private void process() throws IOException {
		
		try {
			FileReader fr = new FileReader("/home/<USER>/Expeida_Custom_Data_Integration_20240208.txt");
			CSVParser csvParser = new CSVParser(fr, csvFormat);
			
			Integer totalSize = 0;
			List<Clarity360Entity> clarityEntities = new ArrayList<>();
			// don't read header
			List<CSVRecord> csvRecords = csvParser.getRecords();
			System.out.println("csvRecords.size():" + csvRecords.size());
			for (int i = 1; i < csvRecords.size(); i++) {
				CSVRecord csvRecord = csvRecords.get(i);
				Clarity360Entity clarity360Entity;
				try {
					clarity360Entity = getClarityEntity(csvRecord);
				} catch (Exception e) {
					System.out.println("line i : " + i);
					e.printStackTrace();
					continue;
				}
				clarityEntities.add(clarity360Entity);
				if (clarityEntities.size() >= maxInsertCount) {
					clarity360Lweb05DAO.insertForBatchV2(clarityEntities);
					System.out.println("finish insert for top : " + maxInsertCount);
					clarityEntities.clear();
				}
			}
			if (CollectionUtils.isNotEmpty(clarityEntities)) {
				clarity360Lweb05DAO.insertForBatchV2(clarityEntities);
				System.out.println("finish insert for left count :" + clarityEntities.size());
				totalSize += clarityEntities.size();
				clarityEntities.clear();
			}

			System.out.println("totalSize:" + totalSize);
			csvParser.close();
			fr.close();
		} catch (Exception e) {
			e.printStackTrace();
			return;
		}

	}
	
	
//	private List<Clarity360Entity> getClarity360Entity(String sheetName) throws Exception{
//		List<Clarity360Entity> list = new ArrayList<Clarity360Entity>();
//		
//		XSSFWorkbook wb = new XSSFWorkbook(filePath);
//		XSSFSheet sheet = wb.getSheet(sheetName);
//		for (int i = sheet.getFirstRowNum() + 1; i <= sheet.getLastRowNum(); i++) {
//			XSSFRow row = sheet.getRow(i);
//			if (row == null || row.getPhysicalNumberOfCells() == 0) {
//				continue;
//			}
//			String kw = null;
//			try {
//				kw = row.getCell(0).getStringCellValue();
//			} catch (Exception e) {
//				kw = row.getCell(0).toString();
//				e.printStackTrace();
//			}
//			if (StringUtils.isNotBlank(kw)) {
//				kw = kw.trim().toLowerCase();
//				list.add(URLEncoder.encode(kw, "UTF-8").toLowerCase().trim());
//			} else {
//				System.out.println("=FOUND EMPTY KW, idx:" + i);
//			}
//		}
//		System.out.println("===total:" + sheet.getLastRowNum() + ", list:" + list.size());
//		return list;
//	}
	
	
	/*
	 * 		"url", 
			"status", 
			"depth", 
			"date_crawled", 
			"title", 
			"h1", 
			"h2", 
			"h3", 
			"description", 
			"indexable", 
			"canonical_type", 
			"index_flg", 
			"outlink_count", 
			"server_response_time ", 
			"download_time", 
	 */
	private Clarity360Entity getClarityEntity(CSVRecord csvRecord) throws ParseException {
		Clarity360Entity clarity360Entity = new Clarity360Entity();

		String url = csvRecord.get("url");
		clarity360Entity.setUrl(url); 

//		clarity360Entity.setCanonical_type(csvRecord.get("canonical_type")); 
		
		if (StringUtils.equalsIgnoreCase(csvRecord.get("canonical_type"), "false") || StringUtils.isBlank(csvRecord.get("canonical_type"))) {
			clarity360Entity.setCanonical_type(""); 
		} else {
			clarity360Entity.setCanonical_type("OTHER"); 
		}
		
		String description = csvRecord.get("description");
		
		if (StringUtils.isNotBlank(description)) {
			clarity360Entity.setDescription(description); 
			clarity360Entity.setDescription_length(description.length());
		}

		
		BigDecimal dividend = new BigDecimal(NumberUtils.toFloat(csvRecord.get("download_time")));
        BigDecimal divisor = new BigDecimal(1000);   // 除数
        try {
            BigDecimal downloadTime = dividend.divide(divisor);
            clarity360Entity.setDownload_time(downloadTime.floatValue()); 
        } catch (ArithmeticException e) {
            e.printStackTrace();
        }
        
		BigDecimal dividServerResponseTime = new BigDecimal(NumberUtils.toFloat(csvRecord.get("server_response_time")));
        try {
            BigDecimal serverResponseTime = dividServerResponseTime.divide(divisor);
            clarity360Entity.setServer_response_time(serverResponseTime.floatValue()); 
        } catch (ArithmeticException e) {
            e.printStackTrace();
        }
        
		clarity360Entity.setStatus(NumberUtils.toInt(csvRecord.get("status"))); 
		clarity360Entity.setIndex_flg(StringUtils.equalsIgnoreCase(csvRecord.get("index_flg"), "true") ? "Yes" : "No"); 
		
		String h1 = csvRecord.get("h1");
		clarity360Entity.setH1(new String[] {h1}); 
		clarity360Entity.setH2(new String[] {csvRecord.get("h2")}); 
		
		String h3 = csvRecord.get("h3");
		String content2 = csvRecord.get("Flight Card Count");
		String content3 = csvRecord.get("Activities Card Count");
		String content4 = csvRecord.get("template-id");
		String content5 = csvRecord.get("template-fm-id");
		String content6 = csvRecord.get("Packages Card Count");
		String content7 = csvRecord.get("Hotel Card Count");
		String content8 = csvRecord.get("Car Card Count");
		
		Map<String, String> contentMap = new HashMap<String, String>();
		contentMap.put("h3", h3);
		contentMap.put("Flight Card Count", content2);
		contentMap.put("Activities Card Count", content3);
		contentMap.put("template-id", content4);
		contentMap.put("template-fm-id", content5);
		contentMap.put("Packages Card Count", content6);
		contentMap.put("Hotel Card Count", content7);
		contentMap.put("Car Card Count", content8);
		
		formatContentList(contentMap, clarity360Entity);
		
		if (StringUtils.equalsIgnoreCase(csvRecord.get("index_flg"), "true")) {
			clarity360Entity.setRobots_contents("noindex");
		}
		
		Integer compliant = StringUtils.equalsIgnoreCase(csvRecord.get("indexable"), "true") ? 1 : 0;
		clarity360Entity.setIndexable(compliant); 
		
		if (compliant == 1) {
			clarity360Entity.setAnalyzed_url_flg_s("Yes");
		}
		
		Integer depth = NumberUtils.toInt(csvRecord.get("depth"));
		if (depth < 0) {
			depth = 0;
		}
		clarity360Entity.setCrawl_depth(depth); 
		clarity360Entity.setCrawl_request_id(9964970);
		
		String domain = FormatUtils.getDomainByUrl(url);
		clarity360Entity.setDomain(domain); 
		clarity360Entity.setDomain_name(domain); 
        
		try {
			HashSet<String> topFolders = getetDirectory(url);
			List<String> folders = new ArrayList<String>(topFolders);

			if(folders != null &&folders.size() > 0) {
				clarity360Entity.setFolder_level_1(folders != null && folders.size() >= 1 ? folders.get(0) : ""); 
				clarity360Entity.setFolder_level_2(folders != null && folders.size() >= 2 ? folders.get(1) : ""); 
				clarity360Entity.setFolder_level_3(folders != null && folders.size() >= 3 ? folders.get(2) : ""); 
				clarity360Entity.setFolder_level_count(folders.size());
			}

		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
       
		clarity360Entity.setSources(new String[] {"sitehealth"}); 
		clarity360Entity.setDomain_id(4);
		//https://dev.seoclarity.net/clarity360.do#/project/1118/report/652
		clarity360Entity.setReport_id(652);
		String title = csvRecord.get("title");
		if (StringUtils.isNotBlank(title)) {
			clarity360Entity.setTitle_length(title.length());
		}
		clarity360Entity.setTitle(title);
		
		clarity360Entity.setTarget_date(targetDate);
		
		if (StringUtils.isNotBlank(h1)) {
			clarity360Entity.setH1_length(h1.length());
		}
		return clarity360Entity;
	}
	
	private void formatContentList(Map<String, String> contentMap, Clarity360Entity clarity360Entity) {
		
		List<String> selectorList = new ArrayList<String>();
		List<String> selectTypeList = new ArrayList<String>();
		List<String> contentList = new ArrayList<String>();
		List<Integer> matchFoundList = new ArrayList<Integer>();
		
		for(String key : contentMap.keySet()) {
//			clarity360Entity.setCustom_data_content(new String[] {"[" + h3 + "]"}); 
//
//			clarity360Entity.setCustom_data_selector_type(new String[] {"XPATH"});
//			clarity360Entity.setCustom_data_match_found(new Integer[] {1});
//			clarity360Entity.setCustom_data_selector(new String[] {"h3"});
			
			String value = contentMap.get(key);
			if (StringUtils.isNotBlank(value)) {
				selectorList.add(key);
				selectTypeList.add("XPATH");
				contentList.add("[\"" + value + "\"]");
				matchFoundList.add(1);
			}
		}
		
		clarity360Entity.setCustom_data_content(contentList.toArray(new String[] {})); 

		clarity360Entity.setCustom_data_selector_type(selectTypeList.toArray(new String[] {}));
		clarity360Entity.setCustom_data_match_found(matchFoundList.toArray(new Integer[] {}));
		clarity360Entity.setCustom_data_selector(selectorList.toArray(new String[] {}));
	}
	
	
    public HashSet<String> getetDirectory(String url) throws Exception {
        url = StringUtils.substringBeforeLast(url, "/");

        String[] uriArraries = url.replaceFirst("/", "").split("/");

        String firstDepth = "";
        String secondDepth = "";
        String thirdDepth = "";
        LinkedHashSet<String> top3Directories = new LinkedHashSet<String>();

        if (uriArraries != null) {
            for (int i = 0; i < uriArraries.length; i++) {
                String directory = uriArraries[i];
                if (i == 0) {
                    firstDepth = directory;
                } else if (i == 1) {
                    secondDepth = firstDepth + "/" + directory;
                } else if (i == 2) {
                    thirdDepth = secondDepth + "/" + directory;
                }
            }
        }

        if (StringUtils.isNotBlank(firstDepth)) {
            top3Directories.add(firstDepth);
        }
        if (StringUtils.isNotBlank(secondDepth)) {
            top3Directories.add(secondDepth);
        }
        if (StringUtils.isNotBlank(thirdDepth)) {
            top3Directories.add(thirdDepth);
        }

        return top3Directories;
    }

}
