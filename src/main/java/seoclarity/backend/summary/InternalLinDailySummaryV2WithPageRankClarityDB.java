package seoclarity.backend.summary;

import java.net.InetAddress;
import java.net.UnknownHostException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.util.CollectionUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.ClarityDBUploadLogDAO;
import seoclarity.backend.dao.actonia.CrawlRequestLogDAO;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkImpl;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer1Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer2Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer3Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer4Dao;
import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer5Dao;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSiteCrawlDoc1Dao;
import seoclarity.backend.dao.mdbkeywordsuggest.InternalLinkPageRankInstanceEntityDAO;
import seoclarity.backend.entity.NodeStatusVO;
import seoclarity.backend.entity.actonia.ClarityDBUploadLogEntity;
import seoclarity.backend.entity.actonia.CrawlRequestLog;
import seoclarity.backend.entity.actonia.InternalLinkPageRankInstanceEntity;
import seoclarity.backend.entity.clickhouse.internallink.ProcessListVO;
import seoclarity.backend.utils.ClarityDBAPIUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.HttpRequestUtils;
import seoclarity.backend.utils.SpringBeanFactory;
// mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.InternalLinDailySummaryV2WithPageRankClarityDB" -Dexec.args=""
public class InternalLinDailySummaryV2WithPageRankClarityDB {
	
	private InternalLinkNewClusterServer4Dao internalLinkNewClusterServer4Dao;
	
	private InternalLinkPageRankInstanceEntityDAO internalLinkPageRankInstanceEntityDAO;
	
	private ClarityDBUploadLogDAO clarityDBUploadLogDAO;
	private CrawlRequestLogDAO crawlRequestLogDAO;
//	private CrawlRequestModifylog crawlRequestModifylog;
	
	private Gson gson = new Gson();
	
	private final static Integer DEFAULT_VERSION_FOR_INTERNAL_LINK = 0;
//	private static Integer version = DEFAULT_VERSION_FOR_INTERNAL_LINK;
//	private static Integer sepCrawlLogId;
//	private static Integer sepDomainId;
	
//	private static boolean reprocessFlag = false;
	
	private static final String databaseName = "actonia_internal_link";
	private static final String FINAL_TABLE_NAME = "dis_internal_link_sampled_view_final_page_rank";
	
	//RE-create this distribute table for api to query for temp data only
	
	//claritydb_upload_log id -- mdb010
	private static Integer logId;
	
	public InternalLinDailySummaryV2WithPageRankClarityDB(){
		internalLinkNewClusterServer4Dao = SpringBeanFactory.getBean("internalLinkNewClusterServer4Dao");
		internalLinkPageRankInstanceEntityDAO = SpringBeanFactory.getBean("internalLinkPageRankInstanceEntityDAO");
		clarityDBUploadLogDAO = SpringBeanFactory.getBean("clarityDBUploadLogDAO");
		crawlRequestLogDAO = SpringBeanFactory.getBean("crawlRequestLogDAO");
	}
	

	public static void main(String[] args) {
		
		
		InternalLinDailySummaryV2WithPageRankClarityDB internalLinkDailySummaryForClarityDB = new InternalLinDailySummaryV2WithPageRankClarityDB();
		internalLinkDailySummaryForClarityDB.init();
		internalLinkDailySummaryForClarityDB.process();
	}
	
	private void init() {
		ClarityDBUploadLogEntity clarityDBUploadLogEntity = new ClarityDBUploadLogEntity();
		
		clarityDBUploadLogEntity.setTmpTableName(FINAL_TABLE_NAME);
		clarityDBUploadLogEntity.setDatabaseName(databaseName);
		clarityDBUploadLogEntity.setFinalTableName(" ");
		clarityDBUploadLogEntity.setFinalTableUploadStatus(ClarityDBUploadLogEntity.FINAL_TABLE_STATUS_NEW);
		clarityDBUploadLogEntity.setUploadType(ClarityDBUploadLogEntity.UPLOAD_TYPE_INTERNAL_LINK_PAGE_RANK_SUMMARY);
		try {
			clarityDBUploadLogEntity.setServerIp(InetAddress.getLocalHost().getHostAddress());
		} catch (UnknownHostException e1) {
			// TODO Auto-generated catch block
			e1.printStackTrace();
		}
		
		clarityDBUploadLogEntity.setTmpTableUploadStartTime(new Date());
		clarityDBUploadLogEntity.setTmpTableUploadStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_PROCESSING);
		
		logId = clarityDBUploadLogDAO.insert(clarityDBUploadLogEntity);
	}
	
	private void process() {
		
		long startTime = System.currentTimeMillis();
		
		//step2. load summary data 
		try {
			List<InternalLinkPageRankInstanceEntity> resultList = internalLinkPageRankInstanceEntityDAO.getSummaryList();
			System.out.println("resultList size : " + (resultList!= null ? resultList.size() : 0));
			
			for(InternalLinkPageRankInstanceEntity vo : resultList) {
				
				Integer ownDomainId = vo.getOwnDomainId();
				Integer crawlRequestId = vo.getCrawlRequestLogId();
				
				ProcessListVO processListVO = internalLinkNewClusterServer4Dao.getCrawlInfoById(crawlRequestId, ownDomainId);
				
				if (processListVO == null) {
					System.out.println("Skip! Not found internal link summary data in CDB!");
					continue;
				}
				
				System.out.println("==== ownDomainId:" + ownDomainId + ", crawlRequestId:" + crawlRequestId);
				
				CrawlRequestLog crawlRequestLog = crawlRequestLogDAO.getCrawlerByOwnDomainIdAndCrawlId(ownDomainId, crawlRequestId);
				
				if (crawlRequestLog == null) {
					System.out.println("=== SKIP Crawl not found!");
					continue;
				}
				
				try {
					summary(processListVO);
					
					try {
						internalLinkPageRankInstanceEntityDAO.updateStatusById(ownDomainId, crawlRequestId, InternalLinkPageRankInstanceEntity.SUMMARY_STATUS_SUCCESS);
					} catch (Exception e) {
						e.printStackTrace();
					}
				} catch (Exception e) {
					e.printStackTrace();
					try {
						internalLinkPageRankInstanceEntityDAO.updateStatusById(ownDomainId, crawlRequestId, InternalLinkPageRankInstanceEntity.SUMMARY_STATUS_FAILURE);
					} catch (Exception e2) {
						// TODO: handle exception
					}
				}
				
			}
			
			long endTime = System.currentTimeMillis();
			int elapsedSeconds = (int) (endTime - startTime);
			
			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_SUCCESS, 0, elapsedSeconds, logId);
			
		} catch (Exception e) {
			e.printStackTrace();
			clarityDBUploadLogDAO.updateTmpTableStatus(ClarityDBUploadLogEntity.TMP_TABLE_STATUS_FAILURE, logId);
		}
	}
	
	private static final Integer LIMITATION_20M = 20000000;
	
	private void summary(ProcessListVO vo) {
		// -------------------------------  final  -----------------------------------
		
		System.out.println("@@@ processing to page rank table ");
		int partitionNum = 0;
		if (vo.getCnt() >= LIMITATION_20M) {
			partitionNum = (vo.getCnt() / LIMITATION_20M) + 1;
		}
		System.out.println("============== processing on crawlRequestId:" + vo.getCrawl_request_log_id_i() + ", ownDomainId:" + vo.getDomain_id_i());
		
		if (partitionNum == 0) {
			internalLinkNewClusterServer4Dao.summaryPageRank(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), null, null);
		} else {
			for(int i = 0; i < partitionNum; i ++) {
				System.out.println("Summary :" + (i + 1) + " of " + partitionNum);
				internalLinkNewClusterServer4Dao.summaryPageRank(vo.getDomain_id_i(), vo.getCrawl_request_log_id_i(), 
						partitionNum, i);
			}
		}
	
			
	}

}
