package seoclarity.backend.summary;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import seoclarity.backend.dao.clickhouse.contentidea.ContentIdeaImpl;
import seoclarity.backend.dao.clickhouse.contentidea.ContentIdeaPreSummaryDao;
import seoclarity.backend.dao.clickhouse.contentidea.ContentIdeaPreSummaryServer2Dao;
import seoclarity.backend.dao.clickhouse.contentidea.ContentIdeaPreSummaryServer3Dao;
import seoclarity.backend.dao.clickhouse.contentidea.ContentIdeaPreSummaryServer4Dao;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.summary.ContentIdeasDailySummaryKeywordForClarityDB" -Dexec.args=""
public class ContentIdeasDailySummaryKeywordForClarityDB {
	
	private ContentIdeaPreSummaryDao contentIdeaPreSummaryDao;
	private ContentIdeaPreSummaryServer2Dao contentIdeaPreSummaryServer2Dao;
	private ContentIdeaPreSummaryServer3Dao contentIdeaPreSummaryServer3Dao;
	private ContentIdeaPreSummaryServer4Dao contentIdeaPreSummaryServer4Dao;
	
	public ContentIdeasDailySummaryKeywordForClarityDB(){
		contentIdeaPreSummaryDao = SpringBeanFactory.getBean("contentIdeaPreSummaryDao");
		contentIdeaPreSummaryServer2Dao = SpringBeanFactory.getBean("contentIdeaPreSummaryServer2Dao");
		contentIdeaPreSummaryServer3Dao = SpringBeanFactory.getBean("contentIdeaPreSummaryServer3Dao");
		contentIdeaPreSummaryServer4Dao = SpringBeanFactory.getBean("contentIdeaPreSummaryServer4Dao");
	}
	
	
	private static String DEFAULT_VERSION = "8";
	private static String PROCESS_VERSION = DEFAULT_VERSION;
	
	private static final String FINAL_TABLE_NAME = "actonia_content_keyword_hashid_relation_template";
	private static final String TMP_TABLE_NAME = "actonia_content_keyword_hashid_relation_" + FormatUtils.formatDate(new Date(), "yyyyMMdd");
	private static final String HOURLY_TMP_LOCAL_TABLE_NAME = "local_actonia_content_keyword_hashid_rel_" + FormatUtils.formatDate(new Date(), "yyyyMMdd");
	private static final String LOCAL_TABLE_NAME_BACKUP = "local_actonia_content_keyword_hashid_rel_bk" + FormatUtils.formatDate(new Date(), "yyyyMMdd");
	private static final String LOCAL_TABLE_NAME = "local_actonia_content_keyword_hashid_relation";
	
	//claritydb_upload_log id -- mdb010
//	private static Integer logId;
	


	public static void main(String[] args) {
		ContentIdeasDailySummaryKeywordForClarityDB contentIdeasDailySummaryForClarityDB = new ContentIdeasDailySummaryKeywordForClarityDB();
		contentIdeasDailySummaryForClarityDB.process();
	}
	
	
//	private boolean checkIsProcessingUpload(){
//		
//		List<ClarityDBUploadLogEntity> list = clarityDBUploadLogDAO.getProcessingRecords(ClarityDBUploadLogEntity.UPLOAD_TYPE_INTERNAL_LINK_SUMMARY);
//		if (!CollectionUtils.isEmpty(list)) {
//			return true;
//		}
//		return false;
//	}
	
	private static List<ContentIdeaImpl> SERVER_LIST = new ArrayList<>();
	
	private void process() {
		
		SERVER_LIST.add(contentIdeaPreSummaryDao);
		SERVER_LIST.add(contentIdeaPreSummaryServer2Dao);
		SERVER_LIST.add(contentIdeaPreSummaryServer3Dao);
		SERVER_LIST.add(contentIdeaPreSummaryServer4Dao);
		
		// create dis table
		System.out.println(" Create dis summary table on server !!!");
		contentIdeaPreSummaryDao.createInsertDistributeTable(TMP_TABLE_NAME, FINAL_TABLE_NAME, HOURLY_TMP_LOCAL_TABLE_NAME);
		
		// summary
		try {
			
			List<String> needProcessList = contentIdeaPreSummaryDao.getKeywordHashGroupIdList(PROCESS_VERSION);
			System.out.println("needProcessList size : " + needProcessList!= null ? needProcessList.size() : 0);
			for(String hashSubStr : needProcessList) {
				contentIdeaPreSummaryDao.hashIdKeywordSummary(TMP_TABLE_NAME, PROCESS_VERSION, hashSubStr);
				
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			return ;
		}
		
		
		/**
		 * 1. check if new local table already exist
		 * 1. create new local table daily
		 * 2. create new dis table daily
		 * 3. summary data daily
		 * 4. drop old local table and rename new local table
		 * 5. drop new dis table
		 */
		
		for(ContentIdeaImpl contentIdeaImpl : SERVER_LIST) {
			
			System.out.println("Rename local table to backup table : " + contentIdeaImpl.getClass());
			System.out.println(contentIdeaImpl.renameTable(LOCAL_TABLE_NAME_BACKUP, LOCAL_TABLE_NAME));
			
			System.out.println("Rename tmp table to local table : " + contentIdeaImpl.getClass());
			System.out.println(contentIdeaImpl.renameTable(LOCAL_TABLE_NAME, HOURLY_TMP_LOCAL_TABLE_NAME));
			
		}
		
		for(ContentIdeaImpl contentIdeaImpl : SERVER_LIST) {
			
			try {
				contentIdeaImpl.dropTable(LOCAL_TABLE_NAME_BACKUP);
			} catch (Exception e) {
				e.printStackTrace();
			}
			
		}
		
		try {
			contentIdeaPreSummaryDao.dropTable(TMP_TABLE_NAME);
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	
}
