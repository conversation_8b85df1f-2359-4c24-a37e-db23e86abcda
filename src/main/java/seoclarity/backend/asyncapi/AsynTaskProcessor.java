package seoclarity.backend.asyncapi;

import java.io.File;
import java.io.FileWriter;
import java.nio.charset.StandardCharsets;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import com.alibaba.fastjson.JSONObject;
import com.amazonaws.auth.AWSCredentials;
import com.amazonaws.auth.BasicAWSCredentials;
import com.amazonaws.services.s3.AmazonS3;
import com.amazonaws.services.s3.AmazonS3Client;
import com.amazonaws.services.s3.model.CannedAccessControlList;
import com.amazonaws.services.s3.model.PutObjectRequest;
import com.amazonaws.services.s3.model.StorageClass;
import com.google.gson.Gson;
import seoclarity.backend.asyncapi.dailyRanking.DailyRankingKeywordDetailTopXProcessorV2;
import seoclarity.backend.asyncapi.topicexplorer.ExportTopicExplorerDetailProcessor;
import seoclarity.backend.dao.ApiTaskParamsEntityDAO;
import seoclarity.backend.dao.ExportUserInfoDAO;
import seoclarity.backend.dao.actonia.ApiTaskInfoEntityDAO;
import seoclarity.backend.dao.actonia.ApiTaskInstanceEntityDAO;
import seoclarity.backend.dao.actonia.CommonParamDAO;
import seoclarity.backend.dao.actonia.ExportInfoDAO;
import seoclarity.backend.entity.ApiTaskParamsEntity;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.bean.FTPServerInfoBean;
import seoclarity.backend.utils.*;
import seoclarity.backend.utils.amazon.AwsCredentialsEnvKeyConstructor;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.asyncapi.AsynTaskProcessor" -Dexec.args="102"
public class AsynTaskProcessor {
    private static boolean IS_DEBUG = false;
    private static final int TASK_INSTANCE_EXPIRE_DAYS = 2; // TODO
    private static final int DEFAULT_PROCESS_INTREVAL_SECONDS = 30; // TODO
    private static final int TASK_INFO_SYNC_INTERVAL_MINUTES = 120; // TODO

    private static final String SYNC_DOWNLOAD_CACHE_KEY_PREFIX = "asyncdownloadIn_"; // TODO
    private static final String MONTHS = "months";
    private static final String DOMAINS = "domains";
    private static final String LISTCHECKIDS = "listCheckId";
    private static final String HISTORY_PATH = "HistoricalFiles/";
    private static final String CUSTOM_FILE_NAME_KEY = "customFileName";
    private static List<Integer> ACTIVE_INSTANCE_STATUS_LIST;  // TODO https://www.wrike.com/open.htm?id=1060556010

    private static final int NO_CACHE_KEY_REPROCESS_INTERVAL_MINUTES = 5; // TODO
    private static final int ERROR_REPROCESS_INTERVAL_MINUTES = 10; // TODO

    private static final boolean SAVE_FILE_TO_SEAGATE = true;
    private static final boolean SAVE_FILE_TO_FTP = false;

    private final static String TASK_NAME_CONTENTGAP_ESTDTRAFFIC = "ContentGap_EstdTraffic"; // https://www.wrike.com/open.htm?id=908593946
    private final static String TASK_NAME_CONTENTGAP_COMPARERANK = "ContentGap_CompareRank"; // https://www.wrike.com/open.htm?id=913173890
    private final static String TASK_NAME_RESEARCHGRID_KEYWORDDETAIL = "ResearchGrid_KeywordDetail"; // https://www.wrike.com/open.htm?id=919475248
    private final static String TASKNAME_RESEARCHGRID_TOPPAGES = "ResearchGrid_TopPages"; // https://www.wrike.com/open.htm?id=922405732
    private final static String TASKNAME_RESEARCHGRID_RANKSUMMARY = "ResearchGrid_RankSummary"; // https://www.wrike.com/open.htm?id=922405358
    private final static String TASKNAME_RESEARCHGRID_PAGESTREND = "ResearchGrid_PageTrend"; // https://www.wrike.com/open.htm?id=922406027
    private final static String TASKNAME_RESEARCHGRID_CONTENTIDEA_PAA = "ResearchGrid_ContentIdea_PAA"; // https://www.wrike.com/open.htm?id=1108060968

    private final static String TASKNAME_RESEARCHGRID_DOMAINKEYWORDTREND = "ResearchGrid_DomainKeywordTrend";
    private final static String TASKNAME_RESEARCHGRID_KEYWORDDETAILTOPX = "ResearchGrid_KeywordDetailTopX";

    public final static String TASKNAME_CONTENTFUSION_KEYWORD = "Contentfusion_Keyword"; // https://www.wrike.com/open.htm?id=908593946
    public final static String TASKNAME_CONTENTFUSION_KEYWORD_REALTIME = "Contentfusion_Keyword_Realtime";
    public final static String TASKNAME_RESEARCHGRID_TOPICEXPLORER_KEYWORD = "TopicExplorer_Keyword";
    // 360 relation
    public final static String TASKNAME_CLARITY360_URL_DETAIL = "Clarity360_Page_Detail";
    public final static String TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL = "Clarity360_Page_DetailDownloadViaMail";

    public final static String TASKNAME_DAILYRANKING_KEYWORDDETAILTOPX = "DailyRanking_KeywordDetailTopX"; // https://www.wrike.com/open.htm?id=1589723934
    public final static String TASKNAME_DAILYRANKING_KEYWORDDETAIL_SERP_TOPX = "DailyRanking_KeywordDetailSerpTopX";


//    public static final String CH_DB_CLARITY360_URL = "http://23.105.177.38:8123";
    public static final String CH_DB_CLARITY360_URL = "http://65.60.17.6:8123";
    public static String CLARITY360_USER = "actoniacdb";
    public static String CLARITY360_PSW = "0%z=Kft99003k#Y6";
    public static final String CH_DB_CLARITY360 = "clarity360";
    private static final String LOCAL_STORE_CLARITY360_DIR = "/tmp/clarity360/";
    private static String LOG_CLARITY360_DIR = "/tmp/clarity360/log/";
    public static final int EXPORT_CLARITY360_PAGE_DETAIL = 109;
    private static final String NOT_EXIST_KEYWORDS = "notExistKeywordList";

    private static final String WORKER_JSON_PROPERTY_KEY_FILE_FORMAT = "fileFormate"; // TODO
    private static final String WORKER_JSON_PROPERTY_KEY_SQL = "sql";
    private static final String WORKER_JSON_PROPERTY_KEY_HEADER = "header";

    private static final String WORKER_JSON_PROPERTY_KEY_PAGE_SIZE = "pageSize";

    public static final String CH_DB_RGTOPIC_URL = "http://108.178.33.122:8123"; // TODO
    public static final String CH_DB_RG_URL = "http://23.105.14.193:8123"; // https://www.wrike.com/open.htm?id=1095553613
    public static final String CH_DB_RG_CENTRAL_URL = "http://cdb-central:8123"; // https://www.wrike.com/open.htm?id=1095553613
    public static final String CH_DB_RG_CENTRAL_URL_BK = "http://cdb-central-backup:8123"; // https://www.wrike.com/open.htm?id=1095553613
    public static final String CH_DB_RG = "monthly_ranking"; // TODO

    public static final String CDB_RI_URL = "http://cdb-ri-backup-first-external:8123";
    public static final String CH_DB_RI = "seo_daily_ranking";

    public static String USER = "default";
    public static String PSW = "clarity99!";
    public static String FILE_FORMATE_CSV = "CSV";// "format": "CSV",    -- optional            [CSV,JSON]
    public static String FILE_FORMATE_JSON = "JSON";
    public static String FAILED_TO_STORAGE_MESSAGE = "Failed to connect to specified server, upload to default server";
    private static String TARGETPATH = "/home/<USER>/public_html/tasks/";
    private static DateFormat SDF = new SimpleDateFormat("yyyy-MM-dd");
//    private static String URL = "http://downloads.seoclarity.net/tasks";
    private static String URL_SEPARATOR = "/";
    // private static String NOTASK = "Task is not ready";
    private static final String LOCAL_STORE_DIR = "/tmp/contentGap/";
    private static String LOG_DIR = "/tmp/contentGap/log/";
    private static String FAILED_SQL_LOG_NAME = "failed_sql.log";

    private static final String SEND_TO = "<EMAIL>";
    private static final String[] CC_TOS = new String[]{ "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>"};
    private static final SimpleDateFormat SDF_YYYYMMDDHHMMSS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final SimpleDateFormat SDF_MMDDHHMMSS = new SimpleDateFormat("MM-dd HH:mm:ss");

    private static final List<Integer> SINGLE_TASK_GROUPS = Arrays.asList(104);
    private boolean isProcessSingleTask = false;

    private Date taskInfoLastRefreshTime = new Date();
    private int processIntervalSeconds = DEFAULT_PROCESS_INTREVAL_SECONDS;
    private List<ApiTaskInfoEntity> taskInfoList;
    private Map<Integer, ApiTaskInfoEntity> taskInfoMap;

    private ZeptoMailSenderComponent zeptoMailSenderComponent;
    private ApiTaskInfoEntityDAO apiTaskInfoEntityDAO;
    private ApiTaskInstanceEntityDAO apiTaskInstanceEntityDAO;
    private ApiTaskParamsEntityDAO apiTaskParamsEntityDAO;
    // https://www.wrike.com/open.htm?id=1057201829
    private ExportInfoDAO exportInfoDAO;
    private ExportUserInfoDAO exportUserInfoDAO;
    private CommonParamDAO commonParamDAO;

    static {
        ACTIVE_INSTANCE_STATUS_LIST = new ArrayList<Integer>();
        ACTIVE_INSTANCE_STATUS_LIST.add(ApiTaskInstanceEntity.STATUS_CREATED);
        ACTIVE_INSTANCE_STATUS_LIST.add(ApiTaskInstanceEntity.STATUS_PROCESSING);
        ACTIVE_INSTANCE_STATUS_LIST.add(ApiTaskInstanceEntity.STATUS_ERR0R);
    }

    public AsynTaskProcessor() {
        zeptoMailSenderComponent = SpringBeanFactory.getBean("zeptoMailSenderComponent");
        apiTaskInfoEntityDAO = SpringBeanFactory.getBean("apiTaskInfoEntityDAO");
        apiTaskInstanceEntityDAO = SpringBeanFactory.getBean("apiTaskInstanceEntityDAO");
        apiTaskParamsEntityDAO = SpringBeanFactory.getBean("apiTaskParamsEntityDAO");
        // https://www.wrike.com/open.htm?id=1057201829
        exportInfoDAO = SpringBeanFactory.getBean("exportInfoDAO");
        exportUserInfoDAO = SpringBeanFactory.getBean("exportUserInfoDAO");
        commonParamDAO = SpringBeanFactory.getBean("commonParamDAO");
        File path = new File(LOG_DIR);
        if (!path.exists()) path.mkdirs();
        path = new File(LOCAL_STORE_DIR);
        if (!path.exists()) path.mkdirs();
        // https://www.wrike.com/open.htm?id=1057201829
        path = new File(LOG_CLARITY360_DIR);
        if (!path.exists()) path.mkdirs();
        path = new File(LOCAL_STORE_CLARITY360_DIR);
        if (!path.exists()) path.mkdirs();
    }

    public static void main(String args[]) {
        List<String> taskGroups = null;
        if (args.length > 0 && StringUtils.isNotBlank(args[0])) {
            String taskGroupsStr = args[0];
            taskGroups = Arrays.asList(taskGroupsStr.split(","));
        } else {
            System.out.println("Exit for invalid parameter taskGroup:" + args[0]);
            return;
        }
        System.out.println("===========Parameters taskGroup:" + args[0]);
        AsynTaskProcessor ins = new AsynTaskProcessor();
        if(IS_DEBUG){
            ins.processDebug(Integer.parseInt(args[0]), null);
        } else if (args.length > 1 && Boolean.valueOf(args[1])) {
            // 0:instantId, 1: true/false debug
            IS_DEBUG = Boolean.valueOf(args[1]);
            ins.processDebug(Integer.parseInt(args[0]), Arrays.asList("101","104"));
        } else {
            ins.process(taskGroups);
        }
    }

    private void reFreshTaskInfoList(List<String> taskGroupList, boolean forceRefresh) {
        boolean needRefresh = false;
        if (taskInfoList == null || forceRefresh) {
            needRefresh = true;
        } else if ((int) (new Date().getTime() - taskInfoLastRefreshTime.getTime()) / (60 * 1000) >= TASK_INFO_SYNC_INTERVAL_MINUTES) {
            needRefresh = true;
        }
        if (needRefresh) {
            taskInfoList = apiTaskInfoEntityDAO.getListByTaskGroups(taskGroupList);
            taskInfoMap = new HashMap<Integer, ApiTaskInfoEntity>();
            if (taskInfoList != null && taskInfoList.size() > 0) {
                // for single task, only process ont task
                if (taskGroupList.size() == 1 && SINGLE_TASK_GROUPS.contains(Integer.parseInt(taskGroupList.get(0)))) {
                    isProcessSingleTask = true;
                } else {
                    isProcessSingleTask = false;
                }

                for (ApiTaskInfoEntity taskInfoEntity : taskInfoList) {
                    taskInfoMap.put(taskInfoEntity.getId(), taskInfoEntity);
                    if (processIntervalSeconds == 0 || processIntervalSeconds > taskInfoEntity.getCheckIntervalSeconds()) {
                        processIntervalSeconds = taskInfoEntity.getCheckIntervalSeconds();
                    }
                }
            }
            Date now = new Date();
            System.out.println("==================================================================================================");
            System.out.println(" ====reFreshTaskInfoListAt:" + SDF_YYYYMMDDHHMMSS.format(now) + " taskInfos:" + taskInfoList.size() +
                    " taskInfoMap:" + taskInfoMap.size() + " processIntervalSeconds:" + processIntervalSeconds + " lastRefreshAt:" + taskInfoLastRefreshTime + ", isProcessSingleTask:" + isProcessSingleTask);
            taskInfoLastRefreshTime = now;
        }
    }

    private void process(List<String> taskGroupList) {
        reFreshTaskInfoList(taskGroupList, true);
        if (taskInfoList == null || taskInfoList.size() == 0) {
            System.out.println("Exit for no taskInfo(taskGroup:" + taskGroupList + ")");
            return;
        }
        Date currTime = new Date();
        System.out.println("======StartProcessAsyncTaskAt:" + SDF_YYYYMMDDHHMMSS.format(currTime) + " taskInfos:" + taskInfoList.size() + " activeTaskStatus:" +
                ACTIVE_INSTANCE_STATUS_LIST + " intervalSeconds:" + processIntervalSeconds + " instanceActiveDays:" + TASK_INSTANCE_EXPIRE_DAYS);

        while (true) {
            try {
                List<ApiTaskInstanceEntity> newTaskInstanceList = getActiveTaskListFromWorkerKey(SYNC_DOWNLOAD_CACHE_KEY_PREFIX);
                if (newTaskInstanceList != null && newTaskInstanceList.size() > 0) {
                    System.out.println("====FoundNewTaskAt:" + SDF_MMDDHHMMSS.format(currTime) + " cnt:" + newTaskInstanceList.size());
                    processTaskInstances(newTaskInstanceList); // TODO
                } else {
                    System.out.println("====NoNewTaskAt:" + SDF_MMDDHHMMSS.format(currTime));
                }

//                List<ApiTaskInstanceEntity> activeTaskInstanceList = apiTaskInstanceEntityDAO.getActiveTaskInstanceList(ACTIVE_INSTANCE_STATUS_LIST,`
//                    DateUtils.addDays(currTime, -TASK_INSTANCE_EXPIRE_DAYS));
//                if (activeTaskInstanceList != null && activeTaskInstanceList.size() > 0) {
//                    System.out.println("===FoundActiveTaskAt:" + SDF_MMDDHHMMSS.format(currTime) + " cnt:" + activeTaskInstanceList.size());
//                    processTaskInstances(activeTaskInstanceList); // TODO
//                } else {
//                    System.out.println("===NoActiveTask at:" + SDF_MMDDHHMMSS.format(currTime));
//                }
                try {
                    Thread.sleep(processIntervalSeconds * 1000);
                } catch (Exception exp) {
                    exp.printStackTrace();
                }

                currTime = new Date();
                reFreshTaskInfoList(taskGroupList, false);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
    }

    private void processDebug(int instantId, List<String> groups) {
        //reFreshTaskInfoList(taskGroupList, true);
        System.out.println("===========instantId is :" + instantId);
        Date currTime = new Date();
        System.out.println("=====instantId:" +instantId);
        //System.out.println("======StartProcessAsyncTaskAt:" + SDF_YYYYMMDDHHMMSS.format(currTime) + " taskInfos:" + taskInfoList.size() + " activeTaskStatus:" +
        //        ACTIVE_INSTANCE_STATUS_LIST + " intervalSeconds:" + processIntervalSeconds + " instanceActiveDays:" + TASK_INSTANCE_EXPIRE_DAYS);
        try {
            groups = groups == null || groups.size() == 0 ? Arrays.asList("101") : groups;
            reFreshTaskInfoList(groups,true);
            List<ApiTaskInstanceEntity> activeTaskInstanceList = apiTaskInstanceEntityDAO.getTaskInstanceListById(instantId);
            if (activeTaskInstanceList != null && activeTaskInstanceList.size() > 0) {
                System.out.println("===FoundActiveTaskAt:" + SDF_MMDDHHMMSS.format(currTime) + " cnt:" + activeTaskInstanceList.size());
                processTaskInstances(activeTaskInstanceList); // TODO
            } else {
                System.out.println("===NoActiveTask at:" + SDF_MMDDHHMMSS.format(currTime));
            }
            try {
                Thread.sleep(processIntervalSeconds * 1000);
            } catch (Exception exp) {
                exp.printStackTrace();
            }

            currTime = new Date();
            //reFreshTaskInfoList(taskGroupList, false);
        } catch (Exception ex) {
            ex.printStackTrace();
        }

    }

    private List<ApiTaskInstanceEntity> getActiveTaskListFromWorkerKey(String paramKeyPrefix) {
        try {
            List<String> cacheKeyList = TaskUtil.listKeys(paramKeyPrefix);
            if (cacheKeyList != null && cacheKeyList.size() > 0) {
                System.out.println("===FoundMatchedKey prefix:" + paramKeyPrefix + " cnt:" + cacheKeyList.size());
                List<ApiTaskInstanceEntity> candidateInstanceList = new ArrayList<ApiTaskInstanceEntity>();
                for (String cacheKey : cacheKeyList) {
                    int taskInfoId = 0;
                    String taskId = null;
                    for (ApiTaskInfoEntity taskInfoEntity : taskInfoList) {
                        String cacheKeyPrefix = taskInfoEntity.getCacheKeyPrefix();
                        if (cacheKey.startsWith(cacheKeyPrefix)) {
                            taskInfoId = taskInfoEntity.getId();
                            taskId = cacheKey.substring(cacheKeyPrefix.length());
                            break;
                        }
                    }
                    if (taskInfoId > 0) {
                        ApiTaskInstanceEntity instanceEntity = apiTaskInstanceEntityDAO.getTaskInstanceByUniqueKey(taskInfoId, taskId);
                        if (instanceEntity != null) {
                            if (isProcessSingleTask) {
                                sleep(10);
                                instanceEntity = apiTaskInstanceEntityDAO.getTaskInstanceByUniqueKey(taskInfoId, taskId);
                                if (candidateInstanceList.size() == 0 && (instanceEntity.getStatus() == ApiTaskInstanceEntity.STATUS_ERR0R || instanceEntity.getStatus() == ApiTaskInstanceEntity.STATUS_CREATED)) {
                                    System.out.println(" ==GotTaskInstance " + instanceEntity.getId() + "(info:" + taskInfoId + ") status:" + instanceEntity.getStatus() +
                                            " createdAt:" + SDF_YYYYMMDDHHMMSS.format(instanceEntity.getCreatedAt()) + " cacheKey:" + cacheKey + " taskId:" + taskId);
                                    candidateInstanceList.add(instanceEntity);
                                } else {
                                    System.out.println(" ==SkipTaskInstance " + instanceEntity.getId() + "(info:" + taskInfoId + ") status:" + instanceEntity.getStatus() +
                                            " createdAt:" + SDF_YYYYMMDDHHMMSS.format(instanceEntity.getCreatedAt()) + " cacheKey:" + cacheKey + " taskId:" + taskId
                                            +", candidateInstanceList:" + candidateInstanceList.stream().map(ApiTaskInstanceEntity::getId).collect(Collectors.toList()));
                                }
                            } else if (ACTIVE_INSTANCE_STATUS_LIST.contains(instanceEntity.getStatus())) {
                                System.out.println(" ==GotTaskInstance " + instanceEntity.getId() + "(info:" + taskInfoId + ") status:" + instanceEntity.getStatus() +
                                        " createdAt:" + SDF_YYYYMMDDHHMMSS.format(instanceEntity.getCreatedAt()) + " cacheKey:" + cacheKey + " taskId:" + taskId);
                                candidateInstanceList.add(instanceEntity);
                            } else {
                                System.out.println(" ==SkipTaskInstance " + instanceEntity.getId() + "(info:" + taskInfoId + ") status:" + instanceEntity.getStatus() +
                                        " createdAt:" + SDF_YYYYMMDDHHMMSS.format(instanceEntity.getCreatedAt()) + " cacheKey:" + cacheKey + " taskId:" + taskId
                                        +", candidateInstanceList:" + candidateInstanceList.stream().map(ApiTaskInstanceEntity::getId).collect(Collectors.toList()));
                            }
                        }
                    }
                }
                return candidateInstanceList;
            }
        } catch (Exception exp) {
            exp.printStackTrace();
        }
        return null;
    }

    private void processTaskInstances(List<ApiTaskInstanceEntity> activeTaskInstanceList) {
        for (ApiTaskInstanceEntity taskInstanceEntity : activeTaskInstanceList) {
            try {
                int taskInfoId = taskInstanceEntity.getTaskInfoId();
                ApiTaskInfoEntity apiTaskInfoEntity = taskInfoMap.get(taskInfoId);
                if (apiTaskInfoEntity == null) {
                    System.out.println("======SkipForNoTaskInfoInMap:" + taskInfoId);
                    continue;
                }

                if (!IS_DEBUG){
                    taskInstanceEntity = apiTaskInstanceEntityDAO.getTaskInstanceById(taskInstanceEntity.getId());
                    if (!shouldProcess(taskInstanceEntity)) {
                        continue;
                    }
                }

                String taskName = apiTaskInfoEntity.getTaskName();
                System.out.println("==ProcessTask id:" + taskInstanceEntity.getId() + "(info:" + taskInfoId + " taskName:" + taskName + ") status:" + taskInstanceEntity.getStatus() + ", currentDate:" + new Date());

                switch (taskName) {
                    case TASK_NAME_CONTENTGAP_ESTDTRAFFIC:
                    case TASK_NAME_CONTENTGAP_COMPARERANK:
                    case TASK_NAME_RESEARCHGRID_KEYWORDDETAIL:
                        asynExportFile(apiTaskInfoEntity, taskInstanceEntity,taskName);
                        break;
                    case TASKNAME_RESEARCHGRID_TOPPAGES:
                    case TASKNAME_RESEARCHGRID_RANKSUMMARY:
                    case TASKNAME_RESEARCHGRID_PAGESTREND:
                    case TASKNAME_RESEARCHGRID_CONTENTIDEA_PAA:
                        asynExportFile(apiTaskInfoEntity, taskInstanceEntity);
                        break;
                    case TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL:
                        asynExportFile(apiTaskInfoEntity, taskInstanceEntity, TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL);
                        break;
                    case TASKNAME_RESEARCHGRID_TOPICEXPLORER_KEYWORD:
                        asynExportFile(apiTaskInfoEntity, taskInstanceEntity,TASKNAME_RESEARCHGRID_TOPICEXPLORER_KEYWORD);
                        break;
                    case TASKNAME_RESEARCHGRID_DOMAINKEYWORDTREND:
                        asynExportFile(apiTaskInfoEntity, taskInstanceEntity,TASKNAME_RESEARCHGRID_DOMAINKEYWORDTREND);
                        break;
                    case TASKNAME_RESEARCHGRID_KEYWORDDETAILTOPX:
                        asynExportFile(apiTaskInfoEntity, taskInstanceEntity,TASKNAME_RESEARCHGRID_KEYWORDDETAILTOPX);
                        break;
                    case TASKNAME_CLARITY360_URL_DETAIL:
                        asynExportFile(apiTaskInfoEntity, taskInstanceEntity,TASKNAME_CLARITY360_URL_DETAIL);
                        break;
                    case TASKNAME_DAILYRANKING_KEYWORDDETAILTOPX:
                        asynExportFile(apiTaskInfoEntity, taskInstanceEntity,TASKNAME_DAILYRANKING_KEYWORDDETAILTOPX);
                        break;
                    case TASKNAME_DAILYRANKING_KEYWORDDETAIL_SERP_TOPX:
                        asynExportFile(apiTaskInfoEntity, taskInstanceEntity,TASKNAME_DAILYRANKING_KEYWORDDETAIL_SERP_TOPX);
                        break;
//                    case TASKNAME_CONTENTFUSION_KEYWORD:
//                    case TASKNAME_CONTENTFUSION_KEYWORD_REALTIME:
//                        ContentFusionHandler.handleContentFusionApi(apiTaskInfoEntity);
//                        break;
                }

                try {
                    Thread.sleep(1000);
                } catch (Exception exp) {
                    exp.printStackTrace();
                }
            } catch (Exception exp) {
                exp.printStackTrace();
            }
        }
    }

    private boolean shouldProcess(ApiTaskInstanceEntity taskInstanceEntity) {
        if (taskInstanceEntity.getUpdateDate() != null) { // TODO
            int elapsedMinutesFromLastUpdate = (int) (new Date().getTime() - taskInstanceEntity.getUpdateDate().getTime()) / (60 * 1000);
            int reprocessIntervalMinutes = (ApiTaskInstanceEntity.STATUS_ERR0R == taskInstanceEntity.getStatus() ? ERROR_REPROCESS_INTERVAL_MINUTES : NO_CACHE_KEY_REPROCESS_INTERVAL_MINUTES);
            if (elapsedMinutesFromLastUpdate < reprocessIntervalMinutes) {
                System.out.println(" ==SkipTask:" + taskInstanceEntity.getId() + " status:" + taskInstanceEntity.getStatus() + " created:" +
                        SDF_YYYYMMDDHHMMSS.format(taskInstanceEntity.getCreatedAt()) + " updAt:" + SDF_YYYYMMDDHHMMSS.format(taskInstanceEntity.getUpdateDate()) +
                        " now:" + SDF_YYYYMMDDHHMMSS.format(new Date()));
                return false;
            }
        }
        if (taskInstanceEntity.getStatus() == ApiTaskInstanceEntity.STATUS_PROCESSING) {
            System.out.println(" ==SkipTask:" + taskInstanceEntity.getId() + " status:" + taskInstanceEntity.getStatus() + " created:" +
                    SDF_YYYYMMDDHHMMSS.format(taskInstanceEntity.getCreatedAt()) + " updAt:" + SDF_YYYYMMDDHHMMSS.format(taskInstanceEntity.getUpdateDate()) +
                    " now:" + SDF_YYYYMMDDHHMMSS.format(new Date()));
            return false;
        }
        return true;
    }

    // https://www.wrike.com/open.htm?id=1057201829
    private void asynExportFile(ApiTaskInfoEntity apiTaskInfoEntity, ApiTaskInstanceEntity apiTaskInstanceEntity) {
        asynExportFile(apiTaskInfoEntity, apiTaskInstanceEntity, null);
    }

    private void asynExportFile(ApiTaskInfoEntity apiTaskInfoEntity, ApiTaskInstanceEntity apiTaskInstanceEntity,String functionName) {
        try {
            System.out.println("=====funcname:"+functionName);
            int taskInstanceId = apiTaskInstanceEntity.getId();
            int ownDomainId = apiTaskInstanceEntity.getOwnDomainId();
            String taskId = apiTaskInstanceEntity.getTaskId();
            String cacheKey = apiTaskInfoEntity.getCacheKeyPrefix() + taskId;
            System.out.println(" ==ProcessTask at " + SDF_YYYYMMDDHHMMSS.format(new Date()) + " insId:" + taskInstanceId + "(info:" + apiTaskInfoEntity.getId() +
                    " OID:" + ownDomainId + ") status:" + apiTaskInstanceEntity.getStatus() + " createdAt:" +
                    SDF_YYYYMMDDHHMMSS.format(apiTaskInstanceEntity.getCreatedAt()) + " cacheKey:" + cacheKey);
            Map<String, Object> workerParameterMap =null;
            String resultStr = TaskUtil.get(cacheKey.getBytes(StandardCharsets.UTF_8));
            System.out.println("=====resultStr:"+resultStr);
            String fileFormate = "";
            if(!IS_DEBUG) {
                if (StringUtils.isBlank(resultStr) || StringUtils.containsIgnoreCase(resultStr, WORKER_JSON_PROPERTY_KEY_FILE_FORMAT) == false
                        || StringUtils.containsIgnoreCase(resultStr, WORKER_JSON_PROPERTY_KEY_SQL) == false) { // TODO
                    System.out.println(" ####Abnormal NoTaskInCache:" + cacheKey + " instanceId:" + taskInstanceId + " resultStr:" + resultStr + " createTime:" +
                            SDF_YYYYMMDDHHMMSS.format(apiTaskInstanceEntity.getCreatedAt()) + " currTime:" + SDF_YYYYMMDDHHMMSS.format(new Date()));
                    return;
                }
            }
            workerParameterMap = JSONObject.parseObject(resultStr, Map.class);
            String customFileName = "";
            if(workerParameterMap!=null && workerParameterMap.get(CUSTOM_FILE_NAME_KEY)!=null
                    && StringUtils.isNotBlank( workerParameterMap.get(CUSTOM_FILE_NAME_KEY).toString())){
                customFileName = workerParameterMap.get(CUSTOM_FILE_NAME_KEY).toString();
            }
            System.out.println("=====customFileName:"+customFileName);
            String topFlag ="";
//            if(StringUtils.isNotBlank(resultStr) && !IS_DEBUG){
                System.out.println("=====resultStr:"+resultStr);
                fileFormate = (String) workerParameterMap.get(WORKER_JSON_PROPERTY_KEY_FILE_FORMAT);
                if( StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_RESEARCHGRID_KEYWORDDETAILTOPX,functionName)){
                     topFlag = (String) workerParameterMap.get("topFlag");
                    if(StringUtils.isNotBlank(topFlag) && StringUtils.equalsIgnoreCase("topFlag",functionName)){
                        functionName =TASKNAME_RESEARCHGRID_KEYWORDDETAILTOPX;
                    }
                }
                System.out.println(" ##UpdToProcessing insId:" + taskInstanceId + " json:" + resultStr);
//            }
            apiTaskInstanceEntityDAO.updateProcessStartTime(taskInstanceId, ApiTaskInstanceEntity.STATUS_PROCESSING, DateTime.now().toDate(), new Date());

            List<String> multiExportList = new ArrayList<>(0);
            List<String> exportFileUrlList = new ArrayList<>(0);
            if(IS_DEBUG){
                //fileFormate = "json";
            }
            boolean isFileFormatCSV = FILE_FORMATE_JSON.equalsIgnoreCase(fileFormate) ? false : true;
            String fullFileName = "";
            if (StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL, functionName)) {
                fullFileName = getLocalFullPathFileName(apiTaskInfoEntity, apiTaskInstanceEntity, fileFormate, TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL,customFileName);
            } else if (StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_CLARITY360_URL_DETAIL, functionName)) {
                fullFileName = getLocalFullPathFileName(apiTaskInfoEntity, apiTaskInstanceEntity, fileFormate,TASKNAME_CLARITY360_URL_DETAIL,customFileName);
            } else if(StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_RESEARCHGRID_TOPICEXPLORER_KEYWORD, functionName)){
                fullFileName = getLocalFullPathFileName(apiTaskInfoEntity, apiTaskInstanceEntity, fileFormate,TASKNAME_RESEARCHGRID_TOPICEXPLORER_KEYWORD,customFileName);
            }else if(StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASK_NAME_RESEARCHGRID_KEYWORDDETAIL, functionName)){
                fullFileName = getLocalFullPathFileName(apiTaskInfoEntity, apiTaskInstanceEntity, fileFormate,TASK_NAME_RESEARCHGRID_KEYWORDDETAIL,customFileName);
            } else if(StringUtils.equalsIgnoreCase(TASKNAME_DAILYRANKING_KEYWORDDETAIL_SERP_TOPX, functionName) || StringUtils.equalsIgnoreCase(TASKNAME_DAILYRANKING_KEYWORDDETAILTOPX, functionName)){ //
                fullFileName = getLocalFullPathFileName(apiTaskInfoEntity, apiTaskInstanceEntity, fileFormate, functionName, customFileName);
            }
            else {
                fullFileName = getLocalFullPathFileName(apiTaskInfoEntity, apiTaskInstanceEntity, fileFormate,functionName,customFileName);
            }

            // Export data from ClarityDB and save into local file, then zip file
            System.out.println(" ###ExpFromCH insId:" + taskInstanceId + " cacheKey:" + cacheKey + " localFile:" + fullFileName);
            int exportRowsCount = 0;
            if (StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL, functionName)) {
                exportRowsCount = exportFromClarityDB(apiTaskInfoEntity, apiTaskInstanceEntity, cacheKey, resultStr, workerParameterMap, fullFileName, isFileFormatCSV, TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL);
            } else if (StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_CLARITY360_URL_DETAIL, functionName)) {
                exportRowsCount = exportFromClarityDB(apiTaskInfoEntity, apiTaskInstanceEntity, cacheKey, resultStr, workerParameterMap, fullFileName, isFileFormatCSV,TASKNAME_CLARITY360_URL_DETAIL);
            } else if(StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_RESEARCHGRID_TOPICEXPLORER_KEYWORD, functionName)) {
                exportRowsCount = exportFromClarityDB(apiTaskInfoEntity, apiTaskInstanceEntity, cacheKey, resultStr, workerParameterMap, fullFileName, isFileFormatCSV,TASKNAME_RESEARCHGRID_TOPICEXPLORER_KEYWORD);
            }
            // https://www.wrike.com/open.htm?id=1247023765
            else if(StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_RESEARCHGRID_KEYWORDDETAILTOPX, functionName)&& StringUtils.isBlank(topFlag)) {
                exportRowsCount = exportFromClarityDB(apiTaskInfoEntity, apiTaskInstanceEntity, cacheKey, resultStr, workerParameterMap, fullFileName, isFileFormatCSV,TASKNAME_RESEARCHGRID_DOMAINKEYWORDTREND);
            }

            // https://www.wrike.com/open.htm?id=1250106697
            else if(StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_RESEARCHGRID_KEYWORDDETAILTOPX, functionName)&& StringUtils.isNotBlank(topFlag) ) {
                exportRowsCount = exportFromClarityDB(apiTaskInfoEntity, apiTaskInstanceEntity, cacheKey, resultStr, workerParameterMap, fullFileName, isFileFormatCSV,TASKNAME_RESEARCHGRID_KEYWORDDETAILTOPX);
            }
            else if(StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASK_NAME_RESEARCHGRID_KEYWORDDETAIL, functionName)) {
                exportRowsCount = exportFromClarityDB(apiTaskInfoEntity, apiTaskInstanceEntity, cacheKey, resultStr, workerParameterMap, fullFileName, isFileFormatCSV,TASK_NAME_RESEARCHGRID_KEYWORDDETAIL);
            }
            else if(StringUtils.equalsIgnoreCase(TASKNAME_DAILYRANKING_KEYWORDDETAILTOPX, functionName) || StringUtils.equalsIgnoreCase(TASKNAME_DAILYRANKING_KEYWORDDETAIL_SERP_TOPX, functionName)) {
                exportRowsCount = exportFromClarityDBForMultipleFiles(apiTaskInfoEntity, apiTaskInstanceEntity, cacheKey, resultStr, workerParameterMap, fullFileName, isFileFormatCSV,functionName , multiExportList);
            }
            else{
                exportRowsCount = exportFromClarityDB(apiTaskInfoEntity, apiTaskInstanceEntity, cacheKey, resultStr, workerParameterMap, fullFileName, isFileFormatCSV);
            }
            if (exportRowsCount < 0) {
                return;
            }
            String fullNameZip = "";
            if (StringUtils.equalsIgnoreCase(TASKNAME_DAILYRANKING_KEYWORDDETAILTOPX, functionName) || StringUtils.equalsIgnoreCase(TASKNAME_DAILYRANKING_KEYWORDDETAIL_SERP_TOPX, functionName)) {
                // just fill the fullNameZip, all export are inert to multiExportList
                fullNameZip = String.join(".", fullFileName, "zip");
            } else if(!StringUtils.equals(functionName, TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL) && !StringUtils.equals(functionName, TASKNAME_CLARITY360_URL_DETAIL)){
                fullNameZip = String.join(".", fullFileName, "gz");
            } else {
                if (exportRowsCount <= 20000000) {
                    fullNameZip = String.join(".", fullFileName, "zip");
                } else {
                    fullNameZip = fullFileName;
                }
            }

            File localZipFile = new File(fullNameZip);
            String zipFileName = localZipFile.getName();

            String presignedSeagateUrl = null;
            if (SAVE_FILE_TO_SEAGATE) {
                if (multiExportList.size() > 0) {
                    multiExportList.forEach(fileName -> {
                        SeagateUtils.saveFileToDefaultSeagate(ownDomainId, fileName);
                        String url = null;
                        try {
                            File f = new File(fileName);
                            url = SeagateUtils.getDefaultSeagatePresignedUrl(ownDomainId, f.getName());
                            if (StringUtils.isBlank(url)) {
                                System.out.println(" ##FailedToPresignURL instance:" + taskInstanceId + " OID:" + ownDomainId + " file:" + fileName);
                            } else {
                                exportFileUrlList.add(url);
                            }
                        } catch (Exception e) {
                            System.out.println(" ##FailedToPresignURL instance:" + taskInstanceId + " OID:" + ownDomainId + " file:" + fileName);
                        }
                    });
                } else {
                    if (SeagateUtils.saveFileToDefaultSeagate(ownDomainId, fullNameZip) == false) { // save output file to Seagate
                        System.out.println(" ##FailedToSaveFileToSeagate instance:" + taskInstanceId + " OID:" + ownDomainId + " file:" + fullNameZip);
                        return;
                    }
                    presignedSeagateUrl = SeagateUtils.getDefaultSeagatePresignedUrl(ownDomainId, zipFileName); // Get pre-signed URL from seagate
                    if (StringUtils.isEmpty(presignedSeagateUrl)) {
                        System.out.println(" ##FailedToPresignURL instance:" + taskInstanceId + " OID:" + ownDomainId + " file:" + fullNameZip);
                        return;
                    }
                }
            }

            String defaultFtpDownloadLinkUrl = null;
//            if (SAVE_FILE_TO_FTP) {
//                if (saveFileToDefaultFTP(localZipFile, TARGETPATH) == false) { // save output file to FTP
//                    System.out.println(" ##FailedToSaveFileToDefaultFTP:" + fullNameZip + "->" + TARGETPATH);
//                    //return; // TODO
//                    // https://www.wrike.com/open.htm?id=1265177517
//                    apiTaskInstanceEntityDAO.updateProcessEndTimeV1(taskInstanceId, ApiTaskInstanceEntity.STATUS_ERR0R, DateTime.now().toDate(), 0,
//                           null, 0, new Date());
//                    return;
//                }
//                defaultFtpDownloadLinkUrl = String.join(URL_SEPARATOR, URL, zipFileName);
//            }

            List<String> responseFileLinkList = new ArrayList<String>();
            if (multiExportList.size() > 0) {
                responseFileLinkList.addAll(exportFileUrlList);
            } else if (StringUtils.isNotBlank(presignedSeagateUrl)) {
                responseFileLinkList.add(presignedSeagateUrl);
            } else if (StringUtils.isNotBlank(defaultFtpDownloadLinkUrl)) {
                responseFileLinkList.add(defaultFtpDownloadLinkUrl);
            }

            // get storageId settings from worker Json. key:"commonParamEntity"
            CommonParamEntity commonParamEntity = getCommonParamEntityFromWorkerParameter(workerParameterMap, "commonParamEntity");
            System.out.println("=====commonParamEntity:"+JSONUtil.toJsonStr(commonParamEntity));
            String[] resPath = new String[1];
            List<String> resPathForMultiFiles = new ArrayList<>(0);
            if (commonParamEntity != null) {
                long commonParamEntityId = commonParamEntity.getId();
                commonParamEntity = commonParamDAO.getById(commonParamEntityId);
                // save output file to client specified storage according to storageId(It is ok even if failed to save)
                String[] errors = new String[1];
                if (commonParamEntity != null && multiExportList.size() > 0) {
                    CommonParamEntity commonParamEntityTemp = commonParamEntity;
                    multiExportList.forEach(fileName -> {
                        File f = new File(fileName);
                        saveFileToClientSpecifiedStorage(commonParamEntityTemp.getParamJson(), commonParamEntityTemp.getTitle(), commonParamEntityTemp.getFuncName(), f, errors, resPath);
                        String specifiedStorageFileUrl = SeagateUtils.getSpecifiedStorageFileUrl(commonParamEntityTemp, f.getName());
                        if (StringUtils.isNotBlank(specifiedStorageFileUrl)) {
                            responseFileLinkList.add(specifiedStorageFileUrl);
                        }
                        System.out.println("=====GET FROM DB commonParamEntity:"+JSONUtil.toJsonStr(commonParamEntityTemp));
                        System.out.println("=====:"+ resPath);
                        resPathForMultiFiles.add(resPath[0]);
                    });
                } else if (commonParamEntity!=null && (saveFileToClientSpecifiedStorage(commonParamEntity.getParamJson(), commonParamEntity.getTitle(),
                        commonParamEntity.getFuncName(), localZipFile,errors,resPath))) {
                    System.out.println("=====GET FROM DB commonParamEntity:"+JSONUtil.toJsonStr(commonParamEntity));
                    System.out.println("=====:"+ resPath);
                    String specifiedStorageFileUrl = SeagateUtils.getSpecifiedStorageFileUrl(commonParamEntity, zipFileName);
                    if (StringUtils.isNotBlank(specifiedStorageFileUrl)) {
                        responseFileLinkList.add(specifiedStorageFileUrl);
                    }
                }else{
                    System.out.println("=====seagate failed,but default is success");
                    apiTaskInstanceEntityDAO.updateProcessEndTime_OptionalUploadFail(taskInstanceId, ApiTaskInstanceEntity.STATUS_SUCCESS, DateTime.now().toDate(), 0,
                            null, 0, new Date(),errors);
                    //return;
                }
            }

            String outTaskKey = apiTaskInfoEntity.getTaskKeyPrefix() + taskId;
            Event event = constructResponseEvent(apiTaskInstanceEntity, commonParamEntity, zipFileName, responseFileLinkList, exportRowsCount);
            if (resPathForMultiFiles.stream().filter(StringUtils::isNotBlank).count() > 0) {
                event.setGsPath(new Gson().toJson(resPathForMultiFiles.stream().map(str -> "gs://" + str).collect(Collectors.toList())));
            } else if(resPath.length > 0 && StringUtils.isNotBlank(resPath[0])){
                event.setGsPath("gs://"+resPath[0]);
            }
            System.out.println(" ##SaveOutKey insId:" + taskInstanceId + " key:" + outTaskKey + " event:" + JSONObject.toJSONString(event));
            TaskUtil.setValue(outTaskKey.getBytes(StandardCharsets.UTF_8), JSONObject.toJSONString(event));

            System.out.println(" ##UpdToSuccess insId:" + taskInstanceId + " ftpFile:" + responseFileLinkList.get(0) + " rows:" + exportRowsCount);
            long fileLength = localZipFile.length();
            if (multiExportList.size() > 0) {
                fileLength = 0;
                for (String fileName : multiExportList) {
                    File f = new File(fileName);
                    fileLength += f.length();
                    if (f.exists()) {
                        f.delete();
                        System.out.println(" ##DelLocalFile insId:" + taskInstanceId + " file:" + f.getAbsolutePath());
                    }
                }
            }
            apiTaskInstanceEntityDAO.updateProcessEndTimeV1(taskInstanceId, ApiTaskInstanceEntity.STATUS_SUCCESS, DateTime.now().toDate(), fileLength,
                    responseFileLinkList.get(0), exportRowsCount, new Date());

            System.out.println(" ##DelCacheKey insId:" + taskInstanceId + " cacheKey:" + cacheKey);
            TaskUtil.deleteKey(cacheKey);

            if (localZipFile.exists()) {
                System.out.println(" ##DelLocalFile insId:" + taskInstanceId + " file:" + fullNameZip);
                localZipFile.delete();
            }

            // https://www.wrike.com/open.htm?id=1057201829
            if (StringUtils.isNotBlank(functionName) && TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL.equalsIgnoreCase(functionName)) {
                System.out.println(" ##SaveOutKey insId for sending email:" + taskInstanceId + " key:" + outTaskKey);
                ApiTaskInstanceEntity apiTaskInstance = apiTaskInstanceEntityDAO.getTaskInstanceById(taskInstanceId);
                TExportInfoEntity info = new TExportInfoEntity();
                info.setCreateDate(new Date());
                info.setOwnDomainId(ownDomainId);
                info.setExportTable(EXPORT_CLARITY360_PAGE_DETAIL);
                ApiTaskParamsEntity apiTaskParamsByTaskName = apiTaskParamsEntityDAO.getApiTaskParamsByTaskName(taskInstanceId);
                info.setExportParam(apiTaskParamsByTaskName.getRequestBody());
                info.setFileName(apiTaskInstance.getFtpFullPathFilename());
                info.setStatus(2);
                info.setMd5(null);
                info.setFileSuffix(isFileFormatCSV?"csv":"json");
                info.setResultCount(apiTaskInstance.getOutputFileSize().intValue());
                Long result = exportInfoDAO.insertNewExportTask(info);
                if (null != result) {
                    TExportUserInfoEntity tExportUserInfoEntity = new TExportUserInfoEntity();
                    tExportUserInfoEntity.setSendToUserid(apiTaskInstance.getUserId());
                    tExportUserInfoEntity.setStatus(0);
                    tExportUserInfoEntity.setExportId(result.intValue());
                    exportUserInfoDAO.insertNewExportTask(tExportUserInfoEntity);
                }
            }
        } catch (Exception exp) {
            exp.printStackTrace();
            saveErrorToDB(apiTaskInstanceEntity, exp);
        }
    }

    private void uploadExportFile(List<String> multiExportList, ApiTaskInstanceEntity apiTaskInstanceEntity, CommonParamEntity commonParamEntity, List<String> exportFileUrlList, List<String> resPathForMultiFiles, String[] errors) {
        int ownDomainId = apiTaskInstanceEntity.getOwnDomainId();
        int taskInstanceId = apiTaskInstanceEntity.getId();
        // save to s3
        multiExportList.forEach(fileName -> {
            SeagateUtils.saveFileToDefaultSeagate(ownDomainId, fileName);
            String url = null;
            try {
                File f = new File(fileName);
                url = SeagateUtils.getDefaultSeagatePresignedUrl(ownDomainId, f.getName());
                if (StringUtils.isBlank(url)) {
                    System.out.println(" ##FailedToPresignURL instance:" + taskInstanceId + " OID:" + ownDomainId + " file:" + fileName);
                } else {
                    exportFileUrlList.add(url);
                }
            } catch (Exception e) {
                System.out.println(" ##FailedToPresignURL instance:" + taskInstanceId + " OID:" + ownDomainId + " file:" + fileName);
            }
        });
        // save to client s3
        String[] resPath = new String[1];
        System.out.println("=====GET FROM DB commonParamEntity:"+JSONUtil.toJsonStr(commonParamEntity));
        System.out.println("=====:"+ resPath[0]);
        multiExportList.forEach(fileName -> {
            File f = new File(fileName);
            saveFileToClientSpecifiedStorage(commonParamEntity.getParamJson(), commonParamEntity.getTitle(), commonParamEntity.getFuncName(), f, errors, resPath);
            String specifiedStorageFileUrl = SeagateUtils.getSpecifiedStorageFileUrl(commonParamEntity, f.getName());
            if (StringUtils.isNotBlank(specifiedStorageFileUrl)) {
                exportFileUrlList.add(specifiedStorageFileUrl);
            }
            resPathForMultiFiles.add(resPath[0]);
        });
    }

    // https://www.wrike.com/open.htm?id=1057201829
    private String getLocalFullPathFileName(ApiTaskInfoEntity apiTaskInfoEntity, ApiTaskInstanceEntity apiTaskInstanceEntity, String fileFormat,String customFileName) {
        return getLocalFullPathFileName(apiTaskInfoEntity,apiTaskInstanceEntity, fileFormat,null,customFileName);
    }

    private String getLocalFullPathFileNameForTopic(ApiTaskInfoEntity apiTaskInfoEntity, ApiTaskInstanceEntity apiTaskInstanceEntity, String fileFormat,String functionName) {
        String format = FILE_FORMATE_JSON.equalsIgnoreCase(fileFormat) ? "json" : "csv"; // default csv
        String rawFileName = String.join(".", apiTaskInfoEntity.getOutFilePrefix() + apiTaskInstanceEntity.getTaskId(), format);
        System.out.println("=====rawFileName:"+rawFileName);
        System.out.println("=====getLocalFullPathFileNameForTopic:"+ LOCAL_STORE_DIR+  rawFileName);
        return  LOCAL_STORE_DIR+  rawFileName;
    }

    private String getLocalFullPathFileName(ApiTaskInfoEntity apiTaskInfoEntity, ApiTaskInstanceEntity apiTaskInstanceEntity, String fileFormat,String functionName,String customFileName) {
        String format = FILE_FORMATE_JSON.equalsIgnoreCase(fileFormat) ? "json" : "csv"; // default csv
        String rawFileName = "";
        System.out.println("=====customFileName:"+customFileName);
        if(StringUtils.isNotBlank(customFileName)){
            System.out.println("=====!!!!!customFileName[0]:"+customFileName);
            rawFileName = String.join(".", customFileName, format);
        }else{
            rawFileName = String.join(".", apiTaskInfoEntity.getOutFilePrefix() + apiTaskInstanceEntity.getTaskId(), format);
        }

        System.out.println("=====rawFileName:"+rawFileName);
        if (StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL, functionName)) {
            return String.join(File.separator, LOCAL_STORE_CLARITY360_DIR, rawFileName);
        } else if (StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_CLARITY360_URL_DETAIL, functionName)) {
            return String.join(File.separator, LOCAL_STORE_CLARITY360_DIR, rawFileName);
        } else {
            System.out.println("=====getLocalFullPathFileName:"+ String.join(File.separator, LOCAL_STORE_DIR, rawFileName));
            return String.join(File.separator, LOCAL_STORE_DIR, rawFileName);
        }
    }

    // https://www.wrike.com/open.htm?id=1057201829
    private int exportFromClarityDB(ApiTaskInfoEntity apiTaskInfoEntity, ApiTaskInstanceEntity apiTaskInstanceEntity, String cacheKey, String resultStr,
                                    Map<String, Object> workerParameterMap, String fullFileName, boolean isFileFormatCSV) throws Exception {
        return exportFromClarityDB(apiTaskInfoEntity,apiTaskInstanceEntity,cacheKey,resultStr,
                workerParameterMap,fullFileName,isFileFormatCSV,null);
    }

    private int exportFromClarityDB(ApiTaskInfoEntity apiTaskInfoEntity, ApiTaskInstanceEntity apiTaskInstanceEntity, String cacheKey, String resultStr,
                                    Map<String, Object> workerParameterMap, String fullFileName, boolean isFileFormatCSV,String functionName) throws Exception {
        System.out.println("=====functionName:"+functionName);
        String[] err = new String[]{""};
        String querySql = "";
        if(IS_DEBUG){
            //System.out.println("=====querySql:"+querySql);
            //return -2;
            ApiTaskParamsEntity  apiTaskParamsEntit = apiTaskParamsEntityDAO.getApiTaskParamsByTaskName(apiTaskInstanceEntity.getId());
            querySql = apiTaskParamsEntit.getRequestSql();
            //querySql = " test error sql";

            System.out.println("=====querySql:"+querySql);
        }else{
            querySql = (String)workerParameterMap.get(WORKER_JSON_PROPERTY_KEY_SQL);
        }

        if(StringUtils.isNotBlank(querySql)){
            System.out.println("=====replaced sql!!!");
            querySql = querySql.replace("ORDER BY avg_search_volume DESC  LIMIT 0, 99999999"," ");
            querySql = querySql.replace("ORDER BY avg_search_volume DESC  LIMIT 99999999"," ");
        }
        System.out.println("=====replaced sql:"+querySql);
        long start = System.currentTimeMillis() / 1000;
        System.out.println("=====exportFromClarityDB.fullFileName:"+fullFileName);
        int rowCount = 0;
        if (StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL, functionName)) {
            if (apiTaskInstanceEntity.getId() == 17966) {
                querySql = "SELECT      urlStr,      originalUrl, response_codeCompare,response_code,titleCompare,title FROM (  SELECT      urlStr,      originalUrl,      urlMurmurHash AS url_murmur_hash, response_codeCompare,response_code AS response_code,titleCompare,title AS title FROM (  SELECT      urlStr,      originalUrl,      urlMurmurHash, response_codeAlias AS response_code, titleAlias AS title FROM (  SELECT          decodeURLComponent(url) as urlStr,         url as originalUrl,         url_murmur_hash as urlMurmurHash, response_code AS response_codeAlias,title AS titleAlias FROM  clarity360.dis_clarity360_v3 t1 WHERE 1 = 1  AND domain_id = 4 AND report_id = 2036  AND report_id_mod = 2036 % 1000  AND (has(sources, 'sitehealth'))  ) ) t  GLOBAL ALL FULL  JOIN (  SELECT      urlMurmurHash, response_codeAlias AS response_codeCompare, titleAlias AS titleCompare FROM (  SELECT          url_murmur_hash as urlMurmurHash, response_code AS response_codeAlias,title AS titleAlias FROM  clarity360.dis_clarity360_v3 t1 WHERE 1 = 1  AND domain_id = 4 AND report_id = 2050  AND report_id_mod = 2050 % 1000  AND (has(sources, 'sitehealth'))  ) ) y USING (urlMurmurHash)  WHERE 1=1   ) FORMAT CSV";
            }
            List<String> headerTitle = (List<String>) workerParameterMap.get(WORKER_JSON_PROPERTY_KEY_HEADER);
            String totalCountSql = (String) workerParameterMap.get("totalCountSql");
            if (apiTaskInstanceEntity.getId() == 17966) {
                totalCountSql = "SELECT  COUNT() as cnt  FROM (  SELECT      urlStr,      originalUrl,      urlMurmurHash, response_codeAlias AS response_code, titleAlias AS title FROM (  SELECT          decodeURLComponent(url) as urlStr,         url as originalUrl,         url_murmur_hash as urlMurmurHash, response_code AS response_codeAlias,title AS titleAlias FROM  clarity360.dis_clarity360_v3 t1 WHERE 1 = 1  AND domain_id = 4 AND report_id = 2036  AND report_id_mod = 2036 % 1000  AND (has(sources, 'sitehealth'))  ) ) t  GLOBAL ALL FULL  JOIN (  SELECT      urlMurmurHash, response_codeAlias AS response_codeCompare, titleAlias AS titleCompare FROM (  SELECT          url_murmur_hash as urlMurmurHash, response_code AS response_codeAlias,title AS titleAlias FROM  clarity360.dis_clarity360_v3 t1 WHERE 1 = 1  AND domain_id = 4 AND report_id = 2050  AND report_id_mod = 2050 % 1000  AND (has(sources, 'sitehealth'))  ) ) y USING (urlMurmurHash)  WHERE 1=1";
            }
            if (workerParameterMap.get("specialResponseCode") != null) {
                String specialResponseCode = (String) workerParameterMap.get("specialResponseCode");
                rowCount = ClarityDBUtils.httpExportFromClarityDBMultiForClarity360SpecialResponseCode(CH_DB_CLARITY360_URL, CH_DB_CLARITY360, CLARITY360_USER, CLARITY360_PSW, querySql + "", fullFileName, isFileFormatCSV, true, err, headerTitle, totalCountSql);
            } else {
//            rowCount = ClarityDBUtils.httpExportFromClarityDBForClarity360(CH_DB_CLARITY360_URL, CH_DB_CLARITY360, USER, PSW, querySql + "", fullFileName, isFileFormatCSV, true, err,headerTitle);
                // modify for clarity360 large data
                rowCount = ClarityDBUtils.httpExportFromClarityDBMultiForClarity360(CH_DB_CLARITY360_URL, CH_DB_CLARITY360, CLARITY360_USER, CLARITY360_PSW, querySql + "", fullFileName, isFileFormatCSV, true, err, headerTitle, totalCountSql);
            }
        } else if (StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_CLARITY360_URL_DETAIL, functionName)) {
            List<String> headerTitle = (List<String>) workerParameterMap.get(WORKER_JSON_PROPERTY_KEY_HEADER);
            Integer pageSize;
            if (workerParameterMap.get(WORKER_JSON_PROPERTY_KEY_PAGE_SIZE) == null) {
                pageSize = 99999999;
            } else {
                pageSize = Integer.parseInt(workerParameterMap.get(WORKER_JSON_PROPERTY_KEY_PAGE_SIZE).toString());
            }
            if (pageSize == null || pageSize == 99999999) {
                rowCount = ClarityDBUtils.httpExportFromClarityDBMultiForClarity360(CH_DB_CLARITY360_URL, CH_DB_CLARITY360, CLARITY360_USER, CLARITY360_PSW, querySql + "", fullFileName, isFileFormatCSV, true, err, headerTitle);
            } else {
                rowCount = ClarityDBUtils.httpExportFromClarityDBMultiForClarity360PageSizeLimit(CH_DB_CLARITY360_URL, CH_DB_CLARITY360, CLARITY360_USER, CLARITY360_PSW, querySql + "", fullFileName, isFileFormatCSV, true, err, headerTitle,pageSize);
            }
        } else if (StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_RESEARCHGRID_TOPICEXPLORER_KEYWORD, functionName)) {
            //List<String> headerTitle = (List<String>) workerParameterMap.get(WORKER_JSON_PROPERTY_KEY_HEADER);
            // List<String> domains = JSONUtil.toList();
            cn.hutool.json.JSONArray monnthArray = JSONUtil.parseArray(workerParameterMap.get(MONTHS).toString());
            List<String> monthList  = JSONUtil.toList(monnthArray, String.class);
            cn.hutool.json.JSONArray headerArray = JSONUtil.parseArray(workerParameterMap.get(WORKER_JSON_PROPERTY_KEY_HEADER).toString());
            String fileformat = workerParameterMap.get("fileFormate").toString();
            System.out.println("=====fileformat:"+fileformat);
            boolean isCsv =StringUtils.endsWithIgnoreCase(FILE_FORMATE_CSV,fileformat);
            List<String> headers = JSONUtil.toList(headerArray, String.class);
            // this export should use central/RG hot, because we don;t maintain the clickstream table in RG backup
//            rowCount = ClarityDBUtils.httpExportFromClarityDB_topicExplorer(CH_DB_RG_CENTRAL_URL, CH_DB_RG, USER, PSW, querySql + "", fullFileName, isCsv,headers,monthList,true, err);
            rowCount = ExportTopicExplorerDetailProcessor.getInstance(apiTaskInstanceEntity, CH_DB_RG_CENTRAL_URL, CH_DB_RG, USER, PSW, workerParameterMap).processExport(querySql, fullFileName, isCsv, headers, monthList, true, err);
        } else if (StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASK_NAME_RESEARCHGRID_KEYWORDDETAIL, functionName)) {
            cn.hutool.json.JSONArray monnthArray = JSONUtil.parseArray(workerParameterMap.get(MONTHS).toString());
            List<String> monthList  = JSONUtil.toList(monnthArray, String.class);
            cn.hutool.json.JSONArray headerArray = JSONUtil.parseArray(workerParameterMap.get(WORKER_JSON_PROPERTY_KEY_HEADER).toString());
            String fileformat = workerParameterMap.get("fileFormate").toString();
            System.out.println("=====fileformat:"+fileformat);
            boolean isCsv =StringUtils.endsWithIgnoreCase(FILE_FORMATE_CSV,fileformat);
            List<String> headers = JSONUtil.toList(headerArray, String.class);
            rowCount = ClarityDBUtils.httpExportFromClarityDB_researchGrid_detail(CH_DB_RG_URL, CH_DB_RG, USER, PSW, querySql + "", fullFileName, isCsv,headers,monthList,true, err);
        }
//        else if (StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(tas, functionName)) {
//            cn.hutool.json.JSONArray monnthArray = JSONUtil.parseArray(workerParameterMap.get(MONTHS).toString());
//            List<String> monthList  = JSONUtil.toList(monnthArray, String.class);
//            cn.hutool.json.JSONArray headerArray = JSONUtil.parseArray(workerParameterMap.get(WORKER_JSON_PROPERTY_KEY_HEADER).toString());
//            String fileformat = workerParameterMap.get("fileFormate").toString();
//            System.out.println("=====fileformat:"+fileformat);
//            boolean isCsv =StringUtils.endsWithIgnoreCase(FILE_FORMATE_CSV,fileformat);
//            List<String> headers = JSONUtil.toList(headerArray, String.class);
//            rowCount = ClarityDBUtils.httpExportFromClarityDB_researchGrid_detail(CH_DB_RG_URL, CH_DB_RG, USER, PSW, querySql + "", fullFileName, isCsv,headers,monthList,true, err);
//        }
        // https://www.wrike.com/open.htm?id=1247023765
        else if (StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_RESEARCHGRID_DOMAINKEYWORDTREND, functionName)) {
            //List<String> headerTitle = (List<String>) workerParameterMap.get(WORKER_JSON_PROPERTY_KEY_HEADER);
            // List<String> domains = JSONUtil.toList();
            cn.hutool.json.JSONArray monnthArray = JSONUtil.parseArray(workerParameterMap.get(MONTHS).toString());
            List<String> monthList  = JSONUtil.toList(monnthArray, String.class);
            List<String> notExistKeywordList = null;
            if(workerParameterMap.get(NOT_EXIST_KEYWORDS)!=null) {
                cn.hutool.json.JSONArray notExistKeywords = JSONUtil.parseArray(workerParameterMap.get(NOT_EXIST_KEYWORDS).toString());
                if (notExistKeywords != null) {
                    notExistKeywordList = JSONUtil.toList(notExistKeywords, String.class);
                }
            }
            cn.hutool.json.JSONArray rank_monnthArray = JSONUtil.parseArray(workerParameterMap.get("rankmonths").toString());
            List<String> rank_monthList  = JSONUtil.toList(rank_monnthArray, String.class);
            cn.hutool.json.JSONArray domainArray = JSONUtil.parseArray(workerParameterMap.get(DOMAINS).toString());
            List<String> domainList = JSONUtil.toList(domainArray, String.class);
            String fileformat = workerParameterMap.get("fileFormate").toString();
            boolean isCsv =StringUtils.endsWithIgnoreCase(FILE_FORMATE_CSV,fileformat);
            rowCount = ClarityDBUtils.httpExportFromClarityDB_domainKeywords(CH_DB_RG_URL, CH_DB_RG, USER, PSW, querySql + "", fullFileName, isCsv, true,domainList,monthList,rank_monthList,notExistKeywordList, err);
        }else if (StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_RESEARCHGRID_KEYWORDDETAILTOPX, functionName)) {
            List<String> notExistKeywordList = null;
            if(workerParameterMap.get(NOT_EXIST_KEYWORDS)!=null) {
                cn.hutool.json.JSONArray notExistKeywords = JSONUtil.parseArray(workerParameterMap.get(NOT_EXIST_KEYWORDS).toString());
                if (notExistKeywords != null) {
                    notExistKeywordList = JSONUtil.toList(notExistKeywords, String.class);
                }
            }
            System.out.println("=====workerParameterMap:"+JSONUtil.toJsonStr(workerParameterMap));
            cn.hutool.json.JSONArray listCheckId = JSONUtil.parseArray(workerParameterMap.get(LISTCHECKIDS).toString());
            cn.hutool.json.JSONArray listMonth = JSONUtil.parseArray(workerParameterMap.get(MONTHS).toString());
            Integer index = Integer.parseInt(workerParameterMap.get("index").toString());
            String fileformat = workerParameterMap.get("fileFormate").toString();
            boolean isCsv =StringUtils.endsWithIgnoreCase(FILE_FORMATE_CSV,fileformat);
            List<Integer> listcheckid = JSONUtil.toList(listCheckId, Integer.class);
            rowCount = ClarityDBUtils.httpExportFromClarityDB_KeywordsTopx(CH_DB_RG_URL, CH_DB_RG, USER, PSW, querySql + "", fullFileName, isCsv, true,listcheckid,JSONUtil.toList(listMonth, String.class),notExistKeywordList,err);
            System.out.println("=====ClarityDBUtils.httpExportFromClarityDB_KeywordsTopx finished!!!"+rowCount);
        }
        else
        {
            rowCount = ClarityDBUtils.httpExportFromClarityDB(CH_DB_RG_URL, CH_DB_RG, USER, PSW, querySql + "", fullFileName, isFileFormatCSV, true, err);
        }
        long end = System.currentTimeMillis() / 1000;
        System.out.println(" ###EndExp insId:" + apiTaskInstanceEntity.getId() + " rows:" + rowCount + " time:" + ((end - start) / 60) + "m" + ((end - start) % 60) + "s");
        if (rowCount == ClarityDBUtils.HTTP_EXPORT_CLARITYDB_TO_FILE_FAILD) {
            if (StringUtils.isNotBlank(functionName) && (StringUtils.equalsIgnoreCase(TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL, functionName) || StringUtils.equalsIgnoreCase(TASKNAME_CLARITY360_URL_DETAIL, functionName)) ) {
                sendMail(apiTaskInfoEntity, apiTaskInstanceEntity, cacheKey, resultStr, err[0], TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL);
            } else {
                sendMail(apiTaskInfoEntity, apiTaskInstanceEntity, cacheKey, resultStr, err[0]);
            }
            return -1;
        }
        System.out.println();
        return rowCount;
    }

    private int exportFromClarityDBForMultipleFiles(ApiTaskInfoEntity apiTaskInfoEntity, ApiTaskInstanceEntity apiTaskInstanceEntity, String cacheKey, String resultStr,
                                   Map<String, Object> workerParameterMap, String fullFileName, boolean isFileFormatCSV, String functionName, List<String> fileList) throws Exception {
        System.out.println("=====functionName:"+functionName);
        String[] err = new String[]{""};
        String querySql = (String)workerParameterMap.get(WORKER_JSON_PROPERTY_KEY_SQL);

        System.out.println("=====replaced sql:"+querySql);
        long start = System.currentTimeMillis() / 1000;
        System.out.println("=====exportFromClarityDB.fullFileName:"+fullFileName);
        int rowCount = 0;
        if (StringUtils.equalsIgnoreCase(TASKNAME_DAILYRANKING_KEYWORDDETAILTOPX, functionName) || StringUtils.equalsIgnoreCase(TASKNAME_DAILYRANKING_KEYWORDDETAIL_SERP_TOPX, functionName)) {
            rowCount = DailyRankingKeywordDetailTopXProcessorV2.getInstance(apiTaskInstanceEntity, resultStr, functionName).processExport(fullFileName, isFileFormatCSV, err, fileList);
        }
        long end = System.currentTimeMillis() / 1000;
        System.out.println(" ###EndExp insId:" + apiTaskInstanceEntity.getId() + " rows:" + rowCount + " time:" + ((end - start) / 60) + "m" + ((end - start) % 60) + "s");
        if (rowCount == ClarityDBUtils.HTTP_EXPORT_CLARITYDB_TO_FILE_FAILD) {
            if (StringUtils.isNotBlank(functionName) && (StringUtils.equalsIgnoreCase(TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL, functionName) || StringUtils.equalsIgnoreCase(TASKNAME_CLARITY360_URL_DETAIL, functionName)) ) {
                sendMail(apiTaskInfoEntity, apiTaskInstanceEntity, cacheKey, resultStr, err[0], TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL);
            } else {
                sendMail(apiTaskInfoEntity, apiTaskInstanceEntity, cacheKey, resultStr, err[0]);
            }
            return -1;
        }
        return rowCount;
    }

    private boolean saveFileToDefaultFTP(File localFile, String ftpDesinationFolder) {
        System.out.println(" ##saveFileToDefaultFTP:" + localFile.getAbsolutePath() + "->" + ftpDesinationFolder);
        FTPServerInfoBean ftpServerInfo = FTPUtils.getFTPServerInfo();
        return FTPUtils.saveFileToFTP(ftpServerInfo.getPrivateHost(), ftpServerInfo.getServerUserName(), ftpServerInfo.getServerPassword(), localFile.getAbsolutePath(),
                ftpDesinationFolder);
    }

    private boolean saveFileToClientSpecifiedStorage(String paramJson, String title, String funcName, File outFile,String[] errors,String[] resPath) {
        boolean saveResultFlag = false;
        System.out.println(" ##saveFileToClientSpecifiedStorage paramJson:" + paramJson + " title:" + title + " funcName:" + funcName + " file:" + outFile.getAbsolutePath());
        long start = System.currentTimeMillis() / 1000;
        try {
            // String paramJson, String title, String funcName,File outFile, String[] outFtpFullPath, boolean isReject
            saveResultFlag = sendRemoteFile(paramJson, title, funcName, outFile, false,resPath);
        } catch (Exception ex) {
            ex.printStackTrace();
            errors[0] = ex.getMessage();
        }
        long end = System.currentTimeMillis() / 1000;
        System.out.println("####upLoadFile time:" + ((end - start) / 60) + "m" + ((end - start) % 60) + "s");
        return saveResultFlag;
    }

    private CommonParamEntity getCommonParamEntityFromWorkerParameter(Map<String, Object> workerParameterMap, String jsonKey) {
        if (workerParameterMap.get(jsonKey) != null) {
            String str = JSONObject.toJSONString(workerParameterMap.get(jsonKey));
            System.out.println("  commonParamEntity:" + str);
            try {
                return JSONObject.parseObject(str, CommonParamEntity.class);
            } catch (Exception ex) {
                ex.printStackTrace();
            }
        }
        return null;
    }

    private Event constructResponseEvent(ApiTaskInstanceEntity apiTaskInstanceEntity, CommonParamEntity commonParamEntity, String fileName,
                                         List<String> responseFileLinkList, int exportRowsCount) {
        Event event = new Event();
        event.taskInstanceId = apiTaskInstanceEntity.getId();
        event.completeTime = DateTime.now();
        event.taskId = apiTaskInstanceEntity.getTaskId();
        event.setTotalCount(exportRowsCount);
        event.files.addAll(responseFileLinkList);
        if (commonParamEntity != null) {
            Long storageId = commonParamEntity.getId();
            if (storageId != null) {
                storageId = storageId > 0 ? storageId : -storageId; // TODO
            }
            event.setStorageId(storageId);
            event.setStorageName(commonParamEntity.getTitle());
            if (commonParamEntity.getId() != null && commonParamEntity.getId() < 0 && StringUtils.isNotBlank(commonParamEntity.getTitle())) {
                event.setStorageName(null);
                event.setMessage(commonParamEntity.getTitle());
            } else if (commonParamEntity.getId() != null && commonParamEntity.getId() > 0 && responseFileLinkList.size() == 1&&!StringUtils.equals(commonParamEntity.getFuncName(),CommonParamEntity.ParamFuncName.SetGCS.name()) ) {
                event.setMessage(FAILED_TO_STORAGE_MESSAGE);
            }
        }
        return event;
    }

    private void saveErrorToDB(ApiTaskInstanceEntity apiTaskInstanceEntity, Exception ex) {
        System.out.println("===AsyncInsError insId:" + apiTaskInstanceEntity.getId() + " err:" + ex.getMessage() + " at " + SDF_YYYYMMDDHHMMSS.format(new Date()));
        try {
            ex.printStackTrace();
            String errorMessage = ex.getCause() != null ? ex.getCause().toString() : "";
            if (errorMessage != null && errorMessage.length() > 256) {
                errorMessage = StringUtils.substring(errorMessage, 0, 256);
            }
            String errorTrace = ex.getMessage();
            if (errorTrace != null && errorTrace.length() > 1024) {
                errorTrace = StringUtils.substring(errorTrace, 0, 1024);
            }
            apiTaskInstanceEntity.setErrorMessage(errorMessage);
            apiTaskInstanceEntity.setErrorTrace(errorTrace); // TODO
            apiTaskInstanceEntityDAO.saveErrorMessage(apiTaskInstanceEntity, new Date());
        } catch (Exception exp) {
            exp.printStackTrace();
        }
    }

    // move to SeagateUtils
//    private String getSpecifiedStorageFileUrl(CommonParamEntity commonParamEntity, String filename) {
//        if (commonParamEntity != null) {
//            if (StringUtils.equals(commonParamEntity.getFuncName(), "SetFtp")) {
//                String host = JSONUtil.getByPath(JSONUtil.parse(commonParamEntity.getParamJson()), "host").toString();
//                String port = JSONUtil.getByPath(JSONUtil.parse(commonParamEntity.getParamJson()), "port").toString();
//                String protocol = JSONUtil.getByPath(JSONUtil.parse(commonParamEntity.getParamJson()), "protocol").toString();
//                String folder = JSONUtil.getByPath(JSONUtil.parse(commonParamEntity.getParamJson()), "folder").toString();
//                return protocol + "://" + host + ":" + port + folder + filename;
//            } else if (StringUtils.equals(commonParamEntity.getFuncName(), "SetS3")) {
//                String bucket = JSONUtil.getByPath(JSONUtil.parse(commonParamEntity.getParamJson()), "bucket").toString();
//                String path = JSONUtil.getByPath(JSONUtil.parse(commonParamEntity.getParamJson()), "path").toString();
//                return "s3://" + bucket + path + filename;
//            }
//        }
//        return null;
//    }

    private void Log(String txt,String functionName) {
        try {
            String fullName = "";
            if (StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL, functionName)) {
                fullName = String.join(File.separator,LOG_CLARITY360_DIR , FAILED_SQL_LOG_NAME);
            } else {
                fullName = String.join(File.separator, LOG_DIR, FAILED_SQL_LOG_NAME);
            }
            File file = new File(fullName);
            FileWriter fw = new FileWriter(file, true);
            fw.write(SDF_YYYYMMDDHHMMSS.format(DateTime.now().toDate()) + "  " + txt + "\r\n");
            fw.close();
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    // https://www.wrike.com/open.htm?id=1057201829
    private void sendMail(ApiTaskInfoEntity apiTaskInfoEntity, ApiTaskInstanceEntity apiTaskInstanceEntity, String cacheKey, String resultStr, String err) throws Exception {
        if(IS_DEBUG){
            //return;
        }
        sendMail(apiTaskInfoEntity, apiTaskInstanceEntity, cacheKey, resultStr, err,null);
    }

    private void sendMail(ApiTaskInfoEntity apiTaskInfoEntity, ApiTaskInstanceEntity apiTaskInstanceEntity, String cacheKey, String resultStr, String err,String functionName) throws Exception {
        if(IS_DEBUG){
            //return;
        }
        apiTaskInstanceEntityDAO.updateProcessEndTime(apiTaskInstanceEntity.getId(), ApiTaskInstanceEntity.STATUS_ERR0R, DateTime.now().toDate(), 0, "", new Date());
        String txt = "<h5>OID:"+apiTaskInstanceEntity.getOwnDomainId()+"</h5> <h5>taskName:" + apiTaskInfoEntity.getTaskName() + "</h5> <h5>taskInstanceName:" + apiTaskInstanceEntity.getTaskInstanceName() + "</h5> <h5>key:" + cacheKey + "</h5> json:" + resultStr;
        System.out.println("###exportFromCHError:" + txt + "\r\n" + err);
        if (StringUtils.isNotBlank(functionName) && StringUtils.equalsIgnoreCase(TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL, functionName)) {
            Log(txt, TASKNAME_CLARITY360_URL_DETAILDOWNLOADVIAMAIL);
        } else {
            Log(txt,null);
        }
        // TaskUtil.setValue((apiTaskInfoEntity.getFailCacheKeyPrefix() + cacheKey).getBytes(StandardCharsets.UTF_8), resultStr); // TODO
        String subject = "Async API (aka TASK system) execute sql failed";
        Map<String, String> map = new HashMap<String, String>();
        map.put("result", txt + "\r\n" + err);
        map.put("dateString", SDF.format(new Date()));
        zeptoMailSenderComponent.sendMimeMultiPartZeptoMailAndBccByFunctionType(SEND_TO, CC_TOS, subject, "mail_upload_report.txt", "mail_upload_report.html", map, null,
                ZeptoMailSenderComponent.FUNCTION_TYPE_BACKEND_INTERNAL, null, null);
    }

    private static boolean sendRemoteFile(String paramJson, String title, String funcName, File outFile, boolean isReject,String[] resPath) throws Exception {
        boolean isS3InfoOk = true;

        System.out.println("=====sendRemoteFile funcname:" + funcName + ",isReject:" + isReject);
        if (StringUtils.equals(funcName, "SetS3") && !isReject) {
            if (StringUtils.isBlank(paramJson)) {
                System.out.println("-----paramJson error :" + paramJson);
                isS3InfoOk = false;
            }
            Gson gson = new Gson();
            try {
                Map<String, String> s3Info = gson.fromJson(paramJson, Map.class);
                String accessKey = "";
                String secretKey = "";
                String s3Type = s3Info.get("s3Type");
                accessKey = s3Info.get("accessKey");
                if(org.apache.commons.lang.StringUtils.isNotBlank(s3Type) && s3Type.equalsIgnoreCase("share")){
                    secretKey = AwsCredentialsEnvKeyConstructor.getInstance().getSQSDecryptedSecretKey();
                    System.out.println("===share key:" + accessKey);
                }else if(org.apache.commons.lang.StringUtils.isNotBlank(s3Type) && s3Type.equalsIgnoreCase("credential")){
                    secretKey = ScStringEncryptor.decrypt(s3Info.get("secretKey"));
                    System.out.println("===credential key:" + accessKey);
                }else {//todo for new logic future

                }

                String bucketName = s3Info.get("bucket");
                String path = s3Info.get("path");
                if (path.equalsIgnoreCase("/")) {
                    path = "";
                }

                if (isS3InfoOk) {
                    AWSCredentials credentials = new BasicAWSCredentials(accessKey, secretKey);
                    AmazonS3 s3client = new AmazonS3Client(credentials);
                    String keyPath = path + outFile.getName();
                    s3client.putObject(new PutObjectRequest(bucketName, keyPath, outFile).withStorageClass(StorageClass.ReducedRedundancy).withCannedAcl(CannedAccessControlList.BucketOwnerFullControl));
                    System.out.println("S3 eekey: " + keyPath + " , file: " + outFile.getName());
                    return true;
                } else {
                    //"Failed to upload file to SetFtp , error message:get SetFtp json Info error"
                    throw new RuntimeException("Failed to upload file to SetS3,  , error message:get SetS3 json Info error");
                }

            } catch (Exception e) {
                throw new RuntimeException("Failed to upload file to SetS3,  , error message:"+e.getMessage());
            }
        } // https://www.wrike.com/open.htm?id=1265177517 marven
        else if (StringUtils.equals(funcName, "SetGCS") && !isReject) {
            try {
                String tempName = outFile.getName();
                System.out.println("=====file name:" + tempName);
                if(GCSUtils.getFileByParamJson(paramJson,outFile.getName())){

                    SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
                    String formattedDate = sdf.format(new Date());
                    String[] tempFileNameArray =StringUtils.split(tempName,'.') ;
                    //System.out.println("=====split FileNameArray:" + JSONUtil.toJsonStr(tempFileNameArray));
                    if(tempFileNameArray.length > 2){
                        tempFileNameArray[tempFileNameArray.length-3] = tempFileNameArray[tempFileNameArray.length-3] +"_"+ formattedDate;
                    }
                    tempName = StringUtils.join(Arrays.asList(tempFileNameArray),".");
                    System.out.println("=====file "+ tempName +",is exist,now rename to yyyyMMddHHmmss:" + tempName);
                }else{
                    System.out.println("=====file "+ tempName +",is not exist, filename:"+tempName);
                }

                System.out.println("=====paramJson:"+paramJson);
                cn.hutool.json.JSONObject obj = JSONUtil.parseObj(paramJson);
                String wpath = obj.get("path").toString();
                if (wpath.equalsIgnoreCase("/")) {
                    wpath = "";
                }
                System.out.println("gcspath:"+ wpath + tempName);
                String serverPath = GCSUtils.putFileByParamJson(paramJson,outFile.getAbsolutePath(),wpath + tempName);
                // https://www.wrike.com/open.htm?id=1344940859 marven
                GCSUtils.toHistoryParam(paramJson,"wmt-seoclarity-dev-api",wpath + tempName,HISTORY_PATH + tempName);
                resPath[0] = serverPath;
                System.out.println("=====serverPath:" +serverPath);
            } catch (Exception e) {
                throw new Exception("Failed to upload file to SetGCS , error message:" + e.getMessage());
            }catch (OutOfMemoryError e){
                throw new Exception("Failed to upload file to SetGCS , error message:" + e.getMessage());
            }
        }else if (StringUtils.equals(funcName, "SetFtp") && !isReject) {
            if (StringUtils.isBlank(paramJson)) {
                System.out.println("-----paramJson error :" + paramJson);
                isS3InfoOk = false;
            }
            Gson gson = new Gson();
            try {
                Map<String, String> s3Info = gson.fromJson(paramJson, Map.class);
                String host = s3Info.get("host");
                String userName = s3Info.get("userName");
                String password = DesUtils.decrypt(s3Info.get("userPwd"));
                Integer port = NumberUtils.toInt(s3Info.get("port"));
                String protocol = s3Info.get("protocol");
                String remoteFolderPath = s3Info.get("folder");
                if (isS3InfoOk) {
                    if (StringUtils.equalsIgnoreCase(protocol, "ftp")) {
                        if (StringUtils.equalsIgnoreCase(remoteFolderPath, "\\")) {
                            remoteFolderPath = "";
                        }
                        if (port > 0) {
                            FtpUtilsV2 ftp = new FtpUtilsV2(host, userName, port, password);
                            ftp.uploadFile(remoteFolderPath, outFile.getName(), outFile.getAbsolutePath());
                        } else {
                            FtpUtilsV2 ftp = new FtpUtilsV2(host, userName, password);
                            ftp.uploadFile(remoteFolderPath, outFile.getName(), outFile.getAbsolutePath());
                        }

                    } else if (StringUtils.equalsIgnoreCase(protocol, "sftp") || StringUtils.equalsIgnoreCase(protocol, "sshftp")) {
                        if (port == 0) {
                            port = 22;//default sftp port
                        }

                        SFTPUtils sf = SFTPUtils.getInstance(host, port, userName, password);
                        sf.upload(remoteFolderPath, outFile.getAbsolutePath());
                        sf.disconnect();
                    } else {
                        return false;
                    }
                    System.out.println("FTP/SFTP: file: " + outFile.getName());
                    return true;
                } else {
                    throw new RuntimeException("Failed to upload file to SetFtp , error message:get SetFtp json Info error" );
                }

            } catch (Exception e) {
                throw new RuntimeException("Failed to upload file to SetFtp , error message:" + e.getMessage());
            }

        }
        //System.out.println("=====return false !!!!");
        return true;
    }

    private void sleep(int maxSec) {
        try {
            long sleepTime = RandomUtil.getRandom(true).nextLong(1000, maxSec * 1000);
            Thread.sleep(sleepTime);
        } catch (Exception e) {

        }
    }

    private class Event {
        String message;
        Long storageId;
        String storageName;

        public String getGsPath() {
            return gsPath;
        }
        public void setGsPath(String gsPath) {
            this.gsPath = gsPath;
        }
        String gsPath;
        Integer totalCount;
        Integer taskInstanceId = 0;
        String taskId = "";
        DateTime completeTime = null;
        List<String> files = new ArrayList<>();

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public Integer getTaskInstanceId() {
            return taskInstanceId;
        }

        public Long getStorageId() {
            return storageId;
        }

        public void setStorageId(Long storageId) {
            this.storageId = storageId;
        }

        public String getStorageName() {
            return storageName;
        }

        public void setStorageName(String storageName) {
            this.storageName = storageName;
        }

        public void setTaskInstanceId(Integer taskInstanceId) {
            this.taskInstanceId = taskInstanceId;
        }

        public Integer getTotalCount() {
            return totalCount;
        }

        public void setTotalCount(Integer totalCount) {
            this.totalCount = totalCount;
        }

        public String getTaskId() {
            return taskId;
        }

        public void setTaskId(String taskId) {
            this.taskId = taskId;
        }

        public DateTime getCompleteTime() {
            return completeTime;
        }

        public void setCompleteTime(DateTime completeTime) {
            this.completeTime = completeTime;
        }

        public List<String> getFiles() {
            return files;
        }

        public void setFiles(List<String> files) {
            this.files = files;
        }
    }
}