package seoclarity.backend.asyncapi;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

public class SQLUtils {
    public static String testStr;
    public static void main(String[] args) {
        try {
            testStr = args[0];
            while (true) {

                System.out.println("test====="+testStr);
                Thread.sleep(1000);
            }

        }catch (Exception ex){
            ex.printStackTrace();
        }
    }
}