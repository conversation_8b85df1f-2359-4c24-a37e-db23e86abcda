package seoclarity.backend.asyncapi;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import seoclarity.backend.dao.actonia.ApiTaskInstanceEntityDAO;
import seoclarity.backend.dao.clickhouse.CICentralBaseJdbcSupport;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.clickhouse.monthlyranking.CentralKeywordTokenizerDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntityDAO;
import seoclarity.backend.entity.actonia.ApiTaskInfoEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntity;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.utils.*;

import java.io.*;
import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.sql.PreparedStatement;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import static seoclarity.backend.utils.FileUtils.createFile;
import static seoclarity.backend.utils.FileUtils.outPutfile;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.asyncapi.AsynTaskProcessor" -Dexec.args="102"
class PostParam{

    String keyword = "";

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public List<Content> getContent() {
        return content;
    }

    public void setContent(List<Content> content) {
        this.content = content;
    }

    List<Content> content;
}
class Content{
    int documentRank;
    String url = "";

    public int getDocumentRank() {
        return documentRank;
    }

    public void setDocumentRank(int documentRank) {
        this.documentRank = documentRank;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public List<String> getTitle() {
        return title;
    }

    public void setTitle(List<String> title) {
        this.title = title;
    }

    public List<String> getDescription() {
        return description;
    }

    public void setDescription(List<String> description) {
        this.description = description;
    }

    List<String> title;
    List<String> description;
}
public class TestApi {
    private static final int MAX_LIST_SIZE = 1000;
    private static final int GET_TIME_OUT = 10 * 1000;
    private static final String END_POINT = "https://event.seoclarity.workers.dev";
    private static final String LIST_BY_KEY_WITH_CURSOR = "/cursor/{cursor}?{prefix}";
    private static final String GET_BY_KEY = "/key/{task_id}";
    private static final String LIST_BY_KEY = "/list/{prefix}";
    private static final String zeroRanksql = "as csvurl, '-' as url0,'-' rank0  format JSONEachRow";
    //***************************************************
    public static final String BASEPREFIX = "asyncdownload_contentgap_estdTrafficKWDetailNew_ownCustom_";
    private static final String LOCAL_STORE_DIR = "/tmp/testMarven/";
    //***************************************************
    private static SeoClarityKeywordMonthlySearchEngineRelationEntityDAO seoClarityKeywordMonthlySearchEngineRelationEntityDAO;
    private static final String DELETE_BY_KEY = "/delete/{task_id}";
    public static final int DOWNLOADMAXCOUNT = 10;
    private static  List<String> listCache = null;
    private static final String HEADER_TOKEN_KEY = "seoclarity-internal-token";
    private static final String HEADER_TOKEN_VALUE = "6603b708dd0bf24b0e7a1e68408c454e";
    //private static Integer READ_TIMEOUT = 1800;
    public static int HTTP_EXPORT_RESPONSE_SCUCCESS=200;
    public static int HTTP_EXPORT_RESPONSE_ERROR=400;
    public static int HTTP_EXPORT_CLARITYDB_TO_FILE_FAILD=-2;
    public static int HTTP_EXPORT_CLARITYDB_TO_FILE_EXCEPTION=-1;
    private static final Map<String, String> CACHE_HEADER_MAP = new HashMap<>();
//    static {
//        CACHE_HEADER_MAP.put(HEADER_TOKEN_KEY, HEADER_TOKEN_VALUE);
//    }
    ClarityDBConnection connection = null;
//    public static String USER = "default";
//    public static String PSW = "clarity99!";
    public static String FILE_FORMATE_CSV = "CSV";// "format": "CSV",    -- optional            [CSV,JSON]
    public static String FILE_FORMATE_JSON = "JSON";
    //public static String g="";

    private final static int INTERVAL = 50000;

    public TestApi() {
            //clDailydao =new ClDailyRankingEntityDao();
            //clarityDBCentralDao = new CentralKeywordTokenizerDAO() ;
    }


    public static int currentCounr =0;
    private static boolean ownCustomExport(int begin ,int end,List<CLRankingDetailEntity> listKname,List<CLRankingDetailEntity> listDetail) {
        String logBasePath = "=====backend testAPi initPost";
        String currKname = "";
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss:sss");
            System.out.println(logBasePath);
            String fullpath = fulloutfilename +begin+"_"+end+".txt";
            File file = createFile(fullpath);
            for (CLRankingDetailEntity kname:listKname) {
                System.out.println(logBasePath+"====TestApi kname=====" +kname.getKeywordName());
                currKname = kname.getKeywordName();
                List<CLRankingDetailEntity> listdata = listDetail.stream().filter(x -> x.getKeywordName().equals(kname.getKeywordName()) )
                        .collect(Collectors.toList());
                System.out.println(logBasePath+"=====listdata"+JSONUtil.toJsonStr(listdata)+"=====");
                PostParam postparam = new PostParam();
                List<Content> listContent = new ArrayList<>();
                for (CLRankingDetailEntity map:listdata) {
                    Content content = new Content();
                    List<String> listTitle = new ArrayList<>();
                    List<String> listdescription = new ArrayList<>();
                    listTitle.add(map.getLabel());
                    listdescription.add(map.getMeta());
                    content.setUrl(map.getUrl());
                    content.setDocumentRank(map.getWebRank());
                    content.setTitle(listTitle); ;
                    content.setDescription(listdescription);
                    System.out.println(logBasePath+"=====content"+JSONUtil.toJsonStr(content)+"=====");
                    listContent.add(content);
                }
                postparam.setKeyword(kname.getKeywordName());
                postparam.content = listContent;
                postparam.setContent(listContent);
                System.out.println(logBasePath+"=====postparam:"+JSONUtil.toJsonStr(postparam)+"=====");
                try{
                    testApi(url, kname.getKeywordName(),postparam,begin,end,file);
                    successcount++;
                }catch (Exception e){
                    faildcount++;
                }
                //break;
            }

            return true;
        } catch (Exception ex) {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss:sss");
            //
            System.out.println(logBasePath+"*******Failed keyword_name is  ===="+currKname +",fail time is:"+formatter.format(new Date())+",error message"+ex.getMessage());
            ex.printStackTrace();
            return  false;
        }
    }

    public static JSONObject httpPostRequest(String param, String url, String token) throws Exception {
        JSONObject jsonObject = new JSONObject();
        try (final CloseableHttpClient httpClient = HttpClients.createDefault()) {
            final HttpPost httpPost = new HttpPost(url);
            JSONObject jsonString = JSON.parseObject(param);
            //设置请求体参数
            StringEntity entity = new StringEntity(param);
            entity.setContentEncoding("utf-8");
            httpPost.setEntity(entity);
            //设置请求头部
            httpPost.setHeader("Content-Type", "application/json");
            if(token != null && !"".equals(token)){
                httpPost.setHeader("Authorization",token);
            }
            //执行请求，返回请求响应
            try (final CloseableHttpResponse response = httpClient.execute(httpPost)) {
                //请求返回状态码
                int statusCode = response.getStatusLine().getStatusCode();
                //请求成功
                if (statusCode == HttpStatus.SC_OK && statusCode <= HttpStatus.SC_TEMPORARY_REDIRECT) {
                    //取出响应体
                    final HttpEntity entity2 = response.getEntity();
                    //从响应体中解析出token
                    String responseBody = EntityUtils.toString(entity2, "utf-8");
                    jsonObject = JSONObject.parseObject(responseBody);
                    //token = jsonObject.getString("access_token");
                } else {
                    //请求失败
                    throw new ClientProtocolException("ProtocolException：" + statusCode);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
            //throw new ClientProtocolException("ProtocolException：" + e.getMessage());

        }
        return jsonObject;
    }

   public static void testApi(String baseurl,String keyname,PostParam postParam,int begin,int end,File file) throws Exception{
       System.out.println("=====backend testAPi testApi post body:"+JSONUtil.toJsonStr(postParam));
       currentCounr++;
       System.out.println("=====backend testAPi testApi current count :"+currentCounr);
       //String url ="https://faas-blr1-8177d592.doserverless.co/api/v1/web/fn-8e5a53a2-120e-46a5-8348-b62f01fd7c7d/keywordOccurrenceFinderNew/finder";

       JSONObject obj = httpPostRequest(JSONUtil.toJsonStr(postParam),url,null);
       System.out.println("=====backend testAPi testApi response body:"+JSONUtil.toJsonStr(obj));
       String outputStr = "keyWordName :\""+keyname+"\"---->response body:"+JSONUtil.toJsonStr(obj);
       List<String> outputList= new ArrayList<>();
       outputList.add(outputStr);
       outPutfile(file,outputList,true);
   }
    public static List<String> loadFileListV1(String pathname) {
        List<String> list = new ArrayList<>();
        String line = null;
        try {
            InputStream in = new FileInputStream(pathname);
            BufferedReader reader = new BufferedReader(new InputStreamReader(in));
            while((line = reader.readLine()) != null){
                list.add(line);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return list.stream().distinct().collect(Collectors.toList());
    }



    public static ClDailyRankingEntityDao clDailydao;
    static List<CLRankingDetailEntity> listKname ;
    static List<CLRankingDetailEntity> listDetail;
    static  List<String> list = new ArrayList<>();
    //初始化
//    public static void doTaskRank10(List<CLRankingDetailEntity> listKnametemp ,int begin,int end,int i){
//        System.out.println("=====backend testAPi postParam sub list size is"+listKnametemp.size());
//        SimpleDateFormat formatter = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss:sss");
//        System.out.println("=====backend testAPi postParam costinfo thread begin:"+begin +"--end :"+end+" started  ,"+formatter.format(new Date()));
//        long startt = System.currentTimeMillis() / 1000;
//        File file = createFile(fulloutfilename+"keywodNameList.txt");
//        outPutfile(file,list,true);
//        ownCustomExport(begin,end,listKnametemp,listDetail);
//        System.out.println("=====backend testAPi postParam costinfo thread :"+begin +"__"+end+" finished at  ,"+formatter.format(new Date()));
//        long endt = System.currentTimeMillis() / 1000;
//        finished[i]=1;
//        System.out.println("=====backend testAPi postParam costinfo thread end cost :"+begin +" :" + ((endt - startt) / 60) + "m" + ((endt - startt) % 60) + "s");
//    }
    //init
    static {
//        clDailydao =SpringBeanFactory.getBean("clDailyRankingEntityDao");
//        listKname = clDailydao.getTop10000KnKnameV1();
//        listDetail = clDailydao.getTop10000KnDetailV1();
//        for (CLRankingDetailEntity one:listKname) {
//            list.add(one.getKeywordName());
//        }

    }

    private static  String  fulloutfilename = "/home/<USER>/outfiles/";
    private static final String  fullinputfilename = "/home/<USER>/inputfiles/";
    public static int finished[] = new int[10];
    public static long finishedCost[] = new long[10];
    public static int faildcount = 0;
    public static int successcount =0;
    private static final Integer READ_TIMEOUT = 1800;
    private static final String CH_DB_RG_URL = "http://184.154.118.206:8123"; // TODO
    private static final String CH_DB_RG = "prod"; // TODO
    private static final String USER = "default";
    private static final String PSW = "clarity99!";
    private  static String url ="https://keyword-occurrence-finder.seoclarity.workers.dev/api/v1/web/fn-8e5a53a2-120e-46a5-8348-b62f01fd7c7d/keywordOccurrenceFinderNew/finder";
    public static void main(String args[]) {
        String errs[] = new String[100];
         String sql1 = "select  distinct title, url, custom_data.selector, custom_data.content, custom_data.links, \n" +
                "custom_data.match_found, custom_data.selector_type, custom_data.index, custom_data.word_count " +
                "from dis_site_crawl_doc where ( not empty(custom_data.content)) AND hasAny(custom_data.match_found, [1]) \n" +
                "AND (arrayElement(custom_data.match_found, indexOf(custom_data.selector, '//div[@data-component-title=\\'SEO\\']'))=1) \n" +
                "AND ( crawl_request_id = 9945663 ) AND ( not empty(url)) AND ( domain_id = 9688 )  order by title,url";
//         String sql2 = "select  distinct title, url, custom_data.selector, custom_data.content, custom_data.links, \n" +
//                "custom_data.match_found, custom_data.selector_type, custom_data.index, custom_data.word_count from dis_site_crawl_doc where ( not empty(custom_data.content)) \n" +
//                "AND hasAny(custom_data.match_found, [1]) \n" +
//                "AND (arrayElement(custom_data.match_found, indexOf(custom_data.selector, '//h2[contains(text(), \\'Trending Searches\\')]'))=1) \n" +
//                "AND ( crawl_request_id = 9945663 ) AND ( not empty(url)) AND ( domain_id = 9688 )  order by title,url";
        try{
            Integer rowCount = ClarityDBUtils.httpExportFromClarityDB(CH_DB_RG_URL, CH_DB_RG, USER, PSW, sql1, "/tmp/extract3.txt", true, false, errs);
            //rowCount = ClarityDBUtils.httpExportFromClarityDB(CH_DB_RG_URL, CH_DB_RG, USER, PSW, sql2, "/tmp/extract4.txt", true, false, errs);
        }catch (Exception ex){
            ex.printStackTrace();
        }

//         Integer rowCount = ClarityDBUtils.httpExportFromClarityDB(CH_DB_RG_URL, CH_DB_RG, USER, PSW, sql1, "/tmp/extract1.txt", true, false, errs);
//        rowCount = ClarityDBUtils.httpExportFromClarityDB(CH_DB_RG_URL, CH_DB_RG, USER, PSW, sql2, "/tmp/extract2.txt", true, false, errs);
        //        try{
//            SimpleDateFormat formatterdate = new SimpleDateFormat("yyyyMMdd");
//            fulloutfilename = fulloutfilename +"/"+formatterdate.format(new Date())+"/testApi/";
//            SimpleDateFormat formatter = new SimpleDateFormat("yyyy.MM.dd HH:mm:ss:sss");
//            System.out.println("=====backend testAPi main costinfo start time:"+formatter.format(new Date())+"=====");
//            long start = System.currentTimeMillis() / 1000;
//            for (int i = 0; i < 10; i++) {
//                finishedCost[i]=0;
//            }
//            for (int i = 0; i < 10; i++) {
//                finished[i] = 0;
//                int begin = i *1000;
//                int end = begin +1000;
//                List<CLRankingDetailEntity> listKnametemp = listKname.subList(begin,end);
//                int finalI = i;
//                Thread thread = new Thread() {
//                    public void run() {
//                        try {
//                            doTaskRank10(listKnametemp,begin,end, finalI);
//                        } catch (Exception e) {
//                            System.out.println("=====backend testAPi main backend testAPi test failed: begin"+begin+",end:"+end+"," +e.getMessage());
//                        }
//                    }
//                };
//                thread.start();
//            }
//
//            while(true){
//                System.out.println("=====checking main is finished=====");
//                boolean checkflag = true;
//                for (int j = 0; j < 10; j++) {
//                    if(finished[j]==1){
//                        if(finishedCost[j]==0){
//                            finishedCost[j]=System.currentTimeMillis() ;
//                            long end = System.currentTimeMillis() / 1000;
//                            System.out.println("=====backend testAPi main thread costinfo:"+ j+"_"+(j+999)+" thread is finished at "+formatter.format(new Date())+",1000 keyWords cost time is:"+ + ((end - start) / 60) + "m" + ((end - start) % 60) + "s" );
//                        }
//                    }
//                }
//
//                for (int j = 0; j < 10; j++) {
//                    if(finished[j]==0) {
//                        checkflag = false;
//                        break;
//                    }
//                }
//                if (checkflag) {
//                    break;
//                }
//                try{
//                    Thread.sleep(100);
//                }catch (Exception ex){
//                    ex.printStackTrace();
//                }
//            }
//            long end11 = System.currentTimeMillis() / 1000;
//            System.out.println("=====backend testAPi main costinfo end time:"+formatter.format(new Date())+"=====");
//            System.out.println("=====backend testAPi main costinfo all api cost time:" + ((end11 - start) / 60) + "m" + ((end11 - start) % 60) + "s");
//            System.out.println("=====backend testAPi main costinfo total: keyWords count is:"+currentCounr+"," +
//                    "fail count:"+faildcount+"," +
//                    "success count is:"+successcount);
//        }catch (Exception ex){
//            ex.printStackTrace();
//            System.out.println("err");
//        }
    }
}

