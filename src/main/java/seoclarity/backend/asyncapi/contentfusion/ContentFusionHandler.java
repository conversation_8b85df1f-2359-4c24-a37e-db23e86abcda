package seoclarity.backend.asyncapi.contentfusion;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.google.common.collect.ImmutableMap;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import com.google.gson.Gson;

import seoclarity.backend.asyncapi.AsynTaskProcessor;
import seoclarity.backend.dao.actonia.ApiTaskInfoEntityDAO;
import seoclarity.backend.entity.actonia.ApiTaskInfoEntity;
import seoclarity.backend.utils.*;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.asyncapi.contentfusion.ContentFusionHandler" -Dexec.args=""
public class ContentFusionHandler {
	// https://www.wrike.com/open.htm?id=1252311888
	private static final int DEFAULT_CACHE_DATE = 30;
	private static final int CACHE_TIMES = 3600 * 24 * DEFAULT_CACHE_DATE;

	private static Integer TYPE_CONTENTFUSION_KEYWORD = 1;
	private static Integer TYPE_CONTENTFUSION_KEYWORD_REALTIME = 2;
	public final static String TASKNAME_CONTENTFUSION_KEYWORD = "Contentfusion_Keyword"; // https://www.wrike.com/open.htm?id=908593946
	public final static String TASKNAME_CONTENTFUSION_KEYWORD_REALTIME = "Contentfusion_Keyword_Realtime";
	private static final ImmutableMap<String, String> INTERNAL_TOKEN_HEADER = ImmutableMap.<String, String>builder().put("INTERNAL_TOKEN", ClarityDBAPIUtils.ACCESS_TOKEN_VALUE).build();

	public ApiTaskInfoEntityDAO apiTaskInfoEntityDAO;

	public ContentFusionHandler() {
		apiTaskInfoEntityDAO = SpringBeanFactory.getBean("apiTaskInfoEntityDAO");
	}

	public static void main(String[] args) {
//		String type = "";
//		if (args != null && args.length > 0) {
//			type = args[0];
//		} else {
//			System.out.println("Not choose task!");
//			return;
//		}

		ContentFusionHandler contentFusionHandler = new ContentFusionHandler();
		contentFusionHandler.autoProcess();
	}

	private void autoProcess() {
		while (true) {

			process(TYPE_CONTENTFUSION_KEYWORD);
			process(TYPE_CONTENTFUSION_KEYWORD_REALTIME);
			try {
				System.out.println(" Sleep 30 sec ");
				Thread.sleep(30 * 1000);
			} catch (Exception e) {
				e.printStackTrace();
			}

		}
	}

	private void process(Integer type) {
		String taskName = "";
		if (type == TYPE_CONTENTFUSION_KEYWORD) {
			taskName = TASKNAME_CONTENTFUSION_KEYWORD;
		} else if (type == TYPE_CONTENTFUSION_KEYWORD_REALTIME) {
			taskName = TASKNAME_CONTENTFUSION_KEYWORD_REALTIME;
		}

		System.out.println("### taskName:" + taskName);

		ApiTaskInfoEntity apiTaskInfoEntity = apiTaskInfoEntityDAO.getApiTaskInfoByTaskName(taskName);
		handleContentFusionApi(apiTaskInfoEntity);

	}

	private static String LAST_TASKID = "";
	public static final int TRY_LIMIT = 3;
	// DEV_API_SERVER = "http://dev-internal-lb-190333-dal13.clb.appdomain.cloud";
//    private static String APIPATH_CONTENTFUSION_KEYWORD = "http://api.seoclarity.net/seoClarity/contentfusion/keywords";
//    private static String APIPATH_CONTENTFUSION_KEYWORD_REALTIME = "http://api.seoclarity.net/seoClarity/contentfusion/keywordsRealtime";
	private static String APIPATH_CONTENTFUSION_KEYWORD_V2 = "http://api-internal-lb-190333-dal13.clb.appdomain.cloud/seoClarity/contentfusion/keywordsV2";
	private static String APIPATH_CONTENTFUSION_KEYWORD = "http://api-internal-lb-190333-dal13.clb.appdomain.cloud/seoClarity/contentfusion/keywords";
	private static String APIPATH_CONTENTFUSION_KEYWORD_REALTIME = "http://api-internal-lb-190333-dal13.clb.appdomain.cloud/contentfusion/keywordsRealtime";

	private static String FOLDER_TEMP = "/tmp/";
	public static final char FILE_SPLIT = '\t';
	public static final String ENCODING = "UTF-8";

	public static void handleContentFusionApi(ApiTaskInfoEntity apiTaskInfoEntity) {

		String cacheKeyPrefix = apiTaskInfoEntity.getCacheKeyPrefix();
		String taskKeyPrefix = apiTaskInfoEntity.getTaskKeyPrefix();
		String apiPath = "";

		

		List<String> taskList = TaskUtil.listKeys(cacheKeyPrefix);

		if (taskList == null || taskList.size() == 0) {
			System.out.println(" NoNewTask ");
			return;
		} else {
			System.out.println(" ==fd Tsk, sz:" + taskList.size());

			System.out.println("taskList:" + new Gson().toJson(taskList));
			for (String task : taskList) {

				System.out.println("===start process task:" + task);

				String finishTaskId = StringUtils.replace(task, cacheKeyPrefix, taskKeyPrefix);

				if (StringUtils.equalsIgnoreCase(LAST_TASKID, finishTaskId)) {
					continue;
				}

				String paramJson = TaskUtil.get(task.getBytes());

				try {
					int num = 0;
					while (true) {
						num++;

						Map map = new Gson().fromJson(paramJson, Map.class);
						boolean needFile = BooleanUtils
								.toBoolean(map.get("needFile") != null ? map.get("needFile").toString() : "false");
						
						if (StringUtils.equals(apiTaskInfoEntity.getTaskName(), AsynTaskProcessor.TASKNAME_CONTENTFUSION_KEYWORD)) {
							if (needFile) {
								apiPath = APIPATH_CONTENTFUSION_KEYWORD_V2;
							} else {
								apiPath = APIPATH_CONTENTFUSION_KEYWORD;
							}
						} else if (StringUtils.equals(apiTaskInfoEntity.getTaskName(),
								AsynTaskProcessor.TASKNAME_CONTENTFUSION_KEYWORD_REALTIME)) {
							apiPath = APIPATH_CONTENTFUSION_KEYWORD_REALTIME;
						} else {
							return;
						}

						System.out.println("=======================================");
						System.out.println("apiPath:" + apiPath);

						Map<String, String> resultMap = getRespFromApi(paramJson, apiPath);
						if (!StringUtils.equals(resultMap.get("responseCode"), "200")) {

							if (num >= 10) {
								System.out.println("@@@@@@@@ not get data from API correctly");

								ZeptoMailSenderComponent.sendEmailReport(new Date(), "ContentFusionHandler Asyn task ERROR",
										"task : " + task, new Gson().toJson(resultMap));
								break;
							}

							System.out.println("responseCode:" + resultMap.get("responseCode"));
							System.out.println("response:" + resultMap.get("response"));
							System.out.println("Retrying, sleep 5 sec, retry cnt:" + num);
							Thread.sleep(15 * 1000);
							continue;
						}

						// resultMap.put("responseCode", responseCode + "");
						// resultMap.put("response", html);

						if (resultMap != null && StringUtils.isNotBlank(resultMap.get("response"))
								&& StringUtils.isNotBlank(resultMap.get("responseCode"))) {
							String resp = StringUtils.removeEnd(resultMap.get("response"), "}")
									+ ", \"api_responsecode\":" + resultMap.get("responseCode") + "}";

							if (needFile) {
								File tempFolder = new File(FOLDER_TEMP);
								File tempfile = File.createTempFile("ContentFunsion_", ".txt", tempFolder);
								System.out.println("Create file:" + tempfile.getAbsolutePath());
								List<String> respResult = new ArrayList<String>();
								respResult.add(resp);

								FileUtils.writeLines(tempfile, ENCODING, respResult, true);
								System.out.println("Write into temp file!");
								
								String zipFilePath = FOLDER_TEMP + tempfile.getName() + GZipUtil.GZFile_POSTFIX;
								
								File zipFile = new File(zipFilePath);
								if (zipFile != null && zipFile.exists()) {
									
									System.out.println("File : " + zipFilePath +" is already exist. Delete and rezip now.");
									zipFile.delete();
								}
								
								GZipUtil.zipFiles(FOLDER_TEMP + tempfile.getName(), zipFilePath);
								

								SeagateUtils.saveFileToDefaultSeagate(0, zipFile.getAbsolutePath());
								String linkText = SeagateUtils.getDefaultSeagatePresignedUrl(0, zipFile.getName());
								
								TaskVO taskVO = new TaskVO();
								taskVO.setTaskId(task);
								taskVO.setFiles(new String[] {linkText});
								TaskUtil.setValue(finishTaskId.getBytes(), new Gson().toJson(taskVO), CACHE_TIMES);
							} else {
								TaskUtil.setValue(finishTaskId.getBytes(), resp, CACHE_TIMES);
							}

							break;
						} else {
							System.out.println("Response is empty! key:" + task);
							System.out.println("paramJson:" + paramJson);
						}

					}

				} catch (Exception e) {
					e.printStackTrace();
				} finally {
					System.out.println("Delete task:" + task);
					TaskUtil.deleteKey(task);

				}

				LAST_TASKID = task;
			}

		}

	}


	public static Map<String, String> getRespFromApi(String stringBody, String apiUrl) {
		System.out.println("==============apiUrl:" + apiUrl + ", stringBody:" + stringBody);
		int tryCnt = 0;
		while (true) {
			tryCnt++;
			if (tryCnt > TRY_LIMIT) {
				break;
			}
			try {
				Map<String, String> resultMap = HttpRequestUtils.queryWebServiceFunctionPostMap(apiUrl, stringBody,
						null, INTERNAL_TOKEN_HEADER);

				// resultMap.put("responseCode", responseCode + "");
				// resultMap.put("response", html);
				if (resultMap == null || StringUtils.isBlank(resultMap.get("response"))) {
					System.out.println("===getRespFromApi failed, tryCnt:" + tryCnt);
					Thread.sleep(10000);
					continue;
				} else {
					return resultMap;
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}

}
