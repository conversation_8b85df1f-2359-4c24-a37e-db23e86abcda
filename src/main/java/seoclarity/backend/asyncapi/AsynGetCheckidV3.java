package seoclarity.backend.asyncapi;

import cn.hutool.json.JSON;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.testcontainers.shaded.okhttp3.*;
import seoclarity.backend.dao.clickhouse.ClDailyRankingEntityDao;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntityDAO;
import seoclarity.backend.entity.actonia.ApiTaskInfoEntity;
import seoclarity.backend.entity.clickhouse.CLRankingDetailEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntity;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.net.SocketTimeoutException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static seoclarity.backend.utils.FileUtils.createFile;
import static seoclarity.backend.utils.FileUtils.outPutfile;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.asyncapi.AsynTaskProcessor" -Dexec.args="102"
public class AsynGetCheckidV3 {
    private static final int MAX_LIST_SIZE = 1000;
    private static final int GET_TIME_OUT = 10 * 1000;
    private static final String END_POINT = "https://event.seoclarity.workers.dev";
    private static final String LIST_BY_KEY_WITH_CURSOR = "/cursor/{cursor}?{prefix}";
    private static final String GET_BY_KEY = "/key/{task_id}";
    private static final String LIST_BY_KEY = "/list/{prefix}";
    private static final String zeroRanksql = "as csvurl, '-' as url0,'-' rank0  format JSONEachRow";
    //***************************************************
    public static final String BASEPREFIX = "asyncdownload_contentgap_estdTrafficKWDetailNew_ownCustom_";
    private static final String LOCAL_STORE_DIR = "/tmp/testMarven/";
    //***************************************************
    private static SeoClarityKeywordMonthlySearchEngineRelationEntityDAO seoClarityKeywordMonthlySearchEngineRelationEntityDAO;
    private static final String DELETE_BY_KEY = "/delete/{task_id}";
    public static final int DOWNLOADMAXCOUNT = 10;
    private static  List<String> listCache = null;
    private static final String HEADER_TOKEN_KEY = "seoclarity-internal-token";
    private static final String HEADER_TOKEN_VALUE = "6603b708dd0bf24b0e7a1e68408c454e";
    private static Integer READ_TIMEOUT = 1800;
    public static int HTTP_EXPORT_RESPONSE_SCUCCESS=200;
    public static int HTTP_EXPORT_RESPONSE_ERROR=400;
    public static int HTTP_EXPORT_CLARITYDB_TO_FILE_FAILD=-2;
    public static int HTTP_EXPORT_CLARITYDB_TO_FILE_EXCEPTION=-1;
    private static final Map<String, String> CACHE_HEADER_MAP = new HashMap<>();

    private static ClDailyRankingEntityDao clDailyRankingEntityDao;
    private static final String listMarket = "[\n" +
            "    {\n" +
            "        \"market\": \"us-en\",\n" +
            "        \"searchEngine\": \"google.com\",\n" +
            "        \"engine_id\": 1,\n" +
            "        \"country_name\": \"us\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 1\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"au-en\",\n" +
            "        \"searchEngine\": \"google.com.au\",\n" +
            "        \"engine_id\": 2,\n" +
            "        \"country_name\": \"au\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 5\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ca-en\",\n" +
            "        \"searchEngine\": \"google.ca\",\n" +
            "        \"engine_id\": 3,\n" +
            "        \"country_name\": \"ca\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 3\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ca-fr\",\n" +
            "        \"searchEngine\": \"google.ca\",\n" +
            "        \"engine_id\": 3,\n" +
            "        \"country_name\": \"ca\",\n" +
            "        \"language_name\": \"fr\",\n" +
            "        \"language_id\": 4\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"fr-fr\",\n" +
            "        \"searchEngine\": \"google.fr\",\n" +
            "        \"engine_id\": 4,\n" +
            "        \"country_name\": \"fr\",\n" +
            "        \"language_name\": \"fr\",\n" +
            "        \"language_id\": 7\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"uk-en\",\n" +
            "        \"searchEngine\": \"google.co.uk\",\n" +
            "        \"engine_id\": 6,\n" +
            "        \"country_name\": \"uk\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 8\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"it-it\",\n" +
            "        \"searchEngine\": \"google.it\",\n" +
            "        \"engine_id\": 8,\n" +
            "        \"country_name\": \"it\",\n" +
            "        \"language_name\": \"it\",\n" +
            "        \"language_id\": 9\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"dk-da\",\n" +
            "        \"searchEngine\": \"google.dk\",\n" +
            "        \"engine_id\": 9,\n" +
            "        \"country_name\": \"dk\",\n" +
            "        \"language_name\": \"da\",\n" +
            "        \"language_id\": 10\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"fi-fi\",\n" +
            "        \"searchEngine\": \"google.fi\",\n" +
            "        \"engine_id\": 10,\n" +
            "        \"country_name\": \"fi\",\n" +
            "        \"language_name\": \"fi\",\n" +
            "        \"language_id\": 11\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"mx-es\",\n" +
            "        \"searchEngine\": \"google.com.mx\",\n" +
            "        \"engine_id\": 11,\n" +
            "        \"country_name\": \"mx\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 12\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"no-no\",\n" +
            "        \"searchEngine\": \"google.no\",\n" +
            "        \"engine_id\": 12,\n" +
            "        \"country_name\": \"no\",\n" +
            "        \"language_name\": \"no\",\n" +
            "        \"language_id\": 13\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"no-en\",\n" +
            "        \"searchEngine\": \"google.no\",\n" +
            "        \"engine_id\": 12,\n" +
            "        \"country_name\": \"no\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 144\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"se-sv\",\n" +
            "        \"searchEngine\": \"google.se\",\n" +
            "        \"engine_id\": 13,\n" +
            "        \"country_name\": \"se\",\n" +
            "        \"language_name\": \"sv\",\n" +
            "        \"language_id\": 14\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"de-de\",\n" +
            "        \"searchEngine\": \"google.de\",\n" +
            "        \"engine_id\": 14,\n" +
            "        \"country_name\": \"de\",\n" +
            "        \"language_name\": \"de\",\n" +
            "        \"language_id\": 15\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"br-pt\",\n" +
            "        \"searchEngine\": \"google.com.br\",\n" +
            "        \"engine_id\": 15,\n" +
            "        \"country_name\": \"br\",\n" +
            "        \"language_name\": \"pt\",\n" +
            "        \"language_id\": 16\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"es-es\",\n" +
            "        \"searchEngine\": \"google.es\",\n" +
            "        \"engine_id\": 16,\n" +
            "        \"country_name\": \"es\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 17\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"nl-nl\",\n" +
            "        \"searchEngine\": \"google.nl\",\n" +
            "        \"engine_id\": 17,\n" +
            "        \"country_name\": \"nl\",\n" +
            "        \"language_name\": \"nl\",\n" +
            "        \"language_id\": 18\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"jp-ja\",\n" +
            "        \"searchEngine\": \"google.co.jp\",\n" +
            "        \"engine_id\": 18,\n" +
            "        \"country_name\": \"jp\",\n" +
            "        \"language_name\": \"ja\",\n" +
            "        \"language_id\": 19\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"pt-pt\",\n" +
            "        \"searchEngine\": \"google.pt\",\n" +
            "        \"engine_id\": 19,\n" +
            "        \"country_name\": \"pt\",\n" +
            "        \"language_name\": \"pt\",\n" +
            "        \"language_id\": 20\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ie-en\",\n" +
            "        \"searchEngine\": \"google.ie\",\n" +
            "        \"engine_id\": 20,\n" +
            "        \"country_name\": \"ie\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 21\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"be-nl\",\n" +
            "        \"searchEngine\": \"google.be\",\n" +
            "        \"engine_id\": 21,\n" +
            "        \"country_name\": \"be\",\n" +
            "        \"language_name\": \"nl\",\n" +
            "        \"language_id\": 22\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"be-fr\",\n" +
            "        \"searchEngine\": \"google.be\",\n" +
            "        \"engine_id\": 21,\n" +
            "        \"country_name\": \"be\",\n" +
            "        \"language_name\": \"fr\",\n" +
            "        \"language_id\": 96\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ch-de\",\n" +
            "        \"searchEngine\": \"google.ch\",\n" +
            "        \"engine_id\": 22,\n" +
            "        \"country_name\": \"ch\",\n" +
            "        \"language_name\": \"de\",\n" +
            "        \"language_id\": 23\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ch-fr\",\n" +
            "        \"searchEngine\": \"google.ch\",\n" +
            "        \"engine_id\": 22,\n" +
            "        \"country_name\": \"ch\",\n" +
            "        \"language_name\": \"fr\",\n" +
            "        \"language_id\": 97\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"kr-ko\",\n" +
            "        \"searchEngine\": \"google.co.kr\",\n" +
            "        \"engine_id\": 23,\n" +
            "        \"country_name\": \"kr\",\n" +
            "        \"language_name\": \"ko\",\n" +
            "        \"language_id\": 24\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"in-en\",\n" +
            "        \"searchEngine\": \"google.co.in\",\n" +
            "        \"engine_id\": 24,\n" +
            "        \"country_name\": \"in\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 25\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ar-es\",\n" +
            "        \"searchEngine\": \"google.com.ar\",\n" +
            "        \"engine_id\": 25,\n" +
            "        \"country_name\": \"ar\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 26\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"cl-es\",\n" +
            "        \"searchEngine\": \"google.cl\",\n" +
            "        \"engine_id\": 26,\n" +
            "        \"country_name\": \"cl\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 27\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"co-es\",\n" +
            "        \"searchEngine\": \"google.com.co\",\n" +
            "        \"engine_id\": 27,\n" +
            "        \"country_name\": \"co\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 28\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"pr-es\",\n" +
            "        \"searchEngine\": \"google.com.pr\",\n" +
            "        \"engine_id\": 28,\n" +
            "        \"country_name\": \"pr\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 29\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"at-de\",\n" +
            "        \"searchEngine\": \"google.at\",\n" +
            "        \"engine_id\": 29,\n" +
            "        \"country_name\": \"at\",\n" +
            "        \"language_name\": \"de\",\n" +
            "        \"language_id\": 30\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"cn-zh\",\n" +
            "        \"searchEngine\": \"google.com.hk\",\n" +
            "        \"engine_id\": 30,\n" +
            "        \"country_name\": \"cn\",\n" +
            "        \"language_name\": \"zh\",\n" +
            "        \"language_id\": 6\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"id-id\",\n" +
            "        \"searchEngine\": \"google.co.id\",\n" +
            "        \"engine_id\": 31,\n" +
            "        \"country_name\": \"id\",\n" +
            "        \"language_name\": \"id\",\n" +
            "        \"language_id\": 31\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"my-en\",\n" +
            "        \"searchEngine\": \"google.com.my\",\n" +
            "        \"engine_id\": 32,\n" +
            "        \"country_name\": \"my\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 32\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ph-tl\",\n" +
            "        \"searchEngine\": \"google.com.ph\",\n" +
            "        \"engine_id\": 33,\n" +
            "        \"country_name\": \"ph\",\n" +
            "        \"language_name\": \"tl\",\n" +
            "        \"language_id\": 33\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ph-en\",\n" +
            "        \"searchEngine\": \"google.com.ph\",\n" +
            "        \"engine_id\": 33,\n" +
            "        \"country_name\": \"ph\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 101\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"tw-zh\",\n" +
            "        \"searchEngine\": \"google.com.tw\",\n" +
            "        \"engine_id\": 34,\n" +
            "        \"country_name\": \"tw\",\n" +
            "        \"language_name\": \"zh\",\n" +
            "        \"language_id\": 34\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"th-th\",\n" +
            "        \"searchEngine\": \"google.co.th\",\n" +
            "        \"engine_id\": 35,\n" +
            "        \"country_name\": \"th\",\n" +
            "        \"language_name\": \"th\",\n" +
            "        \"language_id\": 35\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"th-en\",\n" +
            "        \"searchEngine\": \"google.co.th\",\n" +
            "        \"engine_id\": 35,\n" +
            "        \"country_name\": \"th\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 99\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"vn-vi\",\n" +
            "        \"searchEngine\": \"google.com.vn\",\n" +
            "        \"engine_id\": 36,\n" +
            "        \"country_name\": \"vn\",\n" +
            "        \"language_name\": \"vi\",\n" +
            "        \"language_id\": 36\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"vn-en\",\n" +
            "        \"searchEngine\": \"google.com.vn\",\n" +
            "        \"engine_id\": 36,\n" +
            "        \"country_name\": \"vn\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 128\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"sg-en\",\n" +
            "        \"searchEngine\": \"google.com.sg\",\n" +
            "        \"engine_id\": 37,\n" +
            "        \"country_name\": \"sg\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 37\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"nz-en\",\n" +
            "        \"searchEngine\": \"google.co.nz\",\n" +
            "        \"engine_id\": 38,\n" +
            "        \"country_name\": \"nz\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 38\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ru-ru\",\n" +
            "        \"searchEngine\": \"google.ru\",\n" +
            "        \"engine_id\": 39,\n" +
            "        \"country_name\": \"ru\",\n" +
            "        \"language_name\": \"ru\",\n" +
            "        \"language_id\": 39\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"cz-cs\",\n" +
            "        \"searchEngine\": \"google.cz\",\n" +
            "        \"engine_id\": 40,\n" +
            "        \"country_name\": \"cz\",\n" +
            "        \"language_name\": \"cs\",\n" +
            "        \"language_id\": 40\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"cz-en\",\n" +
            "        \"searchEngine\": \"google.cz\",\n" +
            "        \"engine_id\": 40,\n" +
            "        \"country_name\": \"cz\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 141\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"hu-hu\",\n" +
            "        \"searchEngine\": \"google.hu\",\n" +
            "        \"engine_id\": 41,\n" +
            "        \"country_name\": \"hu\",\n" +
            "        \"language_name\": \"hu\",\n" +
            "        \"language_id\": 41\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"pl-pl\",\n" +
            "        \"searchEngine\": \"google.pl\",\n" +
            "        \"engine_id\": 42,\n" +
            "        \"country_name\": \"pl\",\n" +
            "        \"language_name\": \"pl\",\n" +
            "        \"language_id\": 42\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"sa-ar\",\n" +
            "        \"searchEngine\": \"google.com.sa\",\n" +
            "        \"engine_id\": 43,\n" +
            "        \"country_name\": \"sa\",\n" +
            "        \"language_name\": \"ar\",\n" +
            "        \"language_id\": 43\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"pe-es\",\n" +
            "        \"searchEngine\": \"google.com.pe\",\n" +
            "        \"engine_id\": 44,\n" +
            "        \"country_name\": \"pe\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 44\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ea-ar\",\n" +
            "        \"searchEngine\": \"google.ae\",\n" +
            "        \"engine_id\": 45,\n" +
            "        \"country_name\": \"ea\",\n" +
            "        \"language_name\": \"ar\",\n" +
            "        \"language_id\": 45\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ae-en\",\n" +
            "        \"searchEngine\": \"google.ae\",\n" +
            "        \"engine_id\": 45,\n" +
            "        \"country_name\": \"ae\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 46\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"tr-tr\",\n" +
            "        \"searchEngine\": \"google.com.tr\",\n" +
            "        \"engine_id\": 46,\n" +
            "        \"country_name\": \"tr\",\n" +
            "        \"language_name\": \"tr\",\n" +
            "        \"language_id\": 47\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"za-en\",\n" +
            "        \"searchEngine\": \"google.co.za\",\n" +
            "        \"engine_id\": 47,\n" +
            "        \"country_name\": \"za\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 48\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ke-en\",\n" +
            "        \"searchEngine\": \"google.co.ke\",\n" +
            "        \"engine_id\": 48,\n" +
            "        \"country_name\": \"ke\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 49\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"sk-sk\",\n" +
            "        \"searchEngine\": \"google.sk\",\n" +
            "        \"engine_id\": 49,\n" +
            "        \"country_name\": \"sk\",\n" +
            "        \"language_name\": \"sk\",\n" +
            "        \"language_id\": 50\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"il-he\",\n" +
            "        \"searchEngine\": \"google.co.il\",\n" +
            "        \"engine_id\": 50,\n" +
            "        \"country_name\": \"il\",\n" +
            "        \"language_name\": \"he\",\n" +
            "        \"language_id\": 51\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"il-en\",\n" +
            "        \"searchEngine\": \"google.co.il\",\n" +
            "        \"engine_id\": 50,\n" +
            "        \"country_name\": \"il\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 143\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ec-es\",\n" +
            "        \"searchEngine\": \"google.com.ec\",\n" +
            "        \"engine_id\": 51,\n" +
            "        \"country_name\": \"ec\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 52\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ve-es\",\n" +
            "        \"searchEngine\": \"google.co.ve\",\n" +
            "        \"engine_id\": 52,\n" +
            "        \"country_name\": \"ve\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 53\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"cr-es\",\n" +
            "        \"searchEngine\": \"google.co.cr\",\n" +
            "        \"engine_id\": 53,\n" +
            "        \"country_name\": \"cr\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 118\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ua-ru\",\n" +
            "        \"searchEngine\": \"google.com.ua\",\n" +
            "        \"engine_id\": 54,\n" +
            "        \"country_name\": \"ua\",\n" +
            "        \"language_name\": \"ru\",\n" +
            "        \"language_id\": 104\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"uy-es\",\n" +
            "        \"searchEngine\": \"google.com.uy\",\n" +
            "        \"engine_id\": 55,\n" +
            "        \"country_name\": \"uy\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 149\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"mv-mv\",\n" +
            "        \"searchEngine\": \"google.mv\",\n" +
            "        \"engine_id\": 56,\n" +
            "        \"country_name\": \"mv\",\n" +
            "        \"language_name\": \"mv\",\n" +
            "        \"language_id\": 57\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"kh-en\",\n" +
            "        \"searchEngine\": \"google.com.kh\",\n" +
            "        \"engine_id\": 58,\n" +
            "        \"country_name\": \"kh\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 59\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"fj-en\",\n" +
            "        \"searchEngine\": \"google.com.fj\",\n" +
            "        \"engine_id\": 59,\n" +
            "        \"country_name\": \"fj\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 60\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ws-en\",\n" +
            "        \"searchEngine\": \"google.ws\",\n" +
            "        \"engine_id\": 60,\n" +
            "        \"country_name\": \"ws\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 61\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"gr-el\",\n" +
            "        \"searchEngine\": \"google.gr\",\n" +
            "        \"engine_id\": 61,\n" +
            "        \"country_name\": \"gr\",\n" +
            "        \"language_name\": \"el\",\n" +
            "        \"language_id\": 62\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ng-en\",\n" +
            "        \"searchEngine\": \"google.com.ng\",\n" +
            "        \"engine_id\": 62,\n" +
            "        \"country_name\": \"ng\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 63\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"do-es\",\n" +
            "        \"searchEngine\": \"google.com.do\",\n" +
            "        \"engine_id\": 63,\n" +
            "        \"country_name\": \"do\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 64\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"pk-en\",\n" +
            "        \"searchEngine\": \"google.com.pk\",\n" +
            "        \"engine_id\": 64,\n" +
            "        \"country_name\": \"pk\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 65\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"lb-ar\",\n" +
            "        \"searchEngine\": \"google.com.lb\",\n" +
            "        \"engine_id\": 65,\n" +
            "        \"country_name\": \"lb\",\n" +
            "        \"language_name\": \"ar\",\n" +
            "        \"language_id\": 66\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"qa-ar\",\n" +
            "        \"searchEngine\": \"google.com.qa\",\n" +
            "        \"engine_id\": 66,\n" +
            "        \"country_name\": \"qa\",\n" +
            "        \"language_name\": \"ar\",\n" +
            "        \"language_id\": 67\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"pa-es\",\n" +
            "        \"searchEngine\": \"google.com.pa\",\n" +
            "        \"engine_id\": 67,\n" +
            "        \"country_name\": \"pa\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 124\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"jo-ar\",\n" +
            "        \"searchEngine\": \"google.jo\",\n" +
            "        \"engine_id\": 68,\n" +
            "        \"country_name\": \"jo\",\n" +
            "        \"language_name\": \"ar\",\n" +
            "        \"language_id\": 69\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"hr-hr\",\n" +
            "        \"searchEngine\": \"google.hr\",\n" +
            "        \"engine_id\": 69,\n" +
            "        \"country_name\": \"hr\",\n" +
            "        \"language_name\": \"hr\",\n" +
            "        \"language_id\": 70\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"dz-ar\",\n" +
            "        \"searchEngine\": \"google.dz\",\n" +
            "        \"engine_id\": 70,\n" +
            "        \"country_name\": \"dz\",\n" +
            "        \"language_name\": \"ar\",\n" +
            "        \"language_id\": 71\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ro-ro\",\n" +
            "        \"searchEngine\": \"google.ro\",\n" +
            "        \"engine_id\": 71,\n" +
            "        \"country_name\": \"ro\",\n" +
            "        \"language_name\": \"ro\",\n" +
            "        \"language_id\": 72\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"al-sq\",\n" +
            "        \"searchEngine\": \"google.al\",\n" +
            "        \"engine_id\": 72,\n" +
            "        \"country_name\": \"al\",\n" +
            "        \"language_name\": \"sq\",\n" +
            "        \"language_id\": 73\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"az-az\",\n" +
            "        \"searchEngine\": \"google.az\",\n" +
            "        \"engine_id\": 73,\n" +
            "        \"country_name\": \"az\",\n" +
            "        \"language_name\": \"az\",\n" +
            "        \"language_id\": 74\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"bg-bg\",\n" +
            "        \"searchEngine\": \"google.bg\",\n" +
            "        \"engine_id\": 74,\n" +
            "        \"country_name\": \"bg\",\n" +
            "        \"language_name\": \"bg\",\n" +
            "        \"language_id\": 75\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"bh-ar\",\n" +
            "        \"searchEngine\": \"google.com.bh\",\n" +
            "        \"engine_id\": 75,\n" +
            "        \"country_name\": \"bh\",\n" +
            "        \"language_name\": \"ar\",\n" +
            "        \"language_id\": 76\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"bh-en\",\n" +
            "        \"searchEngine\": \"google.com.bh\",\n" +
            "        \"engine_id\": 75,\n" +
            "        \"country_name\": \"bh\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 107\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"cy-el\",\n" +
            "        \"searchEngine\": \"google.com.cy\",\n" +
            "        \"engine_id\": 76,\n" +
            "        \"country_name\": \"cy\",\n" +
            "        \"language_name\": \"el\",\n" +
            "        \"language_id\": 77\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"eg-ar\",\n" +
            "        \"searchEngine\": \"google.com.eg\",\n" +
            "        \"engine_id\": 78,\n" +
            "        \"country_name\": \"eg\",\n" +
            "        \"language_name\": \"ar\",\n" +
            "        \"language_id\": 79\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"et-am\",\n" +
            "        \"searchEngine\": \"google.com.et\",\n" +
            "        \"engine_id\": 79,\n" +
            "        \"country_name\": \"et\",\n" +
            "        \"language_name\": \"am\",\n" +
            "        \"language_id\": 80\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ga-fr\",\n" +
            "        \"searchEngine\": \"google.ga\",\n" +
            "        \"engine_id\": 80,\n" +
            "        \"country_name\": \"ga\",\n" +
            "        \"language_name\": \"fr\",\n" +
            "        \"language_id\": 81\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ge-ka\",\n" +
            "        \"searchEngine\": \"google.ge\",\n" +
            "        \"engine_id\": 81,\n" +
            "        \"country_name\": \"ge\",\n" +
            "        \"language_name\": \"ka\",\n" +
            "        \"language_id\": 82\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"kw-ar\",\n" +
            "        \"searchEngine\": \"google.com.kw\",\n" +
            "        \"engine_id\": 82,\n" +
            "        \"country_name\": \"kw\",\n" +
            "        \"language_name\": \"ar\",\n" +
            "        \"language_id\": 83\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"kw-en\",\n" +
            "        \"searchEngine\": \"google.com.kw\",\n" +
            "        \"engine_id\": 82,\n" +
            "        \"country_name\": \"kw\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 106\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ma-ar\",\n" +
            "        \"searchEngine\": \"google.co.ma\",\n" +
            "        \"engine_id\": 83,\n" +
            "        \"country_name\": \"ma\",\n" +
            "        \"language_name\": \"ar\",\n" +
            "        \"language_id\": 84\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"mc-fr\",\n" +
            "        \"searchEngine\": \"google.com\",\n" +
            "        \"engine_id\": 84,\n" +
            "        \"country_name\": \"mc\",\n" +
            "        \"language_id\": 85,\n" +
            "        \"language_name\": \"fr\"\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"mt-mt\",\n" +
            "        \"searchEngine\": \"google.com.mt\",\n" +
            "        \"engine_id\": 85,\n" +
            "        \"country_name\": \"mt\",\n" +
            "        \"language_name\": \"mt\",\n" +
            "        \"language_id\": 86\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"mu-en\",\n" +
            "        \"searchEngine\": \"google.mu\",\n" +
            "        \"engine_id\": 86,\n" +
            "        \"country_name\": \"mu\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 87\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"om-ar\",\n" +
            "        \"searchEngine\": \"google.com.om\",\n" +
            "        \"engine_id\": 87,\n" +
            "        \"country_name\": \"om\",\n" +
            "        \"language_name\": \"ar\",\n" +
            "        \"language_id\": 88\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"rs-sr\",\n" +
            "        \"searchEngine\": \"google.rs\",\n" +
            "        \"engine_id\": 88,\n" +
            "        \"country_name\": \"rs\",\n" +
            "        \"language_name\": \"sr\",\n" +
            "        \"language_id\": 89\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"tj-en\",\n" +
            "        \"searchEngine\": \"google.com.tj\",\n" +
            "        \"engine_id\": 90,\n" +
            "        \"country_name\": \"tj\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 131\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"tn-ar\",\n" +
            "        \"searchEngine\": \"google.tn\",\n" +
            "        \"engine_id\": 91,\n" +
            "        \"country_name\": \"tn\",\n" +
            "        \"language_name\": \"ar\",\n" +
            "        \"language_id\": 92\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"sv-es\",\n" +
            "        \"searchEngine\": \"google.com.sv\",\n" +
            "        \"engine_id\": 93,\n" +
            "        \"country_name\": \"sv\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 119\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"lu-lb\",\n" +
            "        \"searchEngine\": \"google.lu\",\n" +
            "        \"engine_id\": 94,\n" +
            "        \"country_name\": \"lu\",\n" +
            "        \"language_name\": \"lb\",\n" +
            "        \"language_id\": 100\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"lt-lt\",\n" +
            "        \"searchEngine\": \"google.lt\",\n" +
            "        \"engine_id\": 95,\n" +
            "        \"country_name\": \"lt\",\n" +
            "        \"language_name\": \"lt\",\n" +
            "        \"language_id\": 105\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"my-mm\",\n" +
            "        \"searchEngine\": \"google.com.mm\",\n" +
            "        \"engine_id\": 96,\n" +
            "        \"country_name\": \"my\",\n" +
            "        \"language_name\": \"mm\",\n" +
            "        \"language_id\": 108\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ee-et\",\n" +
            "        \"searchEngine\": \"google.ee\",\n" +
            "        \"engine_id\": 97,\n" +
            "        \"country_name\": \"ee\",\n" +
            "        \"language_name\": \"et\",\n" +
            "        \"language_id\": 112\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ee-ru\",\n" +
            "        \"searchEngine\": \"google.ee\",\n" +
            "        \"engine_id\": 97,\n" +
            "        \"country_name\": \"ee\",\n" +
            "        \"language_name\": \"ru\",\n" +
            "        \"language_id\": 116\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"lv-lv\",\n" +
            "        \"searchEngine\": \"google.lv\",\n" +
            "        \"engine_id\": 98,\n" +
            "        \"country_name\": \"lv\",\n" +
            "        \"language_name\": \"lv\",\n" +
            "        \"language_id\": 113\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"cn-zh\",\n" +
            "        \"searchEngine\": \"baidu.com\",\n" +
            "        \"engine_id\": 150,\n" +
            "        \"country_name\": \"cn\",\n" +
            "        \"language_name\": \"zh\",\n" +
            "        \"language_id\": 6\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"uz-uz\",\n" +
            "        \"searchEngine\": \"google.co.uz\",\n" +
            "        \"engine_id\": 201,\n" +
            "        \"country_name\": \"uz\",\n" +
            "        \"language_name\": \"uz\",\n" +
            "        \"language_id\": 115\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"jm-en\",\n" +
            "        \"searchEngine\": \"google.com.jm\",\n" +
            "        \"engine_id\": 202,\n" +
            "        \"country_name\": \"jm\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 120\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"kg-ru\",\n" +
            "        \"searchEngine\": \"google.com.kg\",\n" +
            "        \"engine_id\": 203,\n" +
            "        \"country_name\": \"kg\",\n" +
            "        \"language_name\": \"ru\",\n" +
            "        \"language_id\": 121\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ni-es\",\n" +
            "        \"searchEngine\": \"google.com.ni\",\n" +
            "        \"engine_id\": 205,\n" +
            "        \"country_name\": \"ni\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 123\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"hn-es\",\n" +
            "        \"searchEngine\": \"google.hn\",\n" +
            "        \"engine_id\": 207,\n" +
            "        \"country_name\": \"hn\",\n" +
            "        \"language_name\": \"es\",\n" +
            "        \"language_id\": 126\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"am-en\",\n" +
            "        \"searchEngine\": \"google.am\",\n" +
            "        \"engine_id\": 208,\n" +
            "        \"country_name\": \"am\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 129\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"gt-en\",\n" +
            "        \"searchEngine\": \"google.com.gt\",\n" +
            "        \"engine_id\": 209,\n" +
            "        \"country_name\": \"gt\",\n" +
            "        \"language_name\": \"en\",\n" +
            "        \"language_id\": 132\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"mk-mk\",\n" +
            "        \"searchEngine\": \"google.mk\",\n" +
            "        \"engine_id\": 224,\n" +
            "        \"country_name\": \"mk\",\n" +
            "        \"language_name\": \"mk\",\n" +
            "        \"language_id\": 138\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"sl-sl\",\n" +
            "        \"searchEngine\": \"google.si\",\n" +
            "        \"engine_id\": 225,\n" +
            "        \"country_name\": \"sl\",\n" +
            "        \"language_name\": \"sl\",\n" +
            "        \"language_id\": 139\n" +
            "    },\n" +
            "    {\n" +
            "        \"market\": \"ba-bs\",\n" +
            "        \"searchEngine\": \"google.ba\",\n" +
            "        \"engine_id\": 233,\n" +
            "        \"country_name\": \"ba\",\n" +
            "        \"language_name\": \"bs\",\n" +
            "        \"language_id\": 162\n" +
            "    }\n" +
            "]";


    public static String USER = "default";
    public static String PSW = "clarity99!";
    public static String FILE_FORMATE_CSV = "CSV";// "format": "CSV",    -- optional            [CSV,JSON]
    public static String FILE_FORMATE_JSON = "JSON";
    public static String FAILED_TO_STORAGE_MESSAGE = "Failed to connect to specified server, upload to default server";
    // https://www.wrike.com/open.htm?id=908593946
    private final static String TASK_NAME_CONTENTGAP_ESTDTRAFFIC = "ContentGap_EstdTraffic";
    // https://www.wrike.com/open.htm?id=913173890
    private final static String TASK_NAME_CONTENTGAP_COMPARERANK = "ContentGap_CompareRank";
    private final static int INTERVAL = 50000;

    // https://www.wrike.com/open.htm?id=919475248
    private final static String TASK_NAME_RESEARCHGRID_KEYWORDDETAIL = "ResearchGrid_KeywordDetail";
    // https://www.wrike.com/open.htm?id=922405732
    private final static String TASKNAME_RESEARCHGRID_TOPPAGES = "ResearchGrid_TopPages";
    // https://www.wrike.com/open.htm?id=922405358
    private final static String TASKNAME_RESEARCHGRID_RANKSUMMARY = "ResearchGrid_RankSummary";
    // https://www.wrike.com/open.htm?id=922406027
    private final static String TASKNAME_RESEARCHGRID_PAGESTREND = "ResearchGrid_PageTrend";
    private List<ApiTaskInfoEntity> apiTaskInfoEntitys;
    // https://www.wrike.com/open.htm?id=908593946
    public final static String TASKNAME_CONTENTFUSION_KEYWORD = "Contentfusion_Keyword";
    public final static String TASKNAME_CONTENTFUSION_KEYWORD_REALTIME = "Contentfusion_Keyword_Realtime";
    private static String LOG_DIR = "/tmp/contentGap/log/";
    private List<String> taskGroups = new ArrayList<>();
    private static String engine_id = "1";
    private static String language_id = "1";

    private static long page = 0;
    private static int psize = 200;




    public AsynGetCheckidV3() {

        File path = new File(LOG_DIR);
        if (!path.exists()) path.mkdirs();
        path = new File(LOCAL_STORE_DIR);
        if (!path.exists()) path.mkdirs();
    }

    private static File notExistFile;
    private static File notExistFileSource;
    private static File destRankFile;
    /**
     * Add a package name prefix if the name is not absolute Remove leading "/"
     * if name is absolute
     */


    private static int currentCount =0;
    private static int checkCount =0;
    public static void doTaskRank30(long startid,String ym,String dburl,int rank) throws  Exception{
        long start1 = System.currentTimeMillis() ;
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String category = formatter.format(new Date())+"/"+strNum;
        String strdestRankFile = fulloutpath+category+"_1"+"/"+"rank"+rank+".txt";
        System.out.println("=====strdestRankFile:"+strdestRankFile);
        //destRankFile = createFile(fulloutpath+category+"_1"+"/"+"rank"+rank+".txt");
        //File destNotExistFile =createFile(fulloutpath+category+"_1"+"/"+"notExistRank"+rank+".txt");
        //outPutfileHeader(destRankFile);


        int j = 0;
        long endcheck =0;

        System.out.println("=====endcheck:"+endcheck);

        long lastId =0;

        lastId = startid;
        if(lastId == 0){
            page = 0;
        }else{
            page = 1;
        }
        //psize = 5;
        //48000000
        while(true){
            String limitsql = " limit  " +  psize;
            System.out.println("=====limit sql:"+ limitsql);
            List<SeoClarityKeywordMonthlySearchEngineRelationEntity>  listid =seoClarityKeywordMonthlySearchEngineRelationEntityDAO.getLimit(limitsql,lastId);
            if(listid.size()==0){
                break;
            }

            lastId = listid.get(listid.size()-1).getKeywordId();
            System.out.println("=====lastId = " + lastId);
            String sql = getRank30SqlV2(listid,ym,rank);

            System.out.println("=====sql:"+sql);
            ownCustomExport(sql,strdestRankFile,notExistFile,dburl);

            if(lastId >= 40000000l&&startid<40000000l){
                break;
            }

            System.out.println("=====lastpage : "+page);
            page++;
//                long endd = System.currentTimeMillis() ;
//                System.out.print("=====cur count :" + i*size);
//                currentCount = i*size;
//                System.out.println(":###httpExportClarityDBToFile cost time :" + (endd - start1)/1000 );
//                //System.out.println("=====out put file=====");
//                listmap.clear();
//                try{
//                    Thread.sleep(1000);
//                }catch (Exception ex){
//                }
            }


//        List<String> notExistList = RankUtils.loadFileListV3(notExistFile,0);
//        outPutfile(notExistFile,notExistList,true);
        //RankUtils.copy(notExistFile,destNotExistFile);
        long end1 = System.currentTimeMillis() ;
        System.out.println("=====total took:"+(end1 - start1)/1000/3600 +" hours");

    }


    private static List<String> notExistUrl(List<Map> listsrc,List<Map> data) throws Exception{
//        System.out.println("=====notExistUrl.listsrc:"+listsrc.toString());
//        System.out.println("=====notExistUrl.data:"+data.toString());
        //List<String> listSrcUrl = new ArrayList<>();
        List<String> listEncodeSrcUrl = new ArrayList<>();
        List<String> listDataUrl = new ArrayList<>();
        for (Map row:listsrc) {
            String temp = row.get("keyword").toString();
            if(!listEncodeSrcUrl.contains(temp)){
                listEncodeSrcUrl.add(temp);//encode
            }
        }

        for (int i = 0; i < data.size(); i++) {
            String temp = CommonDataService.encodeQueueBaseKeyword(data.get(i).get("keyword_name").toString());
            if(!listDataUrl.contains(temp)){
                listDataUrl.add(temp);
            }
        }

        List<String> listRet = new ArrayList<>();
        listEncodeSrcUrl.removeAll(listDataUrl);
        for (String temp:listEncodeSrcUrl ) {
            temp = URLDecoder.decode(temp,"UTF-8");
            listRet.add(temp);
        }
        return listRet;
    }

    private static boolean ownCustomExport(String sql,String fullfilename,File notExistFIle,String dburl) throws Exception {

            boolean isSucc = false;
            String[] err = new String[]{""};

        //System.out.println("err:" +err.length);
            //List listout =  httpExportClarityDBToFileV3(dburl, USER, PSW, sql ,err);

        System.out.println("===== start page:"+page);
            if(page==0){
                ClarityDBUtils.httpExportFromClarityDB_top10rank(mainurl,"monthly_ranking", USER, PSW, sql ,fullfilename,true,false,err);
            }else{
                ClarityDBUtils.httpExportFromClarityDB_top10rank(mainurl,"monthly_ranking", USER, PSW, sql ,fullfilename,false,false,err);
            }

            if(err.length==1){
                return true;
            }else{
                return false;
            }
            //System.out.println("=====listout:"+listout.toString());
            //List listdata = makeCompititorv2(listout);
            //isSucc= outPutfile(fullfilename,toListStr(listout),true);
//            List listNotExist = notExistUrl(listSrc,listdata);
//            outPutfile(notExistFIle,listNotExist,true);
            //long end = System.currentTimeMillis() / 1000;
            //System.out.println("###httpExportClarityDBToFile cost time:" + ((end - start) / 60) + "m" + ((end - start) % 60) + "s");

    }
    
//                sb.append("keyword_name").append(SPLIT)
//                    .append("Ranking_URL").append(SPLIT)
//                    .append("Ranking_position").append(SPLIT)
//                    .append("SV").append(SPLIT)
//                    .append("svCpcHash").append(SPLIT)
//                    .append("keywordIntent").append(SPLIT)
//                    .append("title").append(SPLIT)
//                    .append("meta").append(SPLIT)
////                    .append("img").append(SPLIT)
////                    .append("locallisting").append(SPLIT)
////                    .append("peoplealsoask").append(SPLIT)
////                    .append("answerbox").append(SPLIT)
////                    .append("news").append(SPLIT)
////                    .append("video").append(SPLIT)
////                    .append("event").append(SPLIT)
////                    .append("products").append(SPLIT)
////                    .append("shopping").append(SPLIT)
////                    .append("ppc").append(SPLIT)
//                    .append("knowledgeGraphFlg").append(SPLIT);
    private static final String SPLIT = "\t";
    private  static List<String> toListStr(List<HashMap> outputList) {
        List<String> list = new ArrayList<>();
        for(HashMap map : outputList){
            String keyword_name =  map.get("keyword_name").toString().trim();
            String SV =   map.get("SV").toString().trim();
            String svCpcHash =  map.get("SVCPCHash").toString().trim();
            String knowledgeGraphFlg =  map.get("knowledgeGraphFlg").toString().trim();
            String keywordIntent =  map.get("keywordIntent").toString().trim();
            String Ranking_URL =  map.get("Ranking_URL").toString().trim();
            String Ranking_position =  map.get("Ranking_position").toString().trim();
            String label =  map.get("label").toString().trim();
            String meta =  map.get("meta").toString().trim();
            //String true_demand = "";
//            if(map.get("true_demand")!=null){
//                true_demand =  map.get("true_demand").toString().trim();
//            }
//            String img = map.get("img").toString().trim();
//            String locallisting = map.get("locallisting").toString().trim();
//            String peoplealsoask = map.get("peoplealsoask").toString().trim();
//            String answerbox = map.get("answerbox").toString().trim();
//            String news = map.get("news").toString().trim();
//            String video = map.get("video").toString().trim();
//            String event = map.get("event").toString().trim();
//            String products = map.get("products").toString().trim();
//            String shopping = map.get("shopping").toString().trim();
//            String ppc = map.get("ppc").toString().trim();



            StringBuffer sb = new StringBuffer();
            sb.append(keyword_name ).append(SPLIT)
                    .append(Ranking_URL).append(SPLIT)
                    .append(Ranking_position).append(SPLIT)
                    .append(SV).append(SPLIT)
                    .append(svCpcHash).append(SPLIT)
                    .append(keywordIntent).append(SPLIT)
                    .append(label).append(SPLIT)
                    .append(meta).append(SPLIT)
//                    .append(img).append(SPLIT)
//                    .append(locallisting).append(SPLIT)
//                    .append(peoplealsoask).append(SPLIT)
//                    .append(answerbox).append(SPLIT)
//                    .append(news).append(SPLIT)
//                    .append(video).append(SPLIT)
//                    .append(event).append(SPLIT)
//                    .append(products).append(SPLIT)
//                    .append(shopping).append(SPLIT)
//                    .append(ppc).append(SPLIT)

                    .append(knowledgeGraphFlg);
                    //.append(true_demand);
            list.add(sb.toString());
        }
        return list;
    }

    private static final String allowedColumns = "keyword_name,url,web_rank,avg_search_volume,SVCPCHash,keywordIntent,label,meta,knowledgeGraphFlg,true_demand,img,locallisting,peoplealsoask,answerbox,news,video,event,products,shopping,ppc";
    private static boolean checkColumns(String strColumns){
        List<String> columns = Arrays.asList(allowedColumns.split(","));
        List<String> columnsInput = Arrays.asList(strColumns.split(","));
        columnsInput.removeAll(columns);
        if(columnsInput.size() > 0){
            return false;
        }else{
            return true;
        }
    }
    // https://www.wrike.com/open.htm?id=1157826235
    // https://www.wrike.com/open.htm?id=1157826235
    //Keyword
    //Ranking URL
    //"Rank position"
    //"Search volume"
    //"Search volume + CPC hash"
    //"SERP title"
    //"SERP meta"
    //"True Demand"
    private static String getRank30SqlV2(List<SeoClarityKeywordMonthlySearchEngineRelationEntity> listmap,String ym,int rank) throws  Exception{
        String sqlTemp = "select keyword_name as Keyword, url as \"Ranking URL\", web_rank as \"Rank position\", sv as  \"Search volume\", key as \"Search volume + CPC hash\", label as \"SERP title\", meta as \"SERP meta\", true_demand as \"True Demand\" from (\n" +
                "select keyword_rankcheck_id, keyword_name, web_rank, url, label, meta from monthly_ranking.d_ranking_detail_202312_us \n" +
                " where own_domain_id=0 and engine_id=1 and language_id=1 and location_id=0 and web_rank <= 10 and keyword_rankcheck_id in({idlist}) ) t1\n" +
                "ANY left join (select keyword_rankcheck_id, avg_search_volume as sv, key, true_demand from keyword_searchvolume.dis_searchvolume_v2 \n" +
                " where engine_id=1 and language_id=1 and location_id=0 and keyword_rankcheck_id in({idlist})) t2\n" +
                " using(keyword_rankcheck_id) order by keyword_rankcheck_id, web_rank settings distributed_product_mode='local' , max_bytes_before_external_group_by=15000000000,max_bytes_before_external_sort=15000000000  format TabSeparatedWithNames ;";
        sqlTemp = sqlTemp.replace("{{ym}}",ym);
        //System.out.println("=====listmap:"+listmap.toString());
        List<String> listkeyid = listmap.stream().map(item ->item.getKeywordId()).map(String::valueOf).collect(Collectors.toList());

        String condition = String.join(",",listkeyid);
        String sqltxt = sqlTemp.replace("{idlist}",condition);
        System.out.println("sqltxt:"+sqltxt);
        return sqltxt;
    }

    private static String getLastClarityDbDay() throws Exception{
        Date currentDate = new Date();
        // 创建Calendar对象
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        // 将日期减少一个月
        calendar.add(Calendar.MONTH, -1);
        // 获取减少一个月后的日期
        Date lastMonthDate = calendar.getTime();
        SimpleDateFormat sp = new SimpleDateFormat("yyyyMM");
        return sp.format(lastMonthDate);
    }


    public static String getAlphaNumericString(int n) {
        // chose a Character random from this String
        String alphaNumericString = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvxyz";
        // create StringBuffer size of AlphaNumericString
        StringBuilder sb = new StringBuilder(n);
        for (int i = 0; i < n; i++) {
            // generate a random number between
            // 0 to AlphaNumericString variable length
            int index
                    = (int) (alphaNumericString.length()
                    * Math.random());
            // add Character one by one in end of sb
            sb.append(alphaNumericString
                    .charAt(index));
        }
        return sb.toString();
    }

    private static final String  fulloutpath = "/home/<USER>/outfiles/auto15/";
    private static final String dburl2 ="http://23.105.14.193:8123";
    private static final String dburl1 ="http://99.198.122.254:8123";

    private static  String mainurl ="";
    private static final boolean IS_DEBUG = true;
    private static long endIndex;
    private static boolean isFinished;
    private static double days;
    private static int hours =0;
    private static int tmpIndex = 0;
    private static int today =1;
    private static String strNum = "";
    private static List<String> inputColumns;
    private static String intl;

    public static void main(String args[]) {
        try{
            String inputFile = args[0];
            Integer rank = Integer.parseInt( args[1]);
            String email =  args[2];
            String market = "us-en";
            intl = "_us";
            market = args[3];
            if(!StringUtils.equals(market,"us-en")){
                intl ="_intl";
            }
            String dbflag = "";
            dbflag = args[4];
            System.out.println("=====dbflag:" + dbflag);
            int dbstart = 0;
            //选择数据库
            if(StringUtils.equals(dbflag,"0")){
                mainurl = dburl2;
                dbstart = 0;
            }else{
                mainurl = dburl1;
                dbstart = 1;
            }
            System.out.println("=====dbstart:" +dbstart);

            String start1 = "";
            if(args.length ==6){
                start1 = args[5];
            }
            //> checkid
            long startid = Long.parseLong(start1) ;

            System.out.println("=====startid:" +startid);
            //page = startid;
            System.out.println("=====mainurl:" +mainurl);
            isFinished = false;
            String debugMode = "";
            if(IS_DEBUG){
                debugMode = "IS IN DEBUG MODE!<br>";
            }else{
                debugMode = "IS IN RELAEASE MODE!<br>";
            }
            seoClarityKeywordMonthlySearchEngineRelationEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordMonthlySearchEngineRelationEntityDAO");

            if(!initData(market)){
                return;
            }else{
                System.out.println("=====languageid:"+language_id+",enginid:"+engine_id);
            }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        System.out.println("=====current time:"+formatter.format(new Date())+"=====");
        //sendMailReport(debugMode+ "   The extract process is launched ,<br>Total input keywords count is " + list.size() +" ,<br>Now is checking  keywords exist in db.",rank);
        strNum = getAlphaNumericString(10);
        System.out.println("=====strNum:"+strNum);

                System.out.println("=====exctrating begin");

                List<String> listChecked = new ArrayList<>();

                doTaskRank30(startid,"202312",mainurl,rank);
                //doTaskRank30(48000000,0,"202312",mainurl,rank);

                long end = System.currentTimeMillis() /1000;

                //sendMailReport("    exctrating is completed !<br> server:script 20,path: "+ destRankFile.getParent(),rank);
                System.out.println("=====finished!!!!!!!!!!!");
                isFinished= true;
//        }else{
//            sendMailReport("======= Extracted failed!!!!",rank);
//        }

        }catch (Exception ex){
            ex.printStackTrace();
            System.out.println("err");
        }
    }

    private static boolean initData(String market) throws Exception{
        boolean output =false;
        JSONArray array = JSONUtil.parseArray(listMarket);
        for (int i = 0; i < array.size(); i++) {
            JSON jobj= JSONUtil.parse( array.get(i)) ;
            if(StringUtils.endsWithIgnoreCase(jobj.getByPath("market").toString(),market)){
                language_id = jobj.getByPath("language_id").toString();
                engine_id = jobj.getByPath("engine_id").toString();
                output = true;
                break;
            }
        }
        return output;
    }

}

