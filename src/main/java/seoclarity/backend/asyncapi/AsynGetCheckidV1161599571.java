package seoclarity.backend.asyncapi;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.commons.lang3.StringUtils;
import org.testcontainers.shaded.okhttp3.*;
import scala.util.parsing.combinator.testing.Str;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntityDAO;
import seoclarity.backend.entity.actonia.ApiTaskInfoEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntity;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static seoclarity.backend.utils.FileUtils.createFile;
import static seoclarity.backend.utils.FileUtils.outPutfile;

//mvn exec:java -Dexec.mainClass="seoclarity.backend.asyncapi.AsynTaskProcessor" -Dexec.args="102"
public class AsynGetCheckidV1161599571 {
    private static final int MAX_LIST_SIZE = 1000;
    private static final int GET_TIME_OUT = 10 * 1000;
    private static final String END_POINT = "https://event.seoclarity.workers.dev";
    private static final String LIST_BY_KEY_WITH_CURSOR = "/cursor/{cursor}?{prefix}";
    private static final String GET_BY_KEY = "/key/{task_id}";
    private static final String LIST_BY_KEY = "/list/{prefix}";
    private static final String zeroRanksql = "as csvurl, '-' as url0,'-' rank0  format JSONEachRow";
    //***************************************************
    public static final String BASEPREFIX = "asyncdownload_contentgap_estdTrafficKWDetailNew_ownCustom_";
    private static final String LOCAL_STORE_DIR = "/tmp/testMarven/";
    //***************************************************
    private static SeoClarityKeywordMonthlySearchEngineRelationEntityDAO seoClarityKeywordMonthlySearchEngineRelationEntityDAO;
    private static final String DELETE_BY_KEY = "/delete/{task_id}";
    public static final int DOWNLOADMAXCOUNT = 10;
    private static  List<String> listCache = null;
    private static final String HEADER_TOKEN_KEY = "seoclarity-internal-token";
    private static final String HEADER_TOKEN_VALUE = "6603b708dd0bf24b0e7a1e68408c454e";
    private static Integer READ_TIMEOUT = 1800;
    public static int HTTP_EXPORT_RESPONSE_SCUCCESS=200;
    public static int HTTP_EXPORT_RESPONSE_ERROR=400;
    public static int HTTP_EXPORT_CLARITYDB_TO_FILE_FAILD=-2;
    public static int HTTP_EXPORT_CLARITYDB_TO_FILE_EXCEPTION=-1;
    private static final Map<String, String> CACHE_HEADER_MAP = new HashMap<>();
    public static String USER = "default";
    public static String PSW = "clarity99!";
    public static String FAILED_TO_STORAGE_MESSAGE = "Failed to connect to specified server, upload to default server";
    private final static int INTERVAL = 50000;

    // https://www.wrike.com/open.htm?id=908593946
    public final static String TASKNAME_CONTENTFUSION_KEYWORD = "Contentfusion_Keyword";
    public final static String TASKNAME_CONTENTFUSION_KEYWORD_REALTIME = "Contentfusion_Keyword_Realtime";
    private static String TARGETPATH = "/home/<USER>/public_html/tasks/";
    private static DateFormat SDF = new SimpleDateFormat("yyyy-MM-dd");

//    private static String URL = "http://downloads.seoclarity.net/tasks";
    private static String URL_SEPARATOR = "/";
    private static String NOTASK = "Task is not ready";
    private  static int GETTASKINFOFLAG = 0;
    private static String LOG_DIR = "/tmp/contentGap/log/";
    private static String FAILED_SQL_LOG_NAME = "failed_sql.log";
    private static String SEND_TO = "<EMAIL>";
    private static String[] CC_TOS = new String[]{"<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"};
    //static private ApiTaskInstanceEntityDAO apiTaskInstanceEntityDAO;
    private List<String> taskGroups = new ArrayList<>();

    public AsynGetCheckidV1161599571() {
        File path = new File(LOG_DIR);
        if (!path.exists()) path.mkdirs();
        path = new File(LOCAL_STORE_DIR);
        if (!path.exists()) path.mkdirs();
    }


    public static String sqlStringEscape(String str) {
        String data = null;
        if (str != null && str.length() > RankUtils.EMPTY_COL_SIZE) {
            str = str.replace(RankUtils.ESCAPED_FW_SLASH, RankUtils.SQL_ESCAPED_FW_SLASH);
            str = str.replace(RankUtils.SINGLE_QUOTE, RankUtils.SQL_ESCAPED_SINGLE_QUOTE);
            str = str.replace(RankUtils.NULL_CHARACTER, RankUtils.SQL_ESCAPED_NULL_CHARACTER);
            str = str.replace(RankUtils.LINE_FEED_CHARACTER, RankUtils.SQL_ESCAPED_LINE_FEED);
            str = str.replace(RankUtils.CARRIAGE_RETURN_CHARACTER, RankUtils.SQL_ESCAPED_CARRIAGE_RETURN);
            str = str.replace(RankUtils.ESCAPED_DOUBLE_QUOTES, RankUtils.SQL_ESCAPED_DOUBLE_QUOTES);
            str = str.replace(RankUtils.SUBSTITUTE_CHARACTER, RankUtils.SQL_ESCAPED_SUBSTITUTE_CHARACTER);
            data = str;
        }
        return data;
    }

    // https://www.wrike.com/open.htm?id=1161599571
    private static String getRank30SqlV3(List<String> listmap,String ym,int rank) throws  Exception{
        String sqlTemp = "SELECT  keyword_name,true_demand FROM keyword_searchvolume.dis_searchvolume_v2 WHERE (engine_id = 1) AND (language_id = 1)\n" +
                " AND (location_id = 0) AND (keyword_name IN ({idlist}))  SETTINGS max_bytes_before_external_group_by=15000000000,max_bytes_before_external_sort=15000000000  format JSONEachRow;";
        //sqlTemp = sqlTemp.replace("{{ym}}",ym);
        //System.out.println("=====listmap:"+listmap.toString());
        //List<String> listkeyid = listmap.stream().map(Map -> Map.get("keyid").toString()).collect(Collectors.toList());
        List<String> listTmp = new ArrayList<>();
        for (int i = 0; i <listmap.size() ; i++) {
            listTmp.add("'"+sqlStringEscape(listmap.get(i))+"'") ;
        }
        String condition = String.join(",",listTmp);
        String sqltxt = sqlTemp.replace("{idlist}",condition);
        System.out.println("=====sqltxt:"+sqltxt);
        return sqltxt;
    }


    private  static void outPutfileHeader(File fullFileName) {
        try {
            StringBuilder sb = new StringBuilder();
            sb.append("keyword_name").append(SPLIT)
//                    .append("Ranking_URL").append(SPLIT)
//                    .append("Ranking_position").append(SPLIT)
//                    .append("SV").append(SPLIT)
//                    .append("svCpcHash").append(SPLIT)
//                    .append("keywordIntent").append(SPLIT)
//                    .append("title").append(SPLIT)
//                    .append("meta").append(SPLIT)
//                    .append("knowledgeGraphFlg").append(SPLIT)
                    .append("True Demand").append(SPLIT);
            System.out.println("header:"+sb.toString());
            List<String> list = new ArrayList<>();
            list.add(sb.toString());
            org.apache.commons.io.FileUtils.writeLines(fullFileName, "UTF-8", list, false);
        }catch (Exception ex){
            ex.printStackTrace();
        }
    }

    // https://www.wrike.com/open.htm?id=1161599571
    public static void doTaskRank1161599571(int size,int begin ,int end,String ym,List<String> listword,String dburl,int rank) throws  Exception{
        long start1 = System.currentTimeMillis() ;
        if(end == 0){
            end = listword.size()-1;
        }
        String category = begin+"_"+end;
        File file = createFile(fulloutfilename+category+"/"+"rank10.txt");
        File notExistFile =createFile(fulloutfilename+category+"/"+"notExistRank10.txt");
        outPutfileHeader(file);
        List<String> list = new ArrayList<>();
        list =listword.subList(begin,end);
        System.out.println("size:"+list.size());
        int i = 0;
        List<String> listStr = new ArrayList<>();
        int j = 0;
        for (String line:list) {
            listStr.add(line);
            if(listStr.size() == size ){
                System.out.println("=====listStrkkkkkk:" +listStr);
                i++;
                String sql = getRank30SqlV3(listStr,ym,rank);
                System.out.println("=====sql:"+sql);
                boolean ret = ownCustomExport(listStr,sql,file,notExistFile,dburl);
                long endd = System.currentTimeMillis() ;
                System.out.print("=====cur count :" + i*size);
                System.out.println(":###httpExportClarityDBToFile cost time :" + (endd - start1)/1000 );
                //System.out.println("=====out put file=====");
                listStr.clear();
                try{
                    Thread.sleep(100);
                }catch (Exception ex){
                }
            }
        }

        if(listStr.size() > 0) {
            String sql = getRank30SqlV3(listStr, ym,rank);
            ownCustomExport(listStr, sql, file, notExistFile, dburl);
        }

        long end1 = System.currentTimeMillis() ;
        System.out.println("=====total took:"+(end1 - start1)/1000);
        System.out.println("!!!!!!!!!!!!!!!!!"+category+":=================taskfinished");
    }

    private static List<String> notExistUrl(List<String> listsrc,List<String> listChecked){
 //       List<String> list = new ArrayList<>();
//        for (String temp:data) {
//            String[] arr = temp.split("\t!!!\t");
//            list.add(arr[1]);
//        }
        System.out.println("=====listsrc:"+ listsrc.toString());
        System.out.println("=====listChecked:"+ listChecked.toString());
        listsrc.removeAll(listChecked);
        if(listsrc.size() > 0) {
            System.out.println("=====listsrc after removed:" + listsrc.toString());
        }
        return listsrc;
    }

    private static boolean ownCustomExport(List listSrc,String sql,File fullfilename,File notExistFIle,String dburl) {
        try{
            System.out.println("=====88888888:"+listSrc.toString());
            boolean isSucc = false;
            String[] err = new String[]{""};
            List listout =  httpExportClarityDBToFileV3(dburl, USER, PSW, sql ,err);

            System.out.println("=====listout:"+listout.toString());

            List<Map> listdata = makeCompititorv2(listout);
            List<String> listTmp = new ArrayList<>();

            List<String> listCmp = new ArrayList<>();
            for (Map temp:listdata) {
                //temp = temp.replace("\t!!!\t","\t");
                listTmp.add(temp.get("keyword_name").toString()+"\t"+temp.get("true_demand"));//xxxxx
                listCmp.add(temp.get("keyword_name").toString());

            }
            System.out.println("=====listTmp:" +listTmp.toString());
            isSucc= outPutfile(fullfilename,listTmp,true);
            listTmp.clear();
            List listNotExist = notExistUrl(listSrc,listCmp);
            if(listNotExist.size() > 0){
                outPutfile(notExistFIle,listNotExist,true);
            }

            //long end = System.currentTimeMillis() / 1000;
            //System.out.println("###httpExportClarityDBToFile cost time:" + ((end - start) / 60) + "m" + ((end - start) % 60) + "s");
            return  isSucc;
        } catch (Exception ex) {
            //System.out.println("*******Failed keyword_name is "+keyword_name+"====");
            ex.printStackTrace();
            return  false;
        }
    }
    private static final String SPLIT = "\t";
    private  static List<String> toListStr(List<HashMap> outputList) {
        List<String> list = new ArrayList<>();
        for(HashMap map : outputList){
            String keyword_name =  map.get("keyword_name").toString().trim();
//            String SV =  map.get("SV").toString().trim();
//            String svCpcHash =  map.get("SVCPCHash").toString().trim();
//            String knowledgeGraphFlg =  map.get("knowledgeGraphFlg").toString().trim();
//            String keywordIntent =  map.get("keywordIntent").toString().trim();
//            String Ranking_URL =  map.get("Ranking_URL").toString().trim();
//            String Ranking_position =  map.get("Ranking_position").toString().trim();
//            String label =  map.get("label").toString().trim();
//            String meta =  map.get("meta").toString().trim();
            String true_demand = "";

            if(map.get("true_demand")!=null){
                true_demand =  map.get("true_demand").toString().trim();
            }

            StringBuffer sb = new StringBuffer();
            sb.append(keyword_name ).append(SPLIT)
//                    .append(Ranking_URL).append(SPLIT)
//                    .append(Ranking_position).append(SPLIT)
//                    .append(SV).append(SPLIT)
//                    .append(svCpcHash).append(SPLIT)
//                    .append(keywordIntent).append(SPLIT)
//                    .append(label).append(SPLIT)
//                    .append(meta).append(SPLIT)
//                    .append(knowledgeGraphFlg).append(SPLIT)
                    .append(true_demand);
            list.add(sb.toString());
        }
        return list;
    }

    private static List<Map> makeCompititorv2(List<String> list){
        try{
            List<Map> listret = new ArrayList<>();
            int i = 0;
            for (String row:list) {
                if(StringUtils.isNotBlank(row)) {
                    //String[] arr = row.split("\t");
                    Map map = new HashMap();
                    JSONObject json = JSONUtil.parseObj(row);
                    map.put("keyword_name",json.getByPath("keyword_name"));
                    map.put("true_demand",json.getByPath("true_demand"));
                    //String temp = json.getByPath("keyword_name") +"\t!!!\t" + json.getByPath("true_demand");
                    listret.add(map);
                }
            }
            return listret;
        }catch (Exception ex){
            //System.out.println("=====error:"+jsonstr+"");
            return null ;
        }
    }


    public static List<String> httpExportClarityDBToFileV3(String dburl, String user, String password, String sql,  String[]err) throws Exception {
        List<String> outputList = new ArrayList<>();
        String url0 = dburl + "/?database=monthly_ranking&enable_http_compression=1&user=" + user + "&password=" + password + "&query=" + sql;
        //System.out.println("=========URL0:" + url0);
        String url=dburl + "/?database=monthly_ranking&enable_http_compression=1&user=" + URLEncoder.encode(user,"UTF-8")  + "&password=" +URLEncoder.encode(password,"UTF-8")
                + "&query=" +URLEncoder.encode(sql,"UTF-8") ;
        int retryCount = 0;
        while (true) {
            long start = System.currentTimeMillis() / 1000;
            Integer readTimeOut = (retryCount + 1) * READ_TIMEOUT;
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            OkHttpClient client = builder.readTimeout(readTimeOut, TimeUnit.SECONDS).build();
            MediaType mediaType = MediaType.parse("text/plain");
            RequestBody body = RequestBody.create(mediaType, "");
            Request request = new Request.Builder().url(url).method("POST", body)/*.addHeader("Accept-Encoding", "gzip")*/.build();
            try (Response response = client.newCall(request).execute()) {

                BufferedReader br = new BufferedReader(new InputStreamReader(response.body().byteStream()));
                String str = null;
                int k = 0;
                boolean append = false;
                outputList = new ArrayList<>();
                while ((str = br.readLine()) != null) {
                    if(response.code()>=HTTP_EXPORT_RESPONSE_ERROR)
                    {
                        err[0]=err[0]+"\r\n"+str;
                        continue;
                    }
                    k++;
                    //List temp = makeCompititorv2(str);
                    outputList.add(str);
                }

                if(response.code()>=HTTP_EXPORT_RESPONSE_ERROR)
                {
                    System.out.println("HTTP_EXPORT_RESPONSE_ERROR");
                }
                return outputList;
            } catch (SocketTimeoutException ex) {
                ex.printStackTrace();
                retryCount++;
                if (retryCount >= 3) {
                    System.out.println("time out");
                    return  null;
                }
                Thread.sleep(500);
            } catch (Exception e) {
                e.printStackTrace();
                return  null;
                //return false;
            }
        }
    }
    private static String getLastClarityDbDay() throws Exception{
        Date currentDate = new Date();
        // 创建Calendar对象
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        // 将日期减少一个月
        calendar.add(Calendar.MONTH, -1);
        // 获取减少一个月后的日期
        Date lastMonthDate = calendar.getTime();
        SimpleDateFormat sp = new SimpleDateFormat("yyyyMM");
        return sp.format(lastMonthDate);
    }

    private static String getEta(double days)throws Exception{
        Date currentDate = new Date();
        // 创建Calendar对象
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(currentDate);
        // 将日期减少一个月
        calendar.add(Calendar.DATE, (int)Math.ceil(days) -1);
        // 获取减少一个月后的日期
        Date lastMonthDate = calendar.getTime();
        SimpleDateFormat sp = new SimpleDateFormat("yyyy/MM/dd");
        return sp.format(lastMonthDate);
    }

    public static final String SINGLE_QUOTE = "'";
    private static final String  fulloutfilename = "/home/<USER>/outfiles/auto7/";
    private static final boolean IS_DEBUG = true;
    private static long endIndex = 0;
    private static boolean isFinished;
    private static double days;
    private static int hours =0;
    private static int today =1;
    public static void main(String args[]) {
        String inputFile = args[0];
        Integer rank = Integer.parseInt( args[1]);
        //System.out.println("=====input file path :" + inputFile +" ,rank"+rank +" ,dbDay is " );
        isFinished = false;

        //endIndex = 5000;
        //String inputfilepath = "/home/<USER>/inputfiles/20230712/keyword_list_hotels.txt";
        seoClarityKeywordMonthlySearchEngineRelationEntityDAO = SpringBeanFactory.getBean("seoClarityKeywordMonthlySearchEngineRelationEntityDAO");
        try{
            System.out.println("=====input file path :" + inputFile +" ,rank"+rank +" ,dbDay is " + getLastClarityDbDay());

            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            //System.out.println("=====current time:"+formatter.format(new Date())+"=====");
                int size =1000;
                String dburl2 ="http://23.105.14.193:8123";
                List<String> list =  RankUtils.loadFileListV3(inputFile,endIndex);
                System.out.println("=====exctrating begin");
                days = days + 1;
                //sendMailReport("Hi team:\n    Total input keywords count is "+list.size()+", \n the extract process started ,  checked exist keywords count is:" + listChecked.size() +" \n It will take "+ String.format("%.2f", days)+" days ETA is :"+getEta(days));
                long start = System.currentTimeMillis()/1000 ;
                doTaskRank1161599571(size,0,0,getLastClarityDbDay(),list,dburl2,rank);
                long end = System.currentTimeMillis() /1000;
                System.out.println("===== costed:"+(end - start));
                System.out.println("=====exctrating end");
                System.out.println("=====finished!!!!!!!!!!!");
                //isFinished= true;

        }catch (Exception ex){
            ex.printStackTrace();
            System.out.println("err");
        }
    }

}

