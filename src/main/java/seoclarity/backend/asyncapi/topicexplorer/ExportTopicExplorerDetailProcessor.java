package seoclarity.backend.asyncapi.topicexplorer;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.asyncapi.AsynTaskProcessor;
import seoclarity.backend.entity.ClarityDBConstants;
import seoclarity.backend.entity.actonia.ApiTaskInstanceEntity;
import seoclarity.backend.utils.*;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-04-30
 * @path seoclarity.backend.asyncapi.topicexplorer.ExportTopicExplorerDetailProcessor
 * TASKNAME_RESEARCHGRID_TOPICEXPLORER_KEYWORD
 * https://www.wrike.com/open.htm?id=1645493716
 */
public class ExportTopicExplorerDetailProcessor {
    private static final String TMP_FILE_PATH = "/tmp/";
    private static final boolean isDev = false;
    private static final String API_PATH = isDev ? "https://s11-dev.seoclarity.dev/seoClarity/" : "https://api.seoclarity.net/";
    private static final String API_RETRIEVE_SV_ENDPOINT = API_PATH + "ckKeywordSv/getRealtimeExactKeywordsSv";
    private static final int TIME_OUT = 60 * 1000;
    private static final String OUTPUT_FILE_SPLIT = "\t";
    private static final Map<String, String> COMMON_HEADERS = new HashMap<>(){
        {
            put("Accept", "application/json");
            put("Content-Type", "application/json");
        }
    };

    private String chServerUrl;
    private String chDatabase;
    private String user;
    private String password;
    private ApiTaskInstanceEntity apiTaskInstanceEntity;
    private String matchType;
    private List<String> requestKeywords;
    private Integer engineId;
    private Integer languageId;

    private ExportTopicExplorerDetailProcessor(ApiTaskInstanceEntity apiTaskInstanceEntity, String chServerUrl, String chDatabase, String user, String password,
                                               Map<String, Object> workerParameterMap) {
        this.apiTaskInstanceEntity = apiTaskInstanceEntity;
        this.chServerUrl = chServerUrl;
        this.chDatabase = chDatabase;
        this.user = user;
        this.password = password;
        this.matchType = workerParameterMap.get("matchType") != null ? workerParameterMap.get("matchType").toString() : "";
        if (workerParameterMap.get("requestKeywords") != null) {
            cn.hutool.json.JSONArray monnthArray = JSONUtil.parseArray(workerParameterMap.get("requestKeywords").toString());
            this.requestKeywords  = JSONUtil.toList(monnthArray, String.class);
        }
        this.engineId = workerParameterMap.get("engineId") != null ? Integer.parseInt(workerParameterMap.get("engineId").toString()) : 0;
        this.languageId = workerParameterMap.get("languageId") != null ? Integer.parseInt(workerParameterMap.get("languageId").toString()) : 0;
    }

    public static ExportTopicExplorerDetailProcessor getInstance(ApiTaskInstanceEntity apiTaskInstanceEntity, String chServerUrl, String chDatabase, String user, String password,
                                                                 Map<String, Object> workerParameterMap) {
        return new ExportTopicExplorerDetailProcessor(apiTaskInstanceEntity, chServerUrl, chDatabase, user, password, workerParameterMap);
    }

    public int processExport(String sql, String fullFileName, boolean haveFileHeader, List<String> headers, List<String> listMonth, boolean isZip, String[] err) throws Exception {
        int outCnt = 0;
        String tempFile = createTempFile(apiTaskInstanceEntity, ".tmp");
        File outFile = new File(fullFileName);
        if (outFile.exists()) {
            outFile.delete();
        }
        // https://www.wrike.com/open.htm?id=1645493716
        // only for exact match
        boolean isExactMatch = StringUtils.equalsIgnoreCase(matchType, "em");
        List<String> responseKeywords = new ArrayList<>(0);

        System.out.println("=====fullFileName:" + fullFileName);
        System.out.println("=====headers:" + JSONUtil.toJsonStr(headers));
        System.out.println("=====tempFile:" + tempFile);
        System.out.println("=====iscsv:" + haveFileHeader);
        System.out.println("=====isExactMatch:" + isExactMatch);

//        int responseCode = ClarityDBUtils.httpExportFromClarityDB(chServerUrl, chDatabase, user, password, sql, tempFile, false, false, err);
//        if (responseCode < 0) {
//            responseCode = ClarityDBUtils.httpExportFromClarityDB(AsynTaskProcessor.CH_DB_RG_CENTRAL_URL_BK, chDatabase, user, password, sql, tempFile, false, false, err);
//            if (responseCode < 0) {
//                throw new Exception("ExportTopicExplorerDetailProcessor.processExport() error: " + err[0]);
//            }
//        }

        String queryUrl = ClarityDBConstants.CKADSHOST_CENTRAL + "/?database=" + chDatabase + "&enable_http_compression=1&query=";
        boolean isComplete = ClarityDBUtils.postToFile(queryUrl, sql, tempFile, null);
        if (!isComplete) {
            throw new Exception("ExportTopicExplorerDetailProcessor.processExport()");
        }

        List<String> outputList = new ArrayList<String>();
        // get header
        List<String> newHeaders = CSVParser.parseJsonToCsv_monthly_volumes_header(headers, listMonth);
        if (haveFileHeader) {
            // export header
            String headerLine = newHeaders.stream().collect(Collectors.joining("\t"));
            FileUtils.writeLines(outFile, "UTF-8", Arrays.asList(headerLine), true);
        }
        // parse the temp export file
        try (BufferedReader br = new BufferedReader(new InputStreamReader(new FileInputStream(tempFile), "UTF-8"))) {
            String rowStr = null;
            while ((rowStr = br.readLine()) != null) {
                String parsedLine = null;
                if (haveFileHeader) {
                    parsedLine = CSVParser.parseJsonToCsv_topicexploere(rowStr, headers, newHeaders, listMonth);
                } else {
                    parsedLine = CSVParser.parseJson_topicexploere(rowStr, headers, newHeaders, listMonth);
                }
                outCnt++;
                if (isExactMatch) {
                    responseKeywords.add(getResponseKeywordByLine(rowStr));
                }
                outputList.add(parsedLine);
                if (outputList.size() >= 10000) {
                    FileUtils.writeLines(outFile, "UTF-8", outputList, true);
                    outputList.clear();
                }
            }
            if (outputList.size() > 0) {
                FileUtils.writeLines(outFile, "UTF-8", outputList, true);
                outputList.clear();
            }

            // retrieve the not exists keywords' sv and export
            if (isExactMatch) {
                responseKeywords = responseKeywords.stream().filter(StringUtils::isNotBlank).map(String::toLowerCase).toList();
                requestKeywords = requestKeywords.stream().map(String::toLowerCase).collect(Collectors.toList());
                requestKeywords.removeAll(responseKeywords);
                System.out.println("=====responseKeywords:" + responseKeywords.size());
                System.out.println("=====retrieveKeywords:" + requestKeywords.size());
                if (!requestKeywords.isEmpty()) {
                    // request api to retrieve sv
                    List<String> newLines = retrieveSvFoeKeywords(requestKeywords, listMonth, newHeaders, haveFileHeader);
                    if (!newLines.isEmpty()) {
                        outCnt = outCnt + newLines.size();
                        FileUtils.writeLines(outFile, "UTF-8", newLines, true);
                    }
                }
            }

            if (isZip) {
                zipFileName(fullFileName, outFile);
            }

            return outCnt;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            FileUtils.deleteQuietly(new File(tempFile));
        }

        return ClarityDBUtils.HTTP_EXPORT_CLARITYDB_TO_FILE_FAILD;
    }

    /**
     * {
     * "ownDomainId": 4661,
     * "engineId": 1,
     * "languageId": 1,
     * "queryMultiKeyword": ["shoes", "cars"],
     * "userId": 0,
     * "requestCommandName": "paidApi"
     * }
     */
    private List<String> retrieveSvFoeKeywords(List<String> keywords, List<String> monthList, List<String> headers, boolean isCsv) {
        Map<String, Object> params = new HashMap<>();
        params.put("ownDomainId", apiTaskInstanceEntity.getOwnDomainId());
        params.put("queryMultiKeyword", keywords);
        params.put("userId", apiTaskInstanceEntity.getUserId());
        params.put("requestCommandName", "paidApi");
        params.put("engineId", engineId);
        params.put("languageId", languageId);
        params.put("accessToken", ClarityDBAPIUtils.ACCESS_TOKEN_VALUE);

        String requestParams = JSONUtil.toJsonStr(params);
        String response = HttpRequestUtils.queryWebServiceFunctionPost(API_RETRIEVE_SV_ENDPOINT, requestParams, COMMON_HEADERS, 10, TIME_OUT);
        List<String> resultLines = new ArrayList<>(0);
        /**
         * {
         *     "data": [
         *         {
         *             "cpc": 0.7,
         *             "competition": "HIGH",
         *             "search_volume": 1000000.0,
         *             "keyword": "shoes",
         *             "monthly_searches": [
         *                 {
         *                     "month": 3.0,
         *                     "year": 2025.0,
         *                     "search_volume": 1000000.0
         *                 },
         *                 ...
         *             ]
         *         }
         *     ],
         *     "lastRefreshDate": 202412
         * }
         */
        JSONObject json = JSONUtil.parseObj(response);
        if (json.containsKey("data")) {
            json.getJSONArray("data").forEach(item -> {
                JSONObject o = (JSONObject) item;
                String kw = o.getStr("keyword");
                float cpc = StringUtils.isBlank(o.getStr("cpc")) ? 0f : Float.parseFloat(String.format("%.0f", o.getFloat("cpc")));
                Integer searchVolumeVal = StringUtils.isBlank("search_volume") ? -1 : Integer.valueOf(String.format("%.0f", o.getFloat("search_volume")));
                JSONArray monthlySearches = o.getJSONArray("monthly_searches");
                Integer searchVolume = searchVolumeVal;
                Map<String, Object> svTrendList = new HashMap<>();
                if (monthlySearches != null && monthlySearches.size() > 0) {
                    monthlySearches.forEach(svItem -> {
                        JSONObject o2 = (JSONObject) svItem;
                        String month = String.format("%02.0f", o2.getFloat("month"));
                        String year = String.format("%.0f", o2.getFloat("year"));
                        Integer sv = Integer.valueOf(String.format("%.0f", o2.getFloat("search_volume")));
                        String yearStr = year + month;
                        svTrendList.put(yearStr, sv);
                    });
                }
//                System.out.println("===kw:" + kw + " cpc:" + cpc + " search_volume:" + searchVolume + ", svTrendList:" + svTrendList);
                JSONArray svTrenArray = new JSONArray();
                monthList.forEach(month -> {
                    Object sv = svTrendList.get(month);
                    JSONObject newSv = new JSONObject();
                    if (sv != null) {
                        newSv.put("yearmonth", month);
                        newSv.put("monthly_volume", sv);
                    } else {
                        newSv.put("yearmonth", month);
                        newSv.put("monthly_volume", 0);
                    }
                    svTrenArray.add(newSv);
                });

                if (isCsv) {
                    List<Object> newLine = new ArrayList<>(0);
                    headers.forEach(header -> {
                        if (StringUtils.equalsIgnoreCase(header, "Keyword Name")) {
                            newLine.add(kw);
                        } else if (StringUtils.equalsIgnoreCase(header, "Avg Search Volume")) {
                            newLine.add(searchVolume);
                        } else if (StringUtils.equalsIgnoreCase(header, "CPC")) {
                            newLine.add(cpc);
                        } else if (StringUtils.equalsIgnoreCase(header, "monthly_volumes")) {
                            newLine.add(svTrenArray.toString());
                        } else {
                            newLine.add("");
                        }
                    });
                    resultLines.add(StringUtils.join(newLine, OUTPUT_FILE_SPLIT));
                } else {
                    JSONObject newLine = new JSONObject();
                    headers.forEach(header -> {
                        if (StringUtils.equalsIgnoreCase(header, "Keyword Name")) {
                            newLine.put(header, kw);
                        } else if (StringUtils.equalsIgnoreCase(header, "Avg Search Volume")) {
                            newLine.put(header, searchVolume);
                        } else if (StringUtils.equalsIgnoreCase(header, "CPC")) {
                            newLine.put(header, cpc);
                        } else if (StringUtils.equalsIgnoreCase(header, "monthly_volumes")) {
                            newLine.put(header, svTrenArray);
                        } else {
                            newLine.put(header, "");
                        }
                    });
                    resultLines.add(newLine.toString());
                }

            });
        }
        return resultLines;
    }

    private String getResponseKeywordByLine(String jsonLine) {
        try {
            if (StringUtils.isNotBlank(jsonLine)) {
                JSONObject json = JSONUtil.parseObj(jsonLine);
                return json.getStr("Keyword Name");
            }
        } catch (Exception e) {
            System.out.println("=== can not parse jsonLine:" + jsonLine);
            e.printStackTrace();
        }
        return null;
    }

    private String zipFileName(String fullFileName, File outFile) throws Exception {
        // https://www.wrike.com/open.htm?id=1265219674
        String fullNameZip = fullFileName + ".gz";
        List<File> srcFiles = new ArrayList<>();
        List<String> srcFilesName = new ArrayList<>();
        srcFiles.add(outFile);
        for (File aFile : srcFiles) {
            srcFilesName.add(aFile.getAbsolutePath());
        }
        GZipUtil.createGz(srcFilesName.toArray(new String[srcFilesName.size()]), fullNameZip);
        outFile.delete();
        System.out.println("===zip file, fullNameZip:" + fullNameZip + ", outFile:" + outFile.getName() + " deleted.");
        return fullNameZip;
    }

    private String createTempFile(ApiTaskInstanceEntity apiTaskInstanceEntity, String fileSuffix) {
        int oid = apiTaskInstanceEntity.getOwnDomainId();
        String taskId = apiTaskInstanceEntity.getTaskId();
        String fileName = "TopicExplorer_Keyword_" + oid + "_" + taskId + "_" + System.currentTimeMillis() + "_" + fileSuffix;
        File file = new File(TMP_FILE_PATH + fileName);
        if (file.exists()) {
            file.delete();
        }
        return file.getAbsolutePath();
    }
}
