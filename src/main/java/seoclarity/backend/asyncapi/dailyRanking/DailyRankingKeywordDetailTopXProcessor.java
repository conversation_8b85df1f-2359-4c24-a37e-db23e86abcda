package seoclarity.backend.asyncapi.dailyRanking;

import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.GZipUtil;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025-02-10
 * @path seoclarity.backend.asyncapi.dailyRanking.DailyRankingKeywordDetailTopXProcessor
 * https://www.wrike.com/open.htm?id=1589723934
 */
public class DailyRankingKeywordDetailTopXProcessor {
    public static final String CDB_RI_URL = "http://cdb-ri-backup-first-external:8123";
    public static final String CH_DB_RI = "seo_daily_ranking";
    public static String USER = "default";
    public static String PSW = "clarity99!";

    public static Integer processExport(String resultStr, String fullFileName, boolean isFileFormatCSV, String[] err) throws Exception {
        // convert resultStr to gson object
        JsonParser jsonParser = new JsonParser();
        JsonObject obj = jsonParser.parse(resultStr).getAsJsonObject();

        String splPattern = obj.get("sql").getAsString();
        JsonArray headers = obj.getAsJsonArray("header");
        String fileFormate = obj.get("fileFormate").getAsString();
        String pageSize = obj.get("pageSize").getAsString();


        List<String> headerList = new ArrayList<>();
        headers.forEach(o -> {
            headerList.add(o.getAsString());
        });
        JsonObject dateTableMapObj =  obj.getAsJsonObject("dateTableMap");
        Map<String, Map<String, String>> dateTableMap = new Gson().fromJson(dateTableMapObj, new TypeToken<Map<String, Map<String, String>>>() {
        }.getType());

        List<String> dateList = new ArrayList<>();
        Map<String, String> sqlMap = dateTableMap.entrySet().stream().map(m -> {
            String date = m.getKey();
            Map<String, String> tableMap = m.getValue();
            String infoTable = tableMap.get("infoTable");
            String detailTable = tableMap.get("detailTable");
            String subrankTable = tableMap.get("subrankTable");

            String sql = new String(splPattern);
            sql = StringUtils.replace(sql, "{%infoTable%}", infoTable);
            sql = StringUtils.replace(sql, "{%detailTable%}", detailTable);
            sql = StringUtils.replace(sql, "{%subrankTable%}", subrankTable);
            sql = StringUtils.replace(sql, "{%date%}", date);

            dateList.add(date);

            return new String[]{date, sql};
        }).collect(Collectors.toMap(array -> array[0], array -> array[1]));

//        System.out.println("=============splPattern:" + splPattern);
        System.out.println("=============headerList:" + headerList);
        System.out.println("=============dateList:" + dateList);
        System.out.println("=============sqlList:" + sqlMap.size());

        List<String> fileList = new ArrayList<>();
        AtomicInteger totalCnt = new AtomicInteger();
        // execute sql
        sqlMap.forEach((date, querySql) -> {
            String createDateFile = StringUtils.substringBeforeLast(fullFileName, ".") + "_" + date;
            String fileSuffix = StringUtils.substringAfterLast(fullFileName, ".");
            String fullFileNameWithDate = createDateFile + "." + fileSuffix;
            System.out.println("=============fullFileNameWithDate:" + fullFileNameWithDate);
            System.out.println("=============querySql:" + querySql);
            try {
                int rowCount = ClarityDBUtils.httpExportFromClarityDB(CDB_RI_URL, CH_DB_RI, USER, PSW, querySql + "", fullFileNameWithDate, isFileFormatCSV, false, err);
                System.out.println("=============rowCount:" + rowCount);
                totalCnt.set(totalCnt.get() + rowCount);
                fileList.add(fullFileNameWithDate);
            } catch (Exception e) {
                System.out.println("===Export date:" + date + " failed. fullFileNameWithDate:" + fullFileNameWithDate + ", sql:" + querySql);
                e.printStackTrace();
            }
        });

        // zip file
        String fullNameZip = String.join(".", fullFileName, "zip");
        List<String> srcFiles = fileList.stream().map(f -> {
            File file = new File(f);
            if (file.exists()) {
                return file.getAbsolutePath();
            } else {
                System.out.println("===Wrong, can not find file:" + f);
                return null;
            }
        }).filter(Objects::nonNull).collect(Collectors.toList());
        System.out.println("============source srcFiles:" + srcFiles);
        System.out.println("============target fullNameZip:" + fullNameZip);
        GZipUtil.createZip(srcFiles.toArray(new String[0]),fullNameZip);
        fileList.forEach(f -> {
            try {
                File file = new File(f);
                if (file.exists()) {
                    file.delete();
                }
            } catch (Exception e) {e.printStackTrace();}
        });

        return totalCnt.get();
    }

}
