package seoclarity.backend.asyncapi.dailyRanking;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-02-14
 * @path seoclarity.backend.asyncapi.dailyRanking.DailyRankingKeywordDetailTopXConstants
 */
public class DailyRankingKeywordDetailTopXConstants {

    static class KeywordInfo {
        /**
         * "ranking_date": "2025-02-13",
         * "keyword_rankcheck_id": 102498247,
         * "keyword_name": "zipcar rent a car cheektowaga airport",
         * "location_id": 0,
         * "keyword_create_date": 20240812,
         * "avg_search_volume": 20,
         * "answerbox": 0,
         * "buyingGuide": 0,
         * "discussionAndForums": 0,
         * "app": 0,
         * "popularStore": 0,
         * "amp": 0,
         * "faq": 0,
         * "freeShop": 0,
         * "job": 0,
         * "knog": 1,
         * "peoplealsoask": 1,
         * "products": 0,
         * "ppc": 0,
         * "pla": 0,
         * "thingsToKnow": 1,
         * "ai_genai_search": 0,
         * "address": 0,
         * "img": 1,
         * "recipes": 0,
         * "ll": 0,
         * "hotel": 0,
         * "news": 0,
         * "estimatedSalary": 0,
         * "video": 0,
         * "shortVideo": 0,
         * "interestingFinds": 0,
         * "comparison": 0,
         * "populardestinations": 0,
         * "topsights": 0,
         * "twitter": 0,
         * "finance": 0,
         * "findresultson": 0,
         * "nearyou": 0,
         * "fromsourcesacrosstheweb": 0,
         * "appbarDisplayKeywords": "-",
         * "appbarSearchedKeywords": "-",
         * "cityName": "national",
         * "tags": [
         * 7692763,
         * 7730549
         * ],
         * "hierarchyTags": [[], []],
         * "keyword_hash": "14971192570515074362",
         * "plpUrlList": []
         */
        private String ranking_date;
        private String keyword_rankcheck_id;
        private String keyword_name;
        private Integer location_id;
        private String keyword_create_date;
        private String avg_search_volume;
        private String trueDemand;
        private String cpc;
        private String googleRecommend;
        private String totalResults;
        private Integer answerbox;
        private Integer buyingGuide;
        private Integer discussionAndForums;
        private Integer app;
        private Integer popularStore;
        private Integer amp;
        private Integer faq;
        private Integer freeShop;
        private Integer job;
        private Integer knog;
        private Integer peoplealsoask;
        private Integer products;
        private Integer ppc;
        private Integer pla;
        private Integer thingsToKnow;
        private Integer ai_genai_search;
        private Integer address;
        private Integer img;
        private Integer recipes;
        private Integer ll;
        private Integer hotel;
        private Integer flight;
        private Integer news;
        private Integer estimatedSalary;
        private Integer video;
        private Integer shortVideo;
        private Integer interestingFinds;
        private Integer comparison;
        private Integer populardestinations;
        private Integer topsights;
        private Integer twitter;
        private Integer finance;
        private Integer findresultson;
        private Integer nearyou;
        private Integer fromsourcesacrosstheweb;
        private String appbarDisplayKeywords;
        private String appbarSearchedKeywords;
        private List<String> peopleAlsoAskList;
        private String refineBy_title;
        private String refineBy_detail;
        private String thingsToKnowStr;
        private String cityName;
        private List<String> tags;
        private List<List<Integer>> hierarchyTags;
        private String keyword_hash;
        private List<String> plpUrlList;
        private List<String> relatedSearches;

        public String getRanking_date() {
            return ranking_date;
        }

        public void setRanking_date(String ranking_date) {
            this.ranking_date = ranking_date;
        }

        public String getKeyword_rankcheck_id() {
            return keyword_rankcheck_id;
        }

        public void setKeyword_rankcheck_id(String keyword_rankcheck_id) {
            this.keyword_rankcheck_id = keyword_rankcheck_id;
        }

        public String getKeyword_name() {
            return keyword_name;
        }

        public void setKeyword_name(String keyword_name) {
            this.keyword_name = keyword_name;
        }

        public Integer getLocation_id() {
            return location_id;
        }

        public void setLocation_id(Integer location_id) {
            this.location_id = location_id;
        }

        public String getKeyword_create_date() {
            return keyword_create_date;
        }

        public void setKeyword_create_date(String keyword_create_date) {
            this.keyword_create_date = keyword_create_date;
        }

        public String getAvg_search_volume() {
            return avg_search_volume;
        }

        public void setAvg_search_volume(String avg_search_volume) {
            this.avg_search_volume = avg_search_volume;
        }

        public Integer getAnswerbox() {
            return answerbox;
        }

        public void setAnswerbox(Integer answerbox) {
            this.answerbox = answerbox;
        }

        public Integer getBuyingGuide() {
            return buyingGuide;
        }

        public void setBuyingGuide(Integer buyingGuide) {
            this.buyingGuide = buyingGuide;
        }

        public Integer getDiscussionAndForums() {
            return discussionAndForums;
        }

        public void setDiscussionAndForums(Integer discussionAndForums) {
            this.discussionAndForums = discussionAndForums;
        }

        public Integer getApp() {
            return app;
        }

        public void setApp(Integer app) {
            this.app = app;
        }

        public Integer getPopularStore() {
            return popularStore;
        }

        public void setPopularStore(Integer popularStore) {
            this.popularStore = popularStore;
        }

        public Integer getAmp() {
            return amp;
        }

        public void setAmp(Integer amp) {
            this.amp = amp;
        }

        public Integer getFaq() {
            return faq;
        }

        public void setFaq(Integer faq) {
            this.faq = faq;
        }

        public Integer getFreeShop() {
            return freeShop;
        }

        public void setFreeShop(Integer freeShop) {
            this.freeShop = freeShop;
        }

        public Integer getJob() {
            return job;
        }

        public void setJob(Integer job) {
            this.job = job;
        }

        public Integer getKnog() {
            return knog;
        }

        public void setKnog(Integer knog) {
            this.knog = knog;
        }

        public Integer getPeoplealsoask() {
            return peoplealsoask;
        }

        public void setPeoplealsoask(Integer peoplealsoask) {
            this.peoplealsoask = peoplealsoask;
        }

        public Integer getProducts() {
            return products;
        }

        public void setProducts(Integer products) {
            this.products = products;
        }

        public Integer getPpc() {
            return ppc;
        }

        public void setPpc(Integer ppc) {
            this.ppc = ppc;
        }

        public Integer getPla() {
            return pla;
        }

        public void setPla(Integer pla) {
            this.pla = pla;
        }

        public Integer getThingsToKnow() {
            return thingsToKnow;
        }

        public void setThingsToKnow(Integer thingsToKnow) {
            this.thingsToKnow = thingsToKnow;
        }

        public Integer getAi_genai_search() {
            return ai_genai_search;
        }

        public void setAi_genai_search(Integer ai_genai_search) {
            this.ai_genai_search = ai_genai_search;
        }

        public Integer getAddress() {
            return address;
        }

        public void setAddress(Integer address) {
            this.address = address;
        }

        public Integer getImg() {
            return img;
        }

        public void setImg(Integer img) {
            this.img = img;
        }

        public Integer getRecipes() {
            return recipes;
        }

        public void setRecipes(Integer recipes) {
            this.recipes = recipes;
        }

        public Integer getLl() {
            return ll;
        }

        public void setLl(Integer ll) {
            this.ll = ll;
        }

        public Integer getHotel() {
            return hotel;
        }

        public void setHotel(Integer hotel) {
            this.hotel = hotel;
        }

        public Integer getNews() {
            return news;
        }

        public void setNews(Integer news) {
            this.news = news;
        }

        public Integer getEstimatedSalary() {
            return estimatedSalary;
        }

        public void setEstimatedSalary(Integer estimatedSalary) {
            this.estimatedSalary = estimatedSalary;
        }

        public Integer getVideo() {
            return video;
        }

        public void setVideo(Integer video) {
            this.video = video;
        }

        public Integer getShortVideo() {
            return shortVideo;
        }

        public void setShortVideo(Integer shortVideo) {
            this.shortVideo = shortVideo;
        }

        public Integer getInterestingFinds() {
            return interestingFinds;
        }

        public void setInterestingFinds(Integer interestingFinds) {
            this.interestingFinds = interestingFinds;
        }

        public Integer getComparison() {
            return comparison;
        }

        public void setComparison(Integer comparison) {
            this.comparison = comparison;
        }

        public Integer getPopulardestinations() {
            return populardestinations;
        }

        public void setPopulardestinations(Integer populardestinations) {
            this.populardestinations = populardestinations;
        }

        public Integer getTopsights() {
            return topsights;
        }

        public void setTopsights(Integer topsights) {
            this.topsights = topsights;
        }

        public Integer getTwitter() {
            return twitter;
        }

        public void setTwitter(Integer twitter) {
            this.twitter = twitter;
        }

        public Integer getFinance() {
            return finance;
        }

        public void setFinance(Integer finance) {
            this.finance = finance;
        }

        public Integer getFindresultson() {
            return findresultson;
        }

        public void setFindresultson(Integer findresultson) {
            this.findresultson = findresultson;
        }

        public Integer getNearyou() {
            return nearyou;
        }

        public void setNearyou(Integer nearyou) {
            this.nearyou = nearyou;
        }

        public Integer getFromsourcesacrosstheweb() {
            return fromsourcesacrosstheweb;
        }

        public void setFromsourcesacrosstheweb(Integer fromsourcesacrosstheweb) {
            this.fromsourcesacrosstheweb = fromsourcesacrosstheweb;
        }

        public String getAppbarDisplayKeywords() {
            return appbarDisplayKeywords;
        }

        public void setAppbarDisplayKeywords(String appbarDisplayKeywords) {
            this.appbarDisplayKeywords = appbarDisplayKeywords;
        }

        public String getAppbarSearchedKeywords() {
            return appbarSearchedKeywords;
        }

        public void setAppbarSearchedKeywords(String appbarSearchedKeywords) {
            this.appbarSearchedKeywords = appbarSearchedKeywords;
        }

        public String getCityName() {
            return cityName;
        }

        public void setCityName(String cityName) {
            this.cityName = cityName;
        }

        public List<String> getTags() {
            return tags;
        }

        public void setTags(List<String> tags) {
            this.tags = tags;
        }

        public List<List<Integer>> getHierarchyTags() {
            return hierarchyTags;
        }

        public void setHierarchyTags(List<List<Integer>> hierarchyTags) {
            this.hierarchyTags = hierarchyTags;
        }

        public String getKeyword_hash() {
            return keyword_hash;
        }

        public void setKeyword_hash(String keyword_hash) {
            this.keyword_hash = keyword_hash;
        }

        public List<String> getPlpUrlList() {
            return plpUrlList;
        }

        public void setPlpUrlList(List<String> plpUrlList) {
            this.plpUrlList = plpUrlList;
        }

        public String getCpc() {
            return cpc;
        }

        public void setCpc(String cpc) {
            this.cpc = cpc;
        }

        public String getGoogleRecommend() {
            return googleRecommend;
        }

        public void setGoogleRecommend(String googleRecommend) {
            this.googleRecommend = googleRecommend;
        }

        public String getTotalResults() {
            return totalResults;
        }

        public void setTotalResults(String totalResults) {
            this.totalResults = totalResults;
        }

        public String getTrueDemand() {
            return trueDemand;
        }

        public void setTrueDemand(String trueDemand) {
            this.trueDemand = trueDemand;
        }

        public Integer getFlight() {
            return flight;
        }

        public void setFlight(Integer flight) {
            this.flight = flight;
        }

        public List<String> getPeopleAlsoAskList() {
            return peopleAlsoAskList;
        }

        public void setPeopleAlsoAskList(List<String> peopleAlsoAskList) {
            this.peopleAlsoAskList = peopleAlsoAskList;
        }

        public String getRefineBy_title() {
            return refineBy_title;
        }

        public void setRefineBy_title(String refineBy_title) {
            this.refineBy_title = refineBy_title;
        }

        public String getRefineBy_detail() {
            return refineBy_detail;
        }

        public void setRefineBy_detail(String refineBy_detail) {
            this.refineBy_detail = refineBy_detail;
        }

        public String getThingsToKnowStr() {
            return thingsToKnowStr;
        }

        public void setThingsToKnowStr(String thingsToKnowStr) {
            this.thingsToKnowStr = thingsToKnowStr;
        }

        public List<String> getRelatedSearches() {
            return relatedSearches;
        }

        public void setRelatedSearches(List<String> relatedSearches) {
            this.relatedSearches = relatedSearches;
        }
    }

    static class KeywordDetail {
        /**
         * {
         * "ranking_date": "2025-02-13",
         * "keyword_rankcheck_id": 5863,
         * "locationId": 0,
         * "true_rank": 8,
         * "web_rank": 7,
         * "estdTraffic": 119.16428571428571,
         * "shareOfVoice": 0.0001,
         * "shareOfMarket": 0,
         * "rankingUrl": "https://www.expedia.com/St-Augustine-Hotels.d6047864.Travel-Guide-Hotels",
         * "sub_ranks": [
         * "1",
         * "2",
         * "3",
         * "4",
         * "5",
         * "6",
         * "7"
         * ],
         * "subRankUrls": [
         * "https://www.expedia.com/St-Augustine-Beach-Hotels.d553248634999246432.Travel-Guide-Hotels",
         * "https://www.expedia.com/St-Augustine-South-St-Augustine-Hotels.0-n553248623743419702-0.Travel-Guide-Filter-Hotels",
         * "https://www.expedia.com/St-Augustine-Beach-Hotels.0-l6063024-0.Travel-Guide-Filter-Hotels",
         * "https://www.expedia.com/St-Augustine-Hotels-Zen-Living-Suites.h23919.Hotel-Information",
         * "https://www.expedia.com/St-Augustine-Historic-District-St-Augustine-Hotels.0-n6339215-0.Travel-Guide-Filter-Hotels",
         * "https://www.expedia.com/St-Augustine-Hotels-Hampton-Inn-St-Augustine-Beach.h55129.Hotel-Information",
         * "https://www.expedia.com/St-Augustine-Hotels-The-Ponce-St-Augustine-Hotel.h6922.Hotel-Information"
         * ],
         * "subRankLabels": [
         * "St. Augustine Beach Hotels",
         * "Hotels in St. Augustine South",
         * "St. Augustine Beach",
         * "Stayable St. Augustine",
         * "St. Augustine Historic District",
         * "Hampton Inn St. Augustine...",
         * "The Ponce St. Augustine Hotel"
         * ]
         * }
         */
        private String ranking_date;
        private Integer keyword_rankcheck_id;
        private Integer locationId;
        private Integer true_rank;
        private Integer web_rank;
        private String estdTraffic;
        private String shareOfVoice;
        private String shareOfMarket;
        private String rankingUrl;
        private String label;
        private String meta;
        private List<String> sub_ranks;
        private List<String> subRankUrls;
        private List<String> subRankLabels;
        private List<String> subRankRatings;
        private List<String> subRankPrices;
        private List<String> subRankReviews;
        private List<String> subRankTags;
        private Integer type;
        private String ratingNumber;
        private String priceNumber;
        private String couponFlag;

        public String getRanking_date() {
            return ranking_date;
        }

        public void setRanking_date(String ranking_date) {
            this.ranking_date = ranking_date;
        }

        public Integer getKeyword_rankcheck_id() {
            return keyword_rankcheck_id;
        }

        public void setKeyword_rankcheck_id(Integer keyword_rankcheck_id) {
            this.keyword_rankcheck_id = keyword_rankcheck_id;
        }

        public Integer getLocationId() {
            return locationId;
        }

        public void setLocationId(Integer locationId) {
            this.locationId = locationId;
        }

        public Integer getTrue_rank() {
            return true_rank;
        }

        public void setTrue_rank(Integer true_rank) {
            this.true_rank = true_rank;
        }

        public Integer getWeb_rank() {
            return web_rank;
        }

        public void setWeb_rank(Integer web_rank) {
            this.web_rank = web_rank;
        }

        public String getEstdTraffic() {
            return estdTraffic;
        }

        public void setEstdTraffic(String estdTraffic) {
            this.estdTraffic = estdTraffic;
        }

        public String getShareOfVoice() {
            return shareOfVoice;
        }

        public void setShareOfVoice(String shareOfVoice) {
            this.shareOfVoice = shareOfVoice;
        }

        public String getShareOfMarket() {
            return shareOfMarket;
        }

        public void setShareOfMarket(String shareOfMarket) {
            this.shareOfMarket = shareOfMarket;
        }

        public String getRankingUrl() {
            return rankingUrl;
        }

        public void setRankingUrl(String rankingUrl) {
            this.rankingUrl = rankingUrl;
        }

        public List<String> getSub_ranks() {
            return sub_ranks;
        }

        public void setSub_ranks(List<String> sub_ranks) {
            this.sub_ranks = sub_ranks;
        }

        public List<String> getSubRankUrls() {
            return subRankUrls;
        }

        public void setSubRankUrls(List<String> subRankUrls) {
            this.subRankUrls = subRankUrls;
        }

        public List<String> getSubRankLabels() {
            return subRankLabels;
        }

        public void setSubRankLabels(List<String> subRankLabels) {
            this.subRankLabels = subRankLabels;
        }

        public Integer getType() {
            return type;
        }

        public void setType(Integer type) {
            this.type = type;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }

        public String getMeta() {
            return meta;
        }

        public void setMeta(String meta) {
            this.meta = meta;
        }

        public String getRatingNumber() {
            return ratingNumber;
        }

        public void setRatingNumber(String ratingNumber) {
            this.ratingNumber = ratingNumber;
        }

        public String getPriceNumber() {
            return priceNumber;
        }

        public void setPriceNumber(String priceNumber) {
            this.priceNumber = priceNumber;
        }

        public String getCouponFlag() {
            return couponFlag;
        }

        public void setCouponFlag(String couponFlag) {
            this.couponFlag = couponFlag;
        }

        public List<String> getSubRankRatings() {
            return subRankRatings;
        }

        public void setSubRankRatings(List<String> subRankRatings) {
            this.subRankRatings = subRankRatings;
        }

        public List<String> getSubRankPrices() {
            return subRankPrices;
        }

        public void setSubRankPrices(List<String> subRankPrices) {
            this.subRankPrices = subRankPrices;
        }

        public List<String> getSubRankReviews() {
            return subRankReviews;
        }

        public void setSubRankReviews(List<String> subRankReviews) {
            this.subRankReviews = subRankReviews;
        }

        public List<String> getSubRankTags() {
            return subRankTags;
        }

        public void setSubRankTags(List<String> subRankTags) {
            this.subRankTags = subRankTags;
        }
    }

    static class SerpSubRank {
        /**
         * keyword_rankcheck_id: 419856
         * locationId:           0
         * serpMatchType:        brand
         * url_type:             15
         * ranking_type:         4
         * trueRank:             1
         * sub_rank:             1
         * subRank_url:          J.Crew Factory
         * subRankLabel:         J.Crew Women's Maternity Jamie Pants
         * price:                $49.99
         * tag:                  -
         * ratingNumber:         -
         * reviews:
         */
        private Integer keyword_rankcheck_id;
        private Integer locationId;
        private String serpMatchType;
        private Integer url_type;
        private Integer ranking_type;
        private Integer trueRank;
        private Integer sub_rank;
        private String subRank_url;
        private String subRankLabel;
        private String price;
        private String tag;
        private String ratingNumber;
        private String reviews;

        public Integer getKeyword_rankcheck_id() {
            return keyword_rankcheck_id;
        }

        public void setKeyword_rankcheck_id(Integer keyword_rankcheck_id) {
            this.keyword_rankcheck_id = keyword_rankcheck_id;
        }

        public Integer getLocationId() {
            return locationId;
        }

        public void setLocationId(Integer locationId) {
            this.locationId = locationId;
        }

        public String getSerpMatchType() {
            return serpMatchType;
        }

        public void setSerpMatchType(String serpMatchType) {
            this.serpMatchType = serpMatchType;
        }

        public Integer getUrl_type() {
            return url_type;
        }

        public void setUrl_type(Integer url_type) {
            this.url_type = url_type;
        }

        public Integer getRanking_type() {
            return ranking_type;
        }

        public void setRanking_type(Integer ranking_type) {
            this.ranking_type = ranking_type;
        }

        public Integer getTrueRank() {
            return trueRank;
        }

        public void setTrueRank(Integer trueRank) {
            this.trueRank = trueRank;
        }

        public Integer getSub_rank() {
            return sub_rank;
        }

        public void setSub_rank(Integer sub_rank) {
            this.sub_rank = sub_rank;
        }

        public String getSubRank_url() {
            return subRank_url;
        }

        public void setSubRank_url(String subRank_url) {
            this.subRank_url = subRank_url;
        }

        public String getSubRankLabel() {
            return subRankLabel;
        }

        public void setSubRankLabel(String subRankLabel) {
            this.subRankLabel = subRankLabel;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public String getTag() {
            return tag;
        }

        public void setTag(String tag) {
            this.tag = tag;
        }

        public String getRatingNumber() {
            return ratingNumber;
        }

        public void setRatingNumber(String ratingNumber) {
            this.ratingNumber = ratingNumber;
        }

        public String getReviews() {
            return reviews;
        }

        public void setReviews(String reviews) {
            this.reviews = reviews;
        }
    }
}
