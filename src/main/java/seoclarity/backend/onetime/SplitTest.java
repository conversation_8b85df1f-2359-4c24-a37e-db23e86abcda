package seoclarity.backend.onetime;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.ParseException;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.util.EntityUtils;
import org.springframework.format.annotation.DateTimeFormat;
import seoclarity.backend.dao.clickhouse.SplitTestCentralDAO;
import seoclarity.backend.dao.clickhouse.realtimeurl.UrlCrawlInstanceDAO;
import seoclarity.backend.entity.clickhouse.realtimeurl.UrlCrawlInstance;
import seoclarity.backend.sender.realtimepagecrawl.RealTimePageCrawlUrlSender;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collectors;


@Slf4j
public class SplitTest {

    private final SplitTestCentralDAO<UrlMetrics> splitTestCentralDAO;
    private final UrlCrawlInstanceDAO urlCrawlInstanceDAO;
    private final RealTimePageCrawlUrlSender realTimePageCrawlUrlSender;
    private final LocalDate prePeriodStartDate;
    private final LocalDate prePeriodEndDate;
    private final LocalDate postPeriodStartDate;
    private final MetricsParams metricsParams;
    private final LocalDate postPeriodEndDate;
    private List<String> distinctUrlList;

    public SplitTest(MetricsParams metricsParams) {
        this.metricsParams = metricsParams;
        log.info("metricsParams: {}", metricsParams);
        final LocalDate endDate = metricsParams.getEndDate();
        prePeriodStartDate = endDate.minusDays(SplitTestHelper.DEFAULT_OFF_DAYS - 1);
        prePeriodEndDate = endDate;
        postPeriodStartDate = endDate.plusDays(1);
        postPeriodEndDate = endDate.plusDays(SplitTestHelper.DEFAULT_OFF_DAYS + 1);
        log.info("endDate: {}, prePeriodStartDate: {}, prePeriodEndDate: {}, postPeriodStartDate: {}, postPeriodEndDate: {}", endDate, prePeriodStartDate, prePeriodEndDate, postPeriodStartDate, postPeriodEndDate);
        this.splitTestCentralDAO = SpringBeanFactory.getBean("splitTestCentralDAO");
        urlCrawlInstanceDAO = SpringBeanFactory.getBean("urlCrawlInstanceDAO");
        realTimePageCrawlUrlSender = SpringBeanFactory.getBean("realTimePageCrawlUrlSender");
    }

    public static void main(String[] args) throws IOException {
        MetricsParams metricsParams;
        if (args.length == 0) {
            final JSONObject json = JSONUtil.readJSONObject(new File("metricsParams.json"), StandardCharsets.UTF_8);
            metricsParams = JSONUtil.toBean(json, MetricsParams.class);
            log.info("read metrics params from file: metricsParams.json");
        } else {
            final String arg = args[0];
            log.info("metrics params: {}", arg);
            metricsParams = JSONUtil.toBean(arg, MetricsParams.class);
        }
        if (metricsParams.getLoopCount() == null || metricsParams.getLoopCount() == 0) {
            log.warn("loop count in param is 0, will use default loop count: {}", SplitTestHelper.DEFAULT_LOOP_COUNT);
            metricsParams.setLoopCount(SplitTestHelper.DEFAULT_LOOP_COUNT);
        }
        if (metricsParams.getDomainId() == null || metricsParams.getDomainId() == 0) {
            throw new IllegalArgumentException("Invalid domain id: " + metricsParams.getDomainId());
        }
        if (metricsParams.getMetricsType() == null || metricsParams.getMetricsType() == 0) {
            throw new IllegalArgumentException("Invalid metrics type: " + metricsParams.getMetricsType());
        }
        if (metricsParams.getEndDate() == null) {
            LocalDate endDate = metricsParams.getMetricsType() == 1 ? SplitTestHelper.today.minusDays(SplitTestHelper.DEFAULT_OFF_DAYS + 1) : SplitTestHelper.today.minusDays(SplitTestHelper.DEFAULT_OFF_DAYS + 1 + 4);
            metricsParams.setEndDate(endDate);
        }
    }

    public List<String> getDistinctUrlList(String innerSql) {
        try {
            return this.splitTestCentralDAO.retrieveMetrics(innerSql);
        } catch (Exception e) {
            log.error("get distinct url error: {}", e.getMessage());
            return Collections.emptyList();
        }
    }

    public CorrelationReport start(String innerSql, int removeNonIndexableUrlsFlag) throws IOException {
        final String sql;
        try {
            sql = getRetrieveMetricsSql(innerSql);
            log.info("getRetrieveMetricsSql: {}", sql);
        } catch (Exception e) {
            log.error("getRetrieveMetricsSql error: {}\nsql: {}", e.getMessage(), innerSql);
            return null;
        }

        final CorrelationReport highestCorrelationReport;
        if (removeNonIndexableUrlsFlag == 1) {
            highestCorrelationReport = runShuffleSplitStrategy(sql);
        } else {
            highestCorrelationReport = runRandomDbSplitStrategy(sql);
        }

        log.info("highest correlation: {}, index: {}\n{}\n{}",
                highestCorrelationReport.getCorrelation(), highestCorrelationReport.getIndex(),
                highestCorrelationReport.summary(), highestCorrelationReport.report());

        return highestCorrelationReport;
    }

    private CorrelationReport runShuffleSplitStrategy(String sql) {
        List<UrlMetrics> allUrlMetrics = retrieveUrlMetricsFromDB(sql);
        if (allUrlMetrics.size() < 2) {
            log.warn("filteredUrlMetricsList size is less than 2, no need to split test. urlMetricsList size: {}", allUrlMetrics.size());
            return new CorrelationReport();
        }
        // use real time page crawl to remove non-indexable urls
        final Map<String, UrlMetrics> urlMetricsMap = allUrlMetrics.stream().collect(Collectors.toMap(UrlMetrics::getUrl, urlMetrics -> urlMetrics));
        final Set<String> urls = urlMetricsMap.keySet();
        final List<String> indexableUrls = this.removeNonIndexableUrls(urls, realTimePageCrawlUrlSender);
        if (indexableUrls.size() < 2) {
            log.warn("indexableUrls size is less than 2, no need to split test. indexableUrls size: {}", indexableUrls.size());
            return new CorrelationReport();
        }
        // use a new metrics list with only indexable urls
        final List<UrlMetrics> indexableUrlMetricsList = indexableUrls.stream().map(urlMetricsMap::get).toList();
        double sumCorrelation = 0.0;
        CorrelationReport highestCorrelationReport = new CorrelationReport();
        for (int i = 1; i <= this.metricsParams.getLoopCount(); i++) {
            CorrelationReport correlationReport = createAndSplitGroupsWithShuffle(indexableUrlMetricsList);
            correlationReport.setIndex(i);
            double correlation = calculateCorrelationForReport(correlationReport, i);
            correlationReport.setCorrelation(correlation);
            highestCorrelationReport = updateHighestCorrelationIfNeeded(correlationReport, highestCorrelationReport, i);
            sumCorrelation += correlationReport.getCorrelation();
        }
        highestCorrelationReport.setAverageCorrelation(sumCorrelation / this.metricsParams.getLoopCount());
        return highestCorrelationReport;
    }

    private List<String> removeNonIndexableUrls(Collection<String> allUrlMetrics, RealTimePageCrawlUrlSender realTimePageCrawlUrlSender) {
        final int urlSize = allUrlMetrics.size();
        // 1. send all urls to real time page crawl
        final int createDate = Integer.parseInt(LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE));
        final Integer domainId = metricsParams.domainId;
        final int instanceId = realTimePageCrawlUrlSender.getInstanceId(1, domainId, createDate);
        realTimePageCrawlUrlSender.processUrlList(domainId, instanceId, createDate, new Date(), new ArrayList<>(allUrlMetrics));
        // 2. wait for all urls to be crawled
        while (true) {
            try {
                Thread.sleep(30_000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            final int crawledCount = this.urlCrawlInstanceDAO.countByDomainIdAndCrawlInstanceId(domainId, instanceId);
            if (crawledCount == urlSize) {
                break;
            }
            log.info("domainId: {}, instanceId: {}, crawledCount: {}, urlSize: {}", domainId, instanceId, crawledCount, urlSize);
        }
        // 3. retrieve indexable urls
        final List<UrlCrawlInstance> urlCrawlInstances = this.urlCrawlInstanceDAO.queryByDomainIdAndCrawlInstanceId(domainId, instanceId);
        final List<String> indexableUrls = urlCrawlInstances.stream()
                .filter(instance -> instance.getIndexable() == 1 && instance.getResponseCode() == 200)
                .map(UrlCrawlInstance::getUrl).toList();
        log.info("indexableUrls size: {}, allUrlMetrics size: {}", indexableUrls.size(), allUrlMetrics.size());
        return indexableUrls;
    }

    private CorrelationReport runRandomDbSplitStrategy(String sql) {
        double sumCorrelation = 0.0;
        CorrelationReport highestCorrelationReport = new CorrelationReport();
        final String retrieveDataSql = sql + " ORDER BY rand() ASC";

        for (int i = 1; i <= this.metricsParams.getLoopCount(); i++) {
            List<UrlMetrics> urlMetricsList = retrieveUrlMetricsFromDB(retrieveDataSql);
            CorrelationReport correlationReport = createAndSplitGroups(urlMetricsList);
            correlationReport.setIndex(i);
            double correlation = calculateCorrelationForReport(correlationReport, i);
            correlationReport.setCorrelation(correlation);
            highestCorrelationReport = updateHighestCorrelationIfNeeded(correlationReport, highestCorrelationReport, i);
            sumCorrelation += correlationReport.getCorrelation();
        }
        highestCorrelationReport.setAverageCorrelation(sumCorrelation / this.metricsParams.getLoopCount());
        return highestCorrelationReport;
    }

    private List<UrlMetrics> retrieveUrlMetricsFromDB(String sql) {
        final List<UrlMetrics> metrics = splitTestCentralDAO.retrieveData(sql, UrlMetrics.class);
        return metrics.stream()
                .map(s -> SplitTestHelper.createUrlMetrics(s.url, s.logDateMetrics))
                .collect(Collectors.toList());
    }

    private CorrelationReport createAndSplitGroupsWithShuffle(List<UrlMetrics> allUrlMetrics) {
        List<UrlMetrics> shuffledMetrics = new ArrayList<>(allUrlMetrics);
        Collections.shuffle(shuffledMetrics);
        return createAndSplitGroups(shuffledMetrics);
    }

    private CorrelationReport createAndSplitGroups(List<UrlMetrics> urlMetricsList) {
        final CorrelationReport correlationReport = new CorrelationReport();
        correlationReport.setTime(LocalDateTime.now());

        if (urlMetricsList.size() < 2) {
            return correlationReport;
        }

        int midIndex = urlMetricsList.size() / 2;
        List<UrlMetrics> testGroup = new ArrayList<>(urlMetricsList.subList(0, midIndex));
        List<UrlMetrics> controlGroup = new ArrayList<>(urlMetricsList.subList(midIndex, urlMetricsList.size()));
        correlationReport.setTestGroup(testGroup);
        correlationReport.setControlGroup(controlGroup);
        log.info("testGroup size: {}, controlGroup size: {}", testGroup.size(), controlGroup.size());
        return correlationReport;
    }

    private double calculateCorrelationForReport(CorrelationReport correlationReport, int index) {
        final CorrelationCoefficientRequest request = buildRequest(
                correlationReport.testTimeSeries,
                Collections.singletonList(correlationReport.controlTimeSeries),
                this.metricsParams.domainId + "_" + index
        );
        final double correlation = calculateCorrelationCoefficient(request);
        log.debug("Calculated correlation for index {}: {}. Request: {}", index, correlation, JSONUtil.toJsonStr(request));
        return correlation;
    }

    private CorrelationReport updateHighestCorrelationIfNeeded(CorrelationReport currentReport,
                                                              CorrelationReport highestReport,
                                                              int loopIndex) {
        double correlation = currentReport.getCorrelation();

        if (correlation > highestReport.getCorrelation()) {
            log.info("highestCorrelation score updated: [score: {}, index: {}], previous: [score: {}, index: {}]",
                    correlation, loopIndex, highestReport.getCorrelation(), highestReport.getIndex());
            return currentReport;
        } else {
            log.debug("correlation is not higher than highest correlation, skip");
            return highestReport;
        }
    }

    public String getInnerSql() {
        String innerSql = "";
        UrlFilterParam urlFilter = metricsParams.getUrlFilter();
        final String filterType = urlFilter.getFilterType();
        final String searchInput = urlFilter.searchInput;
        final Integer metricsType = metricsParams.getMetricsType();
        final String relIds = metricsParams.getRelIds();
        final Integer domainId = metricsParams.getDomainId();
        switch (filterType) {
            case "Urls":
                if (metricsType == 1) {
                    innerSql = String.format(SplitTestHelper.queryGSCInnerSql, StringUtils.isBlank(relIds) ? domainId : domainId + " AND rel_id in (" + relIds + ")", prePeriodStartDate, prePeriodEndDate);
                } else {
                    innerSql = String.format(SplitTestHelper.queryGAInnerSql, SplitTestHelper.getTable(metricsParams.dataSourceType, metricsParams.googleAnalyticsVersion), domainId, prePeriodStartDate, prePeriodEndDate);
                }
                innerSql = innerSql + "AND url_murmur_hash IN (" + searchInput + ")";
                break;
            case "Sql":
                if (metricsType == 1) {
                    final Matcher matcher = SplitTestHelper.GSC_SQL_PATTERN.matcher(searchInput);
                    if (!matcher.find()) {
                        log.error("filterSqlCondition: {} not match GSC pattern", searchInput);
                    }
                    final String sqlCondition = matcher.group(1);
                    innerSql = "select trim(decodeURLComponent(url)) as url, log_date, clicks as entrances " + sqlCondition;
                } else {
                    final Matcher matcher = SplitTestHelper.GA_SQL_PATTERN.matcher(searchInput);
                    if (!matcher.find()) {
                        log.error("filterSqlCondition: {} not match GA pattern", searchInput);
                    }
                    innerSql = SplitTestHelper.GA_INNER_SELECT_SQL + matcher.group(1) + matcher.group(2);
                }
                break;
        }
        return innerSql;
    }

    @SneakyThrows
    private String getRetrieveMetricsSql(String innerSql) {
        int days = SplitTestHelper.DEFAULT_OFF_DAYS;
        while (true) {
            final String sql = String.format(SplitTestHelper.QUERY_SQL, innerSql, days, innerSql);
            final String countSql = "SELECT count(*) FROM (" + sql + ") t";
//            final String queryUrl = SplitTestHelper.DB_QUERY_URL + URLEncoder.encode(countSql, "UTF-8");
            final int count = this.splitTestCentralDAO.retrieveCount(countSql);
            if (days == SplitTestHelper.MIN_DAYS) {
                log.warn("days already reach MIN_DAYS: {}, count = {}", days, count);
                if (count < 2) {
                    log.warn("count: {} < 2", count);
                }
                // return sql no matter count is less than MIN_URLS_SIZE when days == MIN_DAYS
                return String.format(SplitTestHelper.QUERY_SQL, innerSql, 0, innerSql);
            }
            // return sql when count >= MIN_URLS_SIZE
            if (count >= SplitTestHelper.MIN_URLS_SIZE) {
                log.info("count >= MIN_URLS_SIZE: {}, days = {}", count, days);
                return sql;
            }
            log.info("count: {}, days: {}", count, days);
            days--;
        }
    }

    private SummaryMetricsResult summaryMetricsByDate(List<UrlMetrics> metrics) {
        final SummaryMetricsResult summaryMetricsResult = new SummaryMetricsResult();
        // create a list of metrics with all dates from period to post period
        double sum = 0.0;
        double maxSum = 0.0;
        double secondMaxSum = 0.0;
        UrlMetrics maxUrlMetrics = new UrlMetrics();
        int maxUrlMetricsIndex = 0;
        for (int i = 0; i < metrics.size(); i++) {
            // sum all metric every day group by date
            UrlMetrics metric = metrics.get(i);
            final double metricSum = metric.metrics.values()
                    .stream().reduce(Double::sum)
                    .orElse(0.0);
            sum += metricSum;
            metric.setSumClicks(metricSum);
            if (metricSum > maxSum) {
                secondMaxSum = maxSum;
                maxSum = metricSum;
                maxUrlMetrics = metric;
                maxUrlMetricsIndex = i;
            } else if (metricSum > secondMaxSum) {
                secondMaxSum = metricSum;
            }
        }
        double avg = sum / metrics.size();
        // remove a extreme large sum value to reduce the noise if only have a sum is triple larger than avg
        // if secondMaxSum is larger than 3 times of avg means the max sum is not too large
        if (maxSum > avg * 3 && secondMaxSum < avg * 3) {
            final String url = maxUrlMetrics.url;
            log.warn("remove a extreme large sum value to reduce the noise, sum: {}, url: {}, maxSum: {}, avg: {}", sum, url, maxSum, avg);
            summaryMetricsResult.setExtremeUrl(url);
            metrics.remove(maxUrlMetricsIndex);
        }

//        final int periodDates = SplitTestHelper.DEFAULT_OFF_DAYS * 2 + 2;
        final int periodDates = (int) ChronoUnit.DAYS.between(prePeriodStartDate, prePeriodEndDate) + 1;
        double[] summary = new double[periodDates];
        for (int i = 0; i < periodDates; i++) {
            final LocalDate localDate = prePeriodStartDate.plusDays(i);
            double metric = 0.0;
            // sum all metrics for this date
            for (final UrlMetrics urlMetrics : metrics) {
                metric += urlMetrics.getMetrics().getOrDefault(localDate, 0.0);
            }
            summary[i] = metric;
        }
        summaryMetricsResult.setMetrics(summary);
        return summaryMetricsResult;
    }

    private Double calculateCorrelationCoefficient(CorrelationCoefficientRequest request) {
        final String requestJson = JSONUtil.toJsonStr(request);
        final HttpPost httpPost = new HttpPost(SplitTestHelper.endpoint);
        httpPost.setEntity(new StringEntity(requestJson, "utf-8"));
//        log.info("calculateCorrelationCoefficient request: {}", requestJson);
        // retry 3 times
        Double score = 0.0;
        for (int i = 0; i < 3; i++) {
            try (CloseableHttpResponse httpResponse = SplitTestHelper.httpClient.execute(httpPost)) {
                if (httpResponse.getStatusLine().getStatusCode() != 200) {
                    log.error("calculateCorrelationCoefficient error: {}, status: {}, retry: {}", httpResponse.getStatusLine().getReasonPhrase(), httpResponse.getStatusLine().getStatusCode(), i);
                    continue;
                }
                try {
                    final HttpEntity responseEntity = httpResponse.getEntity();
                    final String responseContent = EntityUtils.toString(responseEntity);
                    EntityUtils.consumeQuietly(responseEntity);
                    final CorrelationCoefficientResponse correlationCoefficientResponse = JSONUtil.toBean(responseContent, CorrelationCoefficientResponse.class);
                    final Map<String, Double> controlNameCorrCoefMap = correlationCoefficientResponse.getControl_name_corr_coef_map();
                    score = controlNameCorrCoefMap.values().iterator().next();
                } catch (IOException | ParseException e) {
                    log.error("calculateCorrelationCoefficient error: {}", e.getMessage());
                }
            } catch (IOException e) {
                log.error("calculateCorrelationCoefficient error: {}", e.getMessage());
            }
        }
        return score;
    }

    private CorrelationCoefficientRequest buildRequest(double[] testTimeSeries, List<double[]> controlTimeSeriesList, String controlName) {
        return CorrelationCoefficientRequest.builder()
                .access_token(SplitTestHelper.accessToken)
                .test_time_series(testTimeSeries)
                .control_name_list(Collections.singletonList(controlName + "_" + System.currentTimeMillis()))
                .control_time_series_list(controlTimeSeriesList)
                .pre_period_start_date(prePeriodStartDate.toString())
                .pre_period_end_date(prePeriodEndDate.toString())
                .post_period_start_date(postPeriodStartDate.toString())
                .post_period_end_date(postPeriodEndDate.toString())
                .build();
    }

    @Data
    public static class MetricsParams {
        private boolean useShuffleSplit;
        private Integer domainId;
        private Integer dataSourceType;
        private boolean enableParentChildRel;
        private String relIds;
        private UrlFilterParam urlFilter;
        private Integer metricsType;
        private Integer loopCount;
        @DateTimeFormat(pattern = "yyyy-MM-dd")
        private LocalDate endDate;
        private Integer googleAnalyticsVersion;
    }

    @Data
    public static class UrlFilterParam {
        private String filterType; // PageTags, Pages, ContentTypes, Urls, Sql
        private String actionType;
        private String searchInput;
    }

    @Data
    public static class UrlMetrics {
        private String url;
        private String logDateMetrics;
        private int sumTraffic;
        private double sumClicks;
        // store metric every day
        private Map<LocalDate, Double> metrics;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class CorrelationCoefficientRequest {
        private final int version = 3;
        private String access_token;
        private double[] test_time_series;
        private List<String> control_name_list;
        private List<double[]> control_time_series_list;
        private String pre_period_start_date;
        private String pre_period_end_date;
        private String post_period_start_date;
        private String post_period_end_date;

    }

    @Data
    private static class CorrelationCoefficientResponse {
        private Boolean success;
        private Map<String, Double> control_name_corr_coef_map; // correlation coefficient by control name

    }

    @Data
    private static class SummaryMetricsResult {
        private double[] metrics;
        private String extremeUrl;
    }

    @Data
    public class CorrelationReport {

        private int index;
        private LocalDateTime time;
        private double correlation;
        private double averageCorrelation;
        private List<UrlMetrics> testGroup = new ArrayList<>();
        private double[] testTimeSeries;
        private String extremeTestUrl;
        private List<UrlMetrics> controlGroup = new ArrayList<>();
        private double[] controlTimeSeries;
        private String extremeControlUrl;
        private Set<String> randomAllocateTestUrls;
        private Set<String> randomAllocateControlUrls;

        public String summary() {
            return "index: " + index + ", time: " + time
                    + ", highestCorrelation: " + correlation
                    + ", averageCorrelation: " + averageCorrelation
                    + ", testGroupSize: " + testGroup.size()
                    + ", controlGroupSize: " + controlGroup.size()
                    + ", extremeTestUrl: " + extremeTestUrl
                    + ", extremeControlUrl: " + extremeControlUrl;
        }

        public void setTestGroup(List<UrlMetrics> testGroup) {
            this.testGroup = testGroup;
            final SummaryMetricsResult summaryMetricsResult = summaryMetricsByDate(testGroup);
            this.testTimeSeries = summaryMetricsResult.getMetrics();
            if (summaryMetricsResult.extremeUrl != null) {
                this.extremeTestUrl = summaryMetricsResult.extremeUrl;
            }
        }

        public void setControlGroup(List<UrlMetrics> controlGroup) {
            this.controlGroup = controlGroup;
            final SummaryMetricsResult summaryMetricsResult = summaryMetricsByDate(this.controlGroup);
            this.controlTimeSeries = summaryMetricsResult.getMetrics();
            if (summaryMetricsResult.extremeUrl != null) {
                this.extremeControlUrl = summaryMetricsResult.extremeUrl;
            }
        }

        public String report() {
            return "index: " + index + ", time: " + time
                    + ", highestCorrelation: " + correlation
                    + ", averageCorrelation: " + averageCorrelation
                    + ", testGroupSize: " + testGroup.size()
                    + ", controlGroupSize: " + controlGroup.size()
                    + ", extremeTestUrl: " + extremeTestUrl
                    + ", extremeControlUrl: " + extremeControlUrl
                    + ", testGroupUrls: " + testGroup.stream().map(UrlMetrics::getUrl).filter(url -> !url.equals(extremeTestUrl)).collect(Collectors.toList())
                    + ", controlGroupUrls: " + controlGroup.stream().map(UrlMetrics::getUrl).filter(url -> !url.equals(extremeControlUrl)).collect(Collectors.toList());
        }
    }
}
