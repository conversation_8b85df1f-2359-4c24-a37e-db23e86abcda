package seoclarity.backend.clarity360;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.ArrayUtils;

import seoclarity.backend.dao.clickhouse.internallink.InternalLinkNewClusterServer5Dao;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSiteCrawlDoc1Dao;
import seoclarity.backend.entity.clickhouse.internallink.InternalLinkClarityDBEntity;
import seoclarity.backend.entity.clickhouse.prod.DisSiteCrawlDoc1Entity;
import seoclarity.backend.utils.ClarityDBUtils;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;


// mvn exec:java -Dexec.mainClass="seoclarity.backend.clarity360.InternalLinkFixFromSiteAudit" -Dexec.args=""
public class InternalLinkFixFromSiteAudit {
	
	private InternalLinkNewClusterServer5Dao internalLinkNewClusterServer5Dao;
	private DisSiteCrawlDoc1Dao disSiteCrawlDoc1Dao;
	
	private InternalLinkFixFromSiteAudit() {
		internalLinkNewClusterServer5Dao = SpringBeanFactory.getBean("internalLinkNewClusterServer5Dao");
		disSiteCrawlDoc1Dao = SpringBeanFactory.getBean("disSiteCrawlDoc1Dao");
	}
	private static Integer BACKPROCESS_CRAWL_ID = 0;
	private static Integer BACKPROCESS_DOMAIN_ID = 0;
	
	private static List<Integer[]> fixingList = new ArrayList<Integer[]>();
	
	static {
		fixingList.add(new Integer[] {});




	}
	
	public static void main(String[] args) {
		
		if(args != null && args.length >= 2) {
			BACKPROCESS_CRAWL_ID = NumberUtils.toInt(args[0]);
			BACKPROCESS_DOMAIN_ID = NumberUtils.toInt(args[1]);
			
		}
		
		InternalLinkFixFromSiteAudit InternalLinkFixFromSiteAudit = new InternalLinkFixFromSiteAudit();
//		InternalLinkFixFromSiteAudit.process();
		
		for(Integer[] array : fixingList) {
			BACKPROCESS_CRAWL_ID = array[0];
			BACKPROCESS_DOMAIN_ID = array[1];
			InternalLinkFixFromSiteAudit.process();
		}
	}
	
	
	private void process() {
		
		List<InternalLinkClarityDBEntity> uploadList = new ArrayList<InternalLinkClarityDBEntity>();
		
		Long totalCount = disSiteCrawlDoc1Dao.getCountOfSiteHealthPageLinkV2(BACKPROCESS_DOMAIN_ID, BACKPROCESS_CRAWL_ID);
		Long pageNum = totalCount / 400000;
		
		if (totalCount > 0 && pageNum == 0) {
			pageNum = 1L;
		}
		
		InternalLinkClarityDBEntity internalLinkClarityDBEntity;
		String destUrl = "";
		Integer total = 0;
		
		for(int i = 0; i < pageNum; i++) {
			int num = 0;
			List<DisSiteCrawlDoc1Entity> missingResultList = internalLinkNewClusterServer5Dao.findMissingDataByCrawlRequestId(BACKPROCESS_DOMAIN_ID, BACKPROCESS_CRAWL_ID, pageNum, i);
			System.out.println("missingResultList size :" + missingResultList.size());
			System.out.println("==== processing on part:" + i);
			total += missingResultList.size();
			
			for(DisSiteCrawlDoc1Entity disSiteCrawlDoc1Entity : missingResultList) {
				
				num ++;
				if(num % 500 == 0){
					System.out.println("processing :" + num);
					
//					System.out.println(new Gson().toJson(disSiteCrawlDoc1Entity));
				}
				
				String sourcedomain = FormatUtils.getDomainByUrl(disSiteCrawlDoc1Entity.getUrl());
		        String sourcerootDomain = ClarityDBUtils.getRootDomain(sourcedomain);
		        String sourceUrlFolderLevel1 = "";
		        String sourceUrlFolderLevel2 = "";
		        
		        try {
					String[] domainUrlList = CommonUtils.splitString(disSiteCrawlDoc1Entity.getUrl());
					if (domainUrlList != null && domainUrlList.length >= 3) {
						HashMap<String, String> urlFolder = getDirectory(domainUrlList[1]);
						
						sourceUrlFolderLevel1 = (urlFolder.get("firstDepth") == null ? "": urlFolder.get("firstDepth"));
						sourceUrlFolderLevel2 = (urlFolder.get("secondDepth") == null ? "": urlFolder.get("secondDepth"));
						
					} else {
						System.out.println("invalid url : " + disSiteCrawlDoc1Entity.getUrl() + " found !!!");
					}
				} catch (Exception e) {
					e.printStackTrace();
				}
				
		        
				if (ArrayUtils.isEmpty(disSiteCrawlDoc1Entity.getPageLinkDestinationUrl()) || ArrayUtils.isEmpty(disSiteCrawlDoc1Entity.getPageLinkDestinationAnchortext())
						|| disSiteCrawlDoc1Entity.getPageLinkDestinationUrl().length == disSiteCrawlDoc1Entity.getPageLinkDestinationAnchortext().length) {
					
					if (disSiteCrawlDoc1Entity.getPageLinkDestinationUrl() != null && ArrayUtils.isNotEmpty(disSiteCrawlDoc1Entity.getPageLinkDestinationUrl())) {
						 
						for(int pageLinkSize = 0; pageLinkSize < disSiteCrawlDoc1Entity.getPageLinkDestinationUrl().length; pageLinkSize ++) {
							internalLinkClarityDBEntity = new InternalLinkClarityDBEntity();
							
							
							internalLinkClarityDBEntity.setAnalyzed_url_flg_s(getBooleanFromString(disSiteCrawlDoc1Entity.getAnalyzed_url_flg_s()));
							internalLinkClarityDBEntity.setAnalyzed_url_s(disSiteCrawlDoc1Entity.getAnalyzed_url_s());
							internalLinkClarityDBEntity.setAnchor_text(disSiteCrawlDoc1Entity.getPageLinkDestinationAnchortext()[pageLinkSize]);
							internalLinkClarityDBEntity.setCanonical(disSiteCrawlDoc1Entity.getCanonical());
							internalLinkClarityDBEntity.setCanonical_flg(getBooleanFromString(disSiteCrawlDoc1Entity.getCanonical_flg()));
//							internalLinkClarityDBEntity.setCanonical_string(disSiteCrawlDoc1Entity.getCanonical_string());
							internalLinkClarityDBEntity.setCanonical_type(disSiteCrawlDoc1Entity.getCanonical_type());
//							internalLinkClarityDBEntity.setCrawl_date_long(disSiteCrawlDoc1Entity.getCrawlRequestDate());
							internalLinkClarityDBEntity.setCrawl_request_log_id_i(disSiteCrawlDoc1Entity.getCrawlRequestId());
							
							
							destUrl = disSiteCrawlDoc1Entity.getPageLinkDestinationUrl()[pageLinkSize];
							if (StringUtils.isBlank(destUrl)) {
								continue;
							} else if (!StringUtils.startsWithIgnoreCase(destUrl, "http://") 
									&& !StringUtils.startsWithIgnoreCase(destUrl, "https://")
									&& StringUtils.startsWithIgnoreCase(destUrl, "/")) {
								
								destUrl = getPotocolAndHostname(disSiteCrawlDoc1Entity.getUrl(), destUrl);
							}
							
							String destdomain = FormatUtils.getDomainByUrl(destUrl);
					        String destrootDomain = ClarityDBUtils.getRootDomain(destdomain);
							
					        internalLinkClarityDBEntity.setDestination_domain(destdomain);
					        internalLinkClarityDBEntity.setDestination_root_domain(destrootDomain);
					        
							try {
								String[] domainUrlList = CommonUtils.splitString(destUrl);
								if (domainUrlList != null && domainUrlList.length >= 3) {
									HashMap<String, String> urlFolder = getDirectory(domainUrlList[1]);
									
							        internalLinkClarityDBEntity.setDestination_folder_level_1(urlFolder.get("firstDepth") == null ? "": urlFolder.get("firstDepth"));
							        internalLinkClarityDBEntity.setDestination_folder_level_2(urlFolder.get("secondDepth") == null ? "": urlFolder.get("secondDepth"));
									
								} else {
									System.out.println("invalid url : " + destUrl + " found !!!");
								}
							} catch (Exception e) {
								e.printStackTrace();
							}
							
							internalLinkClarityDBEntity.setDestination_url(destUrl);
					        
							internalLinkClarityDBEntity.setDomain_id_i(disSiteCrawlDoc1Entity.getDomainId());
							internalLinkClarityDBEntity.setLink_nofollow(getBooleanFromString(disSiteCrawlDoc1Entity.getFollow_flg()));
							internalLinkClarityDBEntity.setPage_robots_meta_archive(getBooleanFromString(disSiteCrawlDoc1Entity.getArchive_flg()) ? "noarchive" : "archive");
							internalLinkClarityDBEntity.setPage_robots_meta_follow(getBooleanFromString(disSiteCrawlDoc1Entity.getFollow_flg()) ? "nofollow" : "follow");
							internalLinkClarityDBEntity.setPage_robots_meta_index(getBooleanFromString(disSiteCrawlDoc1Entity.getIndex_flg()) ? "noindex" : "index");
							
							internalLinkClarityDBEntity.setPopularity(disSiteCrawlDoc1Entity.getCrawl_depth());
							

					        internalLinkClarityDBEntity.setSource_url(disSiteCrawlDoc1Entity.getUrl());
							
					        internalLinkClarityDBEntity.setSource_domain(sourcedomain);
					        internalLinkClarityDBEntity.setSource_root_domain(sourcerootDomain);
					        
					        internalLinkClarityDBEntity.setSource_folder_level_1(sourceUrlFolderLevel1);
					        internalLinkClarityDBEntity.setSource_folder_level_2(sourceUrlFolderLevel2);
							
							internalLinkClarityDBEntity.setSource_url_response_code(disSiteCrawlDoc1Entity.getStatus());
							internalLinkClarityDBEntity.setTitle_flg(getBooleanFromString(disSiteCrawlDoc1Entity.getTitle_flg()));
							
							internalLinkClarityDBEntity.setToday(FormatUtils.formatDate(new Date(), "yyyy-MM-dd"));
							
							internalLinkClarityDBEntity.setTitle(disSiteCrawlDoc1Entity.getTitle());
							uploadList.add(internalLinkClarityDBEntity);
						}
						
					} else {
						
						System.out.println("No pagelink, url:" + disSiteCrawlDoc1Entity.getUrl() + ", status:" + disSiteCrawlDoc1Entity.getStatus());
						internalLinkClarityDBEntity = new InternalLinkClarityDBEntity();
						internalLinkClarityDBEntity.setAnalyzed_url_flg_s(getBooleanFromString(disSiteCrawlDoc1Entity.getAnalyzed_url_flg_s()));
						internalLinkClarityDBEntity.setAnalyzed_url_s(disSiteCrawlDoc1Entity.getAnalyzed_url_s());
						internalLinkClarityDBEntity.setAnchor_text("");
						internalLinkClarityDBEntity.setCanonical(disSiteCrawlDoc1Entity.getCanonical());
						internalLinkClarityDBEntity.setCanonical_flg(getBooleanFromString(disSiteCrawlDoc1Entity.getCanonical_flg()));
//						internalLinkClarityDBEntity.setCanonical_string(disSiteCrawlDoc1Entity.getCanonical_string());
						internalLinkClarityDBEntity.setCanonical_type(disSiteCrawlDoc1Entity.getCanonical_type());
//						internalLinkClarityDBEntity.setCrawl_date_long(disSiteCrawlDoc1Entity.getCrawlRequestDate());
						internalLinkClarityDBEntity.setCrawl_request_log_id_i(disSiteCrawlDoc1Entity.getCrawlRequestId());
						
				        internalLinkClarityDBEntity.setDestination_domain("");
				        internalLinkClarityDBEntity.setDestination_root_domain("");
				        internalLinkClarityDBEntity.setDestination_folder_level_1("");
				        internalLinkClarityDBEntity.setDestination_folder_level_2("");
						
						internalLinkClarityDBEntity.setDestination_url("");
				        
						internalLinkClarityDBEntity.setDomain_id_i(disSiteCrawlDoc1Entity.getDomainId());
						internalLinkClarityDBEntity.setLink_nofollow(getBooleanFromString(disSiteCrawlDoc1Entity.getFollow_flg()));
						internalLinkClarityDBEntity.setPage_robots_meta_archive(getBooleanFromString(disSiteCrawlDoc1Entity.getArchive_flg()) ? "noarchive" : "archive");
						internalLinkClarityDBEntity.setPage_robots_meta_follow(getBooleanFromString(disSiteCrawlDoc1Entity.getFollow_flg()) ? "nofollow" : "follow");
						internalLinkClarityDBEntity.setPage_robots_meta_index(getBooleanFromString(disSiteCrawlDoc1Entity.getIndex_flg()) ? "noindex" : "index");
						
						internalLinkClarityDBEntity.setPopularity(disSiteCrawlDoc1Entity.getCrawl_depth());

				        internalLinkClarityDBEntity.setSource_url(disSiteCrawlDoc1Entity.getUrl());
						
				        internalLinkClarityDBEntity.setSource_folder_level_1(sourceUrlFolderLevel1);
				        internalLinkClarityDBEntity.setSource_folder_level_2(sourceUrlFolderLevel2);
						
						internalLinkClarityDBEntity.setSource_url_response_code(disSiteCrawlDoc1Entity.getStatus());
						internalLinkClarityDBEntity.setTitle_flg(getBooleanFromString(disSiteCrawlDoc1Entity.getTitle_flg()));
						
						internalLinkClarityDBEntity.setToday(FormatUtils.formatDate(new Date(), "yyyy-MM-dd"));
						
						internalLinkClarityDBEntity.setTitle(disSiteCrawlDoc1Entity.getTitle());
						uploadList.add(internalLinkClarityDBEntity);
					}
					
				} else {
					System.out.println("skip:" + disSiteCrawlDoc1Entity.getUrl());
				}
				
				
			}
			try {
				System.out.println("Load size :" + uploadList.size());
				internalLinkNewClusterServer5Dao.insertForBatch(uploadList, "scott_test_20240331");
				
			} catch (java.lang.Exception e) {
				// TODO: handle exception
					try {
						Thread.sleep(10 * 1000);
					} catch (InterruptedException e1) {
						// TODO Auto-generated catch block
						e1.printStackTrace();
					}
			}
			uploadList.clear();
			
			try {
				Thread.sleep(10 * 1000);
			} catch (InterruptedException e) {
				// TODO Auto-generated catch block
				e.printStackTrace();
			}
			
		}
		System.out.println("total:" + total);
		
	}

	
	private static Boolean getBooleanFromString(String inputText) {
		
		if (StringUtils.isNotBlank(inputText) && 
				(StringUtils.equalsIgnoreCase(inputText, "yes") || StringUtils.equalsIgnoreCase(inputText, "y"))) {
			return true;
		}
		return false;
	}
	

	
	
	private static String getPotocolAndHostname(String url, String destUri) {
		String uri = "";
		String portocol = "";
		if (StringUtils.startsWithIgnoreCase(url, "https")) {
			uri = StringUtils.removeStartIgnoreCase(url, "https://");
			portocol = "https";
		} else if (StringUtils.startsWithIgnoreCase(url, "http")) {
			uri = StringUtils.removeStartIgnoreCase(url, "http://");
			portocol = "http";
		} else {
			uri = StringUtils.substringAfterLast(url, "http");
			uri = StringUtils.remove(url, "://");
			portocol = "https";
		}
		
		String domainName = FormatUtils.getDomainByUrl(url);
		
		if (StringUtils.startsWith(destUri, "/")) {
			uri = portocol + "://" + domainName + destUri;
		} else {
			uri = portocol + "://" + domainName + "/" + destUri;
		}
		
		System.out.println("==== Trf " + destUri + " ==> " + uri);
		return uri;
	}
	
	
	private static HashMap<String, String> getDirectory(String uri) {
		uri = StringUtils.substringBeforeLast(uri, "/");

		String[] uriArraries = uri.replaceFirst("/", "").split("/");

		String firstDepth = "";
		String secondDepth = "";
		String thirdDepth = "";
		HashMap<String, String> top3Directories = new HashMap<String, String>();

		if (uriArraries != null) {
			for (int i = 0; i < uriArraries.length; i++) {
				String directory = uriArraries[i];
				if (i == 0) {
					firstDepth = directory;
				} else if (i == 1) {
					secondDepth = firstDepth + "/" + directory;
				} else if (i == 2) {
					thirdDepth = secondDepth + "/" + directory;
				}
			}
		}

		if (StringUtils.isNotBlank(firstDepth)) {
			top3Directories.put("firstDepth", firstDepth);
		}
		if (StringUtils.isNotBlank(secondDepth)) {
			top3Directories.put("secondDepth", secondDepth);
		}
		if (StringUtils.isNotBlank(thirdDepth)) {
			top3Directories.put("thirdDepth", thirdDepth);
		}

		return top3Directories;
	}
	
	
	
	
}
