package seoclarity.backend.clarity360;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import com.google.gson.Gson;

import ru.yandex.clickhouse.ClickHouseArray;
import seoclarity.backend.entity.clickhouse.prod.DisSiteCrawlDoc1Entity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.utils.HttpRequestUtils;
import seoclarity.backend.vectordb.EmbeddingEntity;
import seoclarity.backend.vectordb.EmbeddingResponseVO;
import seoclarity.backend.vectordb.ParamVO;

public class Clarity360EmbeddingCrawlerCommand extends BaseThreadCommand {
	
	private String ip;
	private DisSiteCrawlDoc1Entity disSiteCrawlDoc1Entity;
//	private static String API_OPENAI_EMBEDDING = "https://api.openai.com/v1/embeddings";
	private static String API_OPENAI_EMBEDDING = "https://gateway.ai.cloudflare.com/v1/11698fe22e694ee13aa14f29ed067dfe/embeddings";
	private static Map<String, String> OPENAI_DEFAULT_HEADER = new HashMap<String, String>();
	
	//change the model to "text-embedding-3-small"
//	private static String OPENAI_MODEL = "text-embedding-ada-002";
	private static String OPENAI_MODEL = "text-embedding-3-small";
	
	public Clarity360EmbeddingCrawlerCommand(String ipAddress, DisSiteCrawlDoc1Entity disSiteCrawlDoc1Entity) {
		this.ip = ipAddress;
		this.disSiteCrawlDoc1Entity = disSiteCrawlDoc1Entity;
	}
	
	static {
		OPENAI_DEFAULT_HEADER.put("Content-Type", "application/json");
		OPENAI_DEFAULT_HEADER.put("Authorization", "Bearer ***************************************************");
	}
	
	@Override
	protected void execute() throws Exception {
		long a = System.currentTimeMillis();
		
		EmbeddingEntity embeddingEntity = processByUrl();
		
		if (embeddingEntity != null) {
			CacheModleFactory.getInstance().setEmbeddingMap(ip, embeddingEntity);
		}
		
		long b = System.currentTimeMillis();
		System.out.println("EndIP:" + ip + " time:" + (b - a) * 1.0 / 1000 + "s");
		CacheModleFactory.getInstance().setAliveIpAddress(ip); 
	}
	
	private static Gson gson = new Gson();
	
	private EmbeddingEntity processByUrl() throws SQLException {
		
		if (disSiteCrawlDoc1Entity == null) {
			return null;
		}
		
		String h1 = getNotNullStringFromArray(disSiteCrawlDoc1Entity.getH1());
		String h2 = getNotNullStringFromArray(disSiteCrawlDoc1Entity.getH2());
		String title = disSiteCrawlDoc1Entity.getTitle();
		String meta = disSiteCrawlDoc1Entity.getDescription();
		ClickHouseArray customDataArray = disSiteCrawlDoc1Entity.getContent();
		String fullText = title + "/" + meta + "/" + h1 + "/" + h2;
		String customData1 = "";
		String customData2 = "";
		String customData3 = "";
		String customData4 = "";
		String customData5 = "";
		
		List<Integer> keyList = new ArrayList<>();
		List<String> valueList = new ArrayList<>();

		if (StringUtils.isNotBlank(title)) {
			keyList.add(1);
			valueList.add(title);
		}
		
		if (StringUtils.isNotBlank(meta)) {
			keyList.add(2);
			valueList.add(meta);
		}
		
		if (StringUtils.isNotBlank(h1)) {
			keyList.add(3);//row number
			valueList.add(h1);
		}
		
		if (StringUtils.isNotBlank(h2)) {
			keyList.add(4);
			valueList.add(h2);
		}
		
		keyList.add(5);
		valueList.add(fullText);
		
		int customNum = 1;
		for(String customData : (String[])customDataArray.getArray()) {
			if (StringUtils.isNotBlank(customData) && !StringUtils.equals(customData, "[]")) {
				String customDataValue = removeHtmlTag(customData);
				keyList.add(5 + customNum);// customData from row 20-24
				valueList.add(customDataValue);
				if (customNum == 1) {
					customData1 = customDataValue;
				} else if (customNum == 2) {
					customData2 = customDataValue;
				} else if (customNum == 3) {
					customData3 = customDataValue;
				} else if (customNum == 4) {
					customData4 = customDataValue;
				} else if (customNum == 5) {
					customData5 = customDataValue;
				}
			}
			
			customNum ++;
			if (customNum >= 5) {
				break;
			}
		}
		
		ParamVO paramVO = new ParamVO();
		
		paramVO.setInput(valueList.toArray(new String[valueList.size()]));
		paramVO.setModel(OPENAI_MODEL);
		
//		String dataJson = \
		
		if (keyList.size() == 0) {
			System.out.println("========= key list is empty!");
			System.out.println("Url:" + disSiteCrawlDoc1Entity.getUrl());
			return null;
		}
		
		Map<String, String> response = HttpRequestUtils.queryWebServiceFunctionPostMap(API_OPENAI_EMBEDDING, new Gson().toJson(paramVO), null, OPENAI_DEFAULT_HEADER);
		if (response!= null) {
			String responseText = response.get("response");
			
			System.out.println(responseText);
			
			EmbeddingResponseVO embeddingResponseVO = new Gson().fromJson(responseText, EmbeddingResponseVO.class);
			EmbeddingEntity entity = new EmbeddingEntity();
			
			entity.setUrl(disSiteCrawlDoc1Entity.getUrl());
			entity.setCrawlRequestDate(disSiteCrawlDoc1Entity.getCrawlRequestDate() + "");
			entity.setCrawlRequestId(disSiteCrawlDoc1Entity.getCrawlRequestId());
			entity.setDomainId(disSiteCrawlDoc1Entity.getDomainId());
			entity.setTitle(title);
			entity.setH1(h1);
			entity.setH2(h2);
			entity.setMeta(meta);
			entity.setFullText(fullText);
			entity.setCustomData1(customData1);
			entity.setCustomData2(customData2);
			entity.setCustomData3(customData3);
			entity.setCustomData4(customData4);
			entity.setCustomData5(customData5);
			
			//------------------------------------------------
			//this may need to update when row column number changes
			//------------------------------------------------
			
			if (embeddingResponseVO == null || embeddingResponseVO.getData() == null || keyList.size() != embeddingResponseVO.getData().length) {
				System.out.println("========== key and value size can not match");
				System.out.println(gson.toJson(keyList));
				System.out.println(gson.toJson(valueList));
//				return "";
			}
			
			for(int i = 0; i < keyList.size(); i ++) {
				Float[] embedding = null; 
				try {
					embedding = embeddingResponseVO.getData()[i].getEmbedding();
				} catch (Exception e) {
					e.printStackTrace();
					continue;
				}
				
				if (i == 0) {
					entity.setTitleEmbedding(embedding);
				} else if (i == 1) {
					entity.setMetaEmbedding(embedding);
				} else if (i == 2) {
					entity.setH1Embedding(embedding);
				} else if (i == 3) {
					entity.setH2Embedding(embedding);
				} else if (i == 4) {
					entity.setFullTextEmbedding(embedding);
				} else if (i == 5) {
					entity.setCustomData1Embedding(embedding);
				} else if (i == 6) {
					entity.setCustomData2Embedding(embedding);
				} else if (i == 7) {
					entity.setCustomData3Embedding(embedding);
				} else if (i == 8) {
					entity.setCustomData4Embedding(embedding);
				} else if (i == 9) {
					entity.setCustomData5Embedding(embedding);
				}
				
			}
			
			return entity;
		} else {
			System.out.println("Resp is empty!");
			return null;
		}
		
	}
	

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub
		
	}
	
	private static String removeHtmlTag(String inputStr) {
		String outputStr = "";
		try {
			outputStr = StringUtils.trim(StringUtils.replace(inputStr, "\\&[a-zA-Z]{1,10};", "").replaceAll("<[^>]*>", "").replaceAll("[(/>)<]", ""));
			outputStr = StringUtils.removeEnd(StringUtils.removeStart(StringUtils.removeEnd(StringUtils.removeStart(outputStr, "["), "]"), "\""), "\"");
			outputStr = StringUtils.replace(outputStr, "\\n", "");
			outputStr = StringUtils.replace(outputStr, "\n", "");
			outputStr = StringUtils.replace(outputStr, "\t", "");
			return outputStr;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return inputStr;
	}
	
	private String getNotNullStringFromArray(ClickHouseArray array) throws SQLException {
		if (array == null) {
			return null;
		}
		
		for(String str : (String[])array.getArray()) {
			if (StringUtils.isNotBlank(str)) {
				return str;
			}
		}
		return null;
	}

	
}