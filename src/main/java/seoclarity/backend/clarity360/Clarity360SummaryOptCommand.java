package seoclarity.backend.clarity360;

import java.util.Date;

import org.apache.commons.lang.StringUtils;

import seoclarity.backend.dao.actonia.Clarity360ReportStageEntityDAO;
import seoclarity.backend.dao.clickhouse.clarity360.lweb01.Clarity360Lweb01DAO;
import seoclarity.backend.dao.clickhouse.clarity360.lweb02.Clarity360Lweb02DAO;
import seoclarity.backend.dao.clickhouse.clarity360.lweb03.Clarity360Lweb03DAO;
import seoclarity.backend.dao.clickhouse.clarity360.lweb04.Clarity360Lweb04DAO;
import seoclarity.backend.dao.clickhouse.clarity360.lweb05.Clarity360Lweb05DAO;
import seoclarity.backend.dao.clickhouse.clarity360.merge01.Clarity360Merge01DAO;
import seoclarity.backend.dao.clickhouse.clarity360.merge02.Clarity360Merge02DAO;
import seoclarity.backend.dao.clickhouse.clarity360.merge03.Clarity360Merge03DAO;
import seoclarity.backend.dao.clickhouse.clarity360.merge04.Clarity360Merge04DAO;
import seoclarity.backend.dao.clickhouse.clarity360.merge05.Clarity360Merge05DAO;
import seoclarity.backend.entity.actonia.Clarity360ReportStageEntity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.utils.SpringBeanFactory;

public class Clarity360SummaryOptCommand extends BaseThreadCommand {
	
	private String ip;
//	private Keyword[] keywords;
	
	//360 cluster
	private Clarity360Lweb01DAO clarity360Lweb01DAO;
	private Clarity360Lweb02DAO clarity360Lweb02DAO;
	private Clarity360Lweb03DAO clarity360Lweb03DAO;
	private Clarity360Lweb04DAO clarity360Lweb04DAO;
	private Clarity360Lweb05DAO clarity360Lweb05DAO;
	
	private Clarity360Merge01DAO clarity360Merge01DAO;
	private Clarity360Merge02DAO clarity360Merge02DAO;
	private Clarity360Merge03DAO clarity360Merge03DAO;
	private Clarity360Merge04DAO clarity360Merge04DAO;
	private Clarity360Merge05DAO clarity360Merge05DAO;
	
	private Clarity360ReportStageEntityDAO clarity360ReportStageEntityDAO;
	
	private Integer serverId;
	private String localTableName;
	private String targetDate;
	private Integer reportId;
	
	public Clarity360SummaryOptCommand() {
		clarity360ReportStageEntityDAO = SpringBeanFactory.getBean("clarity360ReportStageEntityDAO");
	}
	
	public Clarity360SummaryOptCommand(String ip, 
			Clarity360Lweb01DAO clarity360Lweb01DAO, Integer serverId, String localTableName, String targetDate, Integer reportId) {
		this.ip = ip;
		this.clarity360Lweb01DAO = clarity360Lweb01DAO;
		this.serverId = serverId;
		this.localTableName = localTableName;
		this.targetDate = targetDate;
		this.reportId = reportId;
		clarity360ReportStageEntityDAO = SpringBeanFactory.getBean("clarity360ReportStageEntityDAO");
	}

	public Clarity360SummaryOptCommand(String ip, 
			Clarity360Lweb02DAO clarity360Lweb02DAO, Integer serverId, String localTableName, String targetDate, Integer reportId) {
		this.ip = ip;
		this.clarity360Lweb02DAO = clarity360Lweb02DAO;
		this.serverId = serverId;
		this.localTableName = localTableName;
		this.targetDate = targetDate;
		this.reportId = reportId;
		clarity360ReportStageEntityDAO = SpringBeanFactory.getBean("clarity360ReportStageEntityDAO");
	}
	
	public Clarity360SummaryOptCommand(String ip, 
			Clarity360Lweb03DAO clarity360Lweb03DAO, Integer serverId, String localTableName, String targetDate, Integer reportId) {
		this.ip = ip;
		this.clarity360Lweb03DAO = clarity360Lweb03DAO;
		this.serverId = serverId;
		this.localTableName = localTableName;
		this.targetDate = targetDate;
		this.reportId = reportId;
		clarity360ReportStageEntityDAO = SpringBeanFactory.getBean("clarity360ReportStageEntityDAO");
	}
	
	public Clarity360SummaryOptCommand(String ip, 
			Clarity360Lweb04DAO clarity360Lweb04DAO, Integer serverId, String localTableName, String targetDate, Integer reportId) {
		this.ip = ip;
		this.clarity360Lweb04DAO = clarity360Lweb04DAO;
		this.serverId = serverId;
		this.localTableName = localTableName;
		this.targetDate = targetDate;
		this.reportId = reportId;
		clarity360ReportStageEntityDAO = SpringBeanFactory.getBean("clarity360ReportStageEntityDAO");
	}
	
	public Clarity360SummaryOptCommand(String ip, 
			Clarity360Lweb05DAO clarity360Lweb05DAO, Integer serverId, String localTableName, String targetDate, Integer reportId) {
		this.ip = ip;
		this.clarity360Lweb05DAO = clarity360Lweb05DAO;
		this.serverId = serverId;
		this.localTableName = localTableName;
		this.targetDate = targetDate;
		this.reportId = reportId;
		clarity360ReportStageEntityDAO = SpringBeanFactory.getBean("clarity360ReportStageEntityDAO");
	}
	
	public Clarity360SummaryOptCommand(String ip, 
			Clarity360Merge01DAO clarity360Merge01DAO, Integer serverId, String localTableName, String targetDate, Integer reportId) {
		this.ip = ip;
		this.clarity360Merge01DAO = clarity360Merge01DAO;
		this.serverId = serverId;
		this.localTableName = localTableName;
		this.targetDate = targetDate;
		this.reportId = reportId;
		clarity360ReportStageEntityDAO = SpringBeanFactory.getBean("clarity360ReportStageEntityDAO");
	}
	
	public Clarity360SummaryOptCommand(String ip, 
			Clarity360Merge02DAO clarity360Merge02DAO, Integer serverId, String localTableName, String targetDate, Integer reportId) {
		this.ip = ip;
		this.clarity360Merge02DAO = clarity360Merge02DAO;
		this.serverId = serverId;
		this.localTableName = localTableName;
		this.targetDate = targetDate;
		this.reportId = reportId;
		clarity360ReportStageEntityDAO = SpringBeanFactory.getBean("clarity360ReportStageEntityDAO");
	}
	
	public Clarity360SummaryOptCommand(String ip, 
			Clarity360Merge03DAO clarity360Merge03DAO, Integer serverId, String localTableName, String targetDate, Integer reportId) {
		this.ip = ip;
		this.clarity360Merge03DAO = clarity360Merge03DAO;
		this.serverId = serverId;
		this.localTableName = localTableName;
		this.targetDate = targetDate;
		this.reportId = reportId;
		clarity360ReportStageEntityDAO = SpringBeanFactory.getBean("clarity360ReportStageEntityDAO");
	}
	
	public Clarity360SummaryOptCommand(String ip, 
			Clarity360Merge04DAO clarity360Merge04DAO, Integer serverId, String localTableName, String targetDate, Integer reportId) {
		this.ip = ip;
		this.clarity360Merge04DAO = clarity360Merge04DAO;
		this.serverId = serverId;
		this.localTableName = localTableName;
		this.targetDate = targetDate;
		this.reportId = reportId;
		clarity360ReportStageEntityDAO = SpringBeanFactory.getBean("clarity360ReportStageEntityDAO");
	}
	
	public Clarity360SummaryOptCommand(String ip, 
			Clarity360Merge05DAO clarity360Merge05DAO, Integer serverId, String localTableName, String targetDate, Integer reportId) {
		this.ip = ip;
		this.clarity360Merge05DAO = clarity360Merge05DAO;
		this.serverId = serverId;
		this.localTableName = localTableName;
		this.targetDate = targetDate;
		this.reportId = reportId;
		clarity360ReportStageEntityDAO = SpringBeanFactory.getBean("clarity360ReportStageEntityDAO");
	}

	@Override
	protected void execute() throws Exception {
		long a = System.currentTimeMillis();
		
//      try {
//    	System.out.println("optimize server1");
//    	insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, Clarity360ReportStageEntity.STEP_OPT_SERVER1);
//		clarity360Lweb01DAO.optimizeTable(localTableName, targetDate);
//		clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_OPT_SERVER1, 
//				Clarity360ReportStageEntity.STATUS_COMPLETE, "");
//	} catch (Exception e) {
//		clarity360ReportStageEntityDAO.updateStatus(reportId, Clarity360ReportStageEntity.STEP_OPT_SERVER1, 
//				Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
//		e.printStackTrace();
//	}
		Integer step = Clarity360ReportStageEntity.STEP_OPT_SERVER1;
//		if(serverId == 1 && StringUtils.equals(localTableName, Clarity360SummaryV3.localTableName)) {
//			step = Clarity360ReportStageEntity.STEP_OPT_SERVER1;
//		} else if(serverId == 1 && StringUtils.equals(localTableName, Clarity360SummaryV3.localCustomTableName)) {
//			step = Clarity360ReportStageEntity.STEP_OPT_SERVER1_URL_HASH;
//		} else if(serverId == 2 && StringUtils.equals(localTableName, Clarity360SummaryV3.localTableName)) {
//			step = Clarity360ReportStageEntity.STEP_OPT_SERVER2;
//		} else if(serverId == 2 && StringUtils.equals(localTableName, Clarity360SummaryV3.localCustomTableName)) {
//			step = Clarity360ReportStageEntity.STEP_OPT_SERVER2_URL_HASH;
//		} else if(serverId == 3 && StringUtils.equals(localTableName, Clarity360SummaryV3.localTableName)) {
//			step = Clarity360ReportStageEntity.STEP_OPT_SERVER3;
//		} else if(serverId == 3 && StringUtils.equals(localTableName, Clarity360SummaryV3.localCustomTableName)) {
//			step = Clarity360ReportStageEntity.STEP_OPT_SERVER3_URL_HASH;
//		} else if(serverId == 4 && StringUtils.equals(localTableName, Clarity360SummaryV3.localTableName)) {
//			step = Clarity360ReportStageEntity.STEP_OPT_SERVER4;
//		} else if(serverId == 4 && StringUtils.equals(localTableName, Clarity360SummaryV3.localCustomTableName)) {
//			step = Clarity360ReportStageEntity.STEP_OPT_SERVER4_URL_HASH;
//		} else if(serverId == 5 && StringUtils.equals(localTableName, Clarity360SummaryV3.localTableName)) {
//			step = Clarity360ReportStageEntity.STEP_OPT_SERVER5;
//		} else if(serverId == 5 && StringUtils.equals(localTableName, Clarity360SummaryV3.localCustomTableName)) {
//			step = Clarity360ReportStageEntity.STEP_OPT_SERVER5_URL_HASH;
//		}
		
		if(serverId == 1 && StringUtils.equals(localTableName, Clarity360SummaryV6.localTableName)) {
			step = Clarity360ReportStageEntity.STEP_OPT_SERVER1;
		} else if(serverId == 1 && StringUtils.equals(localTableName, Clarity360SummaryV6.localCustomTableName)) {
			step = Clarity360ReportStageEntity.STEP_OPT_SERVER1_URL_HASH;
		} else if(serverId == 2 && StringUtils.equals(localTableName, Clarity360SummaryV6.localTableName)) {
			step = Clarity360ReportStageEntity.STEP_OPT_SERVER2;
		} else if(serverId == 2 && StringUtils.equals(localTableName, Clarity360SummaryV6.localCustomTableName)) {
			step = Clarity360ReportStageEntity.STEP_OPT_SERVER2_URL_HASH;
		} else if(serverId == 3 && StringUtils.equals(localTableName, Clarity360SummaryV6.localTableName)) {
			step = Clarity360ReportStageEntity.STEP_OPT_SERVER3;
		} else if(serverId == 3 && StringUtils.equals(localTableName, Clarity360SummaryV6.localCustomTableName)) {
			step = Clarity360ReportStageEntity.STEP_OPT_SERVER3_URL_HASH;
		} else if(serverId == 4 && StringUtils.equals(localTableName, Clarity360SummaryV6.localTableName)) {
			step = Clarity360ReportStageEntity.STEP_OPT_SERVER4;
		} else if(serverId == 4 && StringUtils.equals(localTableName, Clarity360SummaryV6.localCustomTableName)) {
			step = Clarity360ReportStageEntity.STEP_OPT_SERVER4_URL_HASH;
		} else if(serverId == 5 && StringUtils.equals(localTableName, Clarity360SummaryV6.localTableName)) {
			step = Clarity360ReportStageEntity.STEP_OPT_SERVER5;
		} else if(serverId == 5 && StringUtils.equals(localTableName, Clarity360SummaryV6.localCustomTableName)) {
			step = Clarity360ReportStageEntity.STEP_OPT_SERVER5_URL_HASH;
		}
		
		try {
			insertNewStage(reportId, Clarity360ReportStageEntity.STATUS_PROCESSING, step);
			
//			if (serverId == 1) {
//				clarity360Lweb01DAO.optimizeTable(localTableName, targetDate);
//			} else if (serverId == 2) {
//				clarity360Lweb02DAO.optimizeTable(localTableName, targetDate);
//			} else if (serverId == 3) {
//				clarity360Lweb03DAO.optimizeTable(localTableName, targetDate);
//			} else if (serverId == 4) {
//				clarity360Lweb04DAO.optimizeTable(localTableName, targetDate);
//			} else if (serverId == 5) {
//				clarity360Lweb05DAO.optimizeTable(localTableName, targetDate);
//			}
			
			if (serverId == 1) {
				clarity360Merge01DAO.optimizeTable(localTableName, reportId);
			} else if (serverId == 2) {
				clarity360Merge02DAO.optimizeTable(localTableName, reportId);
			} else if (serverId == 3) {
				clarity360Merge03DAO.optimizeTable(localTableName, reportId);
			} else if (serverId == 4) {
				clarity360Merge04DAO.optimizeTable(localTableName, reportId);
			} else if (serverId == 5) {
				clarity360Merge05DAO.optimizeTable(localTableName, reportId);
			}
			
			clarity360ReportStageEntityDAO.updateStatus(reportId, step, 
					Clarity360ReportStageEntity.STATUS_COMPLETE, "");
		} catch (Exception e) {
			e.printStackTrace();
			clarity360ReportStageEntityDAO.updateStatus(reportId, step, 
					Clarity360ReportStageEntity.STATUS_ERROR, StringUtils.substring(e.getMessage(), 0, 400));
		}
		long b = System.currentTimeMillis();
		System.out.println("EndIP:" + ip + " time:" + (b - a) * 1.0 / 1000 + "s");
		CacheModleFactory.getInstance().setAliveIpAddress(ip); 
	}
	
    private void insertNewStage(Integer reportId, Integer status, Integer dateSourceType) {
    	try {
    		Clarity360ReportStageEntity clarity360ReportStageEntity = new Clarity360ReportStageEntity();
        	
        	clarity360ReportStageEntity.setReportId(reportId);
        	clarity360ReportStageEntity.setStatus(status);
        	clarity360ReportStageEntity.setDataSource(dateSourceType);
        	clarity360ReportStageEntity.setProcessStartTime(new Date());
        	
        	clarity360ReportStageEntityDAO.insertAutoRunDetail(clarity360ReportStageEntity);
		} catch (Exception e) {
			e.printStackTrace();
		}
    	
    }

	@Override
	protected void undo() throws Exception {
		// TODO Auto-generated method stub
		
	}
	
	
}