package seoclarity.backend.clarity360.vo;

import java.util.List;

import seoclarity.backend.entity.clickhouse.ri.BaseClarityDBVO;
import seoclarity.backend.export.vo.ClarityDBTypesFilterVO;
import seoclarity.backend.export.vo.PaginationColFilter;

public class Clarity360BaseVO extends BaseClarityDBVO {

	private List<ClarityDBTypesFilterVO> contentTypeFilters;
	
	private PaginationColFilter[] leftNaviFilters;
	
	private Integer urlTagId;
	
	public Integer getUrlTagId() {
		return urlTagId;
	}

	public void setUrlTagId(Integer urlTagId) {
		this.urlTagId = urlTagId;
	}

	public List<ClarityDBTypesFilterVO> getContentTypeFilters() {
		return contentTypeFilters;
	}

	public void setContentTypeFilters(List<ClarityDBTypesFilterVO> contentTypeFilters) {
		this.contentTypeFilters = contentTypeFilters;
	}
	
	public ClarityDBTypesFilterVO getContentTypeFilterVO() {
		if (contentTypeFilters != null && contentTypeFilters.size() >= 1) {
			return contentTypeFilters.get(0);
		}
		return null;
	}

	public PaginationColFilter[] getLeftNaviFilters() {
		return leftNaviFilters;
	}

	public void setLeftNaviFilters(PaginationColFilter[] leftNaviFilters) {
		this.leftNaviFilters = leftNaviFilters;
	}
	

}
