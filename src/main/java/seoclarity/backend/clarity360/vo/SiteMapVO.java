package seoclarity.backend.clarity360.vo;

import java.util.List;

public class SiteMapVO extends Clarity360BaseVO {
	
	private List<String> urlList;
	
	private String crawlRequestDate;
	
	private String siteMapUrl;

	public String getCrawlRequestDate() {
		return crawlRequestDate;
	}

	public void setCrawlRequestDate(String crawlRequestDate) {
		this.crawlRequestDate = crawlRequestDate;
	}

	public String getSiteMapUrl() {
		return siteMapUrl;
	}

	public void setSiteMapUrl(String siteMapUrl) {
		this.siteMapUrl = siteMapUrl;
	}

	public List<String> getUrlList() {
		return urlList;
	}

	public void setUrlList(List<String> urlList) {
		this.urlList = urlList;
	}
	
	
	
}
