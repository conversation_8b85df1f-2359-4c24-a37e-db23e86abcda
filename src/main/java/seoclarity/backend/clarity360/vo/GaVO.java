package seoclarity.backend.clarity360.vo;

public class GaVO extends Clarity360BaseVO {
	
	private String type;
	
	private String analyticsGroupFilter;
	
	private Integer analyticsGroupId;
	
	private String[] engines;
	
	private Integer trafficType;
	
	private Integer ownDomainProtocol;
	
	private Integer profileId;
	
	
	public Integer getOwnDomainProtocol() {
		return ownDomainProtocol;
	}

	public void setOwnDomainProtocol(Integer ownDomainProtocol) {
		this.ownDomainProtocol = ownDomainProtocol;
	}

	public Integer getTrafficType() {
		return trafficType;
	}

	public void setTrafficType(Integer trafficType) {
		this.trafficType = trafficType;
	}

	public String[] getEngines() {
		return engines;
	}

	public void setEngines(String[] engines) {
		this.engines = engines;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getAnalyticsGroupFilter() {
		return analyticsGroupFilter;
	}

	public void setAnalyticsGroupFilter(String analyticsGroupFilter) {
		this.analyticsGroupFilter = analyticsGroupFilter;
	}

	public Integer getAnalyticsGroupId() {
		return analyticsGroupId;
	}

	public void setAnalyticsGroupId(Integer analyticsGroupId) {
		this.analyticsGroupId = analyticsGroupId;
	}

	public Integer getProfileId() {
		if (profileId == null || profileId <0) {
			return 0;
		}
		return profileId;
	}

	public void setProfileId(Integer profileId) {
		this.profileId = profileId;
	}
	
}
