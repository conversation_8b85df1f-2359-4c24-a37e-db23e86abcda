package seoclarity.backend.clarity360.vo;

import seoclarity.backend.utils.CommonUtils;

public class RiVO extends Clarity360BaseVO {

	private Integer locationId;

	private String domainName;

	private String rankingType;

	private Integer languageId;

	private Integer engineId;
	
	private String device;
	
	private Boolean broadMatch;
	
	public String getDevice() {
		return device;
	}

	public void setDevice(String device) {
		this.device = device;
	}

	public Integer getLocationId() {
		if (locationId == null) {
			return 0;
		}
		return locationId;
	}

	public void setLocationId(Integer locationId) {
		this.locationId = locationId;
	}

	public String getDomainName() {
		return domainName;
	}

	public void setDomainName(String domainName) {
		this.domainName = domainName;
	}
	
	public String getDomainReverse() {
		try {
			return CommonUtils.getReversedRootDomain(domainName);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return domainName;
	}

	public String getRankingType() {
		return rankingType;
	}

	public void setRankingType(String rankingType) {
		this.rankingType = rankingType;
	}

	public Integer getLanguageId() {
		if (languageId == null) {
			return 0;
		}
		return languageId;
	}

	public void setLanguageId(Integer languageId) {
		this.languageId = languageId;
	}

	public Integer getEngineId() {
		if (engineId == null) {
			return 0;
		}
		return engineId;
	}

	public void setEngineId(Integer engineId) {
		this.engineId = engineId;
	}

	public Boolean isBroadMatch() {
		return broadMatch;
	}

	public void setBroadMatch(Boolean isBroadMatch) {
		this.broadMatch = isBroadMatch;
	}
	

}
