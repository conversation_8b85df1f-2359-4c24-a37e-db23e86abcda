package seoclarity.backend.clarity360.vo;

import seoclarity.backend.export.vo.ClarityDBTypesFilterVO;

public class SiteHealthVO extends Clarity360BaseVO {
	
	private Integer crawlRequestId;
	
	private Long crawlRequestDate;
	
	private boolean isEnableEmbedding;
	

	public Integer getCrawlRequestId() {
		return crawlRequestId;
	}

	public void setCrawlRequestId(Integer crawlRequestId) {
		this.crawlRequestId = crawlRequestId;
	}

	public Long getCrawlRequestDate() {
		return crawlRequestDate;
	}

	public void setCrawlRequestDate(Long crawlRequestDate) {
		this.crawlRequestDate = crawlRequestDate;
	}

	public boolean isEnableEmbedding() {
		return isEnableEmbedding;
	}

	public void setEnableEmbedding(boolean isEnableEmbedding) {
		this.isEnableEmbedding = isEnableEmbedding;
	}

}
