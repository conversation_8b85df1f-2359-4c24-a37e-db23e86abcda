package seoclarity.backend.clarity360;

import seoclarity.backend.dao.clickhouse.clarity360.lweb01.Clarity360Lweb01DAO;
import seoclarity.backend.dao.clickhouse.clarity360.lweb02.Clarity360Lweb02DAO;
import seoclarity.backend.dao.clickhouse.clarity360.lweb03.Clarity360Lweb03DAO;
import seoclarity.backend.dao.clickhouse.clarity360.lweb04.Clarity360Lweb04DAO;
import seoclarity.backend.dao.clickhouse.clarity360.lweb05.Clarity360Lweb05DAO;

public class OptimizeTableCommand extends Thread {
	
	private Clarity360Lweb01DAO clarity360Lweb01DAO;
	private Clarity360Lweb02DAO clarity360Lweb02DAO;
	private Clarity360Lweb03DAO clarity360Lweb03DAO;
	private Clarity360Lweb04DAO clarity360Lweb04DAO;
	private Clarity360Lweb05DAO clarity360Lweb05DAO;
	
	public OptimizeTableCommand() {
		
	}
	 
    @Override
    public void run() {
        
    }
 
}
