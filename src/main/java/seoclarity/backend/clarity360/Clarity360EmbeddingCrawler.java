package seoclarity.backend.clarity360;

import java.util.List;

import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.ArrayUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.SemanticSimilarityProjectDAO;
import seoclarity.backend.dao.clickhouse.clarity360.lweb05.Clarity360LwebVector05DAO;
import seoclarity.backend.dao.clickhouse.prodclarity.DisSiteCrawlDoc1Dao;
import seoclarity.backend.entity.clarity360.SemanticSimilarityProject;
import seoclarity.backend.entity.clickhouse.prod.DisSiteCrawlDoc1Entity;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.threadpool.ThreadPoolManager;
import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.vectordb.EmbeddingEntity;

public class Clarity360EmbeddingCrawler {

	//mvn exec:java -Dexec.mainClass="seoclarity.backend.clarity360.Clarity360EmbeddingCrawler" -Dexec.args=""
	public static ThreadPoolManager threadPool = ThreadPoolManager.getInstance();
	private static final String databaseName = "vector_db";
	private static final String finalTableName = "dis_vector_table";
	private Clarity360LwebVector05DAO clarity360LwebVector05DAO;
	private SemanticSimilarityProjectDAO semanticSimilarityProjectDAO;
	
	private DisSiteCrawlDoc1Dao disSiteCrawlDoc1Dao;
	
	public Clarity360EmbeddingCrawler() {
		// TODO Auto-generated constructor stub
		disSiteCrawlDoc1Dao = SpringBeanFactory.getBean("disSiteCrawlDoc1Dao");
		clarity360LwebVector05DAO = SpringBeanFactory.getBean("clarity360LwebVector05DAO");
		semanticSimilarityProjectDAO = SpringBeanFactory.getBean("semanticSimilarityProjectDAO");
	}
	
	public static void main(String[] args) {
		Clarity360EmbeddingCrawler Clarity360EmbeddingCrawler = new Clarity360EmbeddingCrawler();
		
		Clarity360EmbeddingCrawler.process();
	}
	
	private void process() {
		
		processOne(7529, 9972209);
	}
	
	private static Integer pageSize = 500;
	
	public void processOne(Integer ownDomainId, Integer crawlRequestLogId) {
		
		
		Integer pageNum = 1;
		while (true) {
			
			System.out.println("===== processing ownDomainId:" + ownDomainId);
			System.out.println("===== processing crawlRequestLogId:" + crawlRequestLogId);
			System.out.println("===== processing pageSize:" + pageSize);
			System.out.println("===== processing pageNum:" + pageNum);
			// Alps: only query for desktop and national
			List<DisSiteCrawlDoc1Entity> resultList = disSiteCrawlDoc1Dao.getByDomainIdAndCrawlRequestId(
					ownDomainId, crawlRequestLogId, pageSize, pageNum);
			
			System.out.println("resultList size:" + resultList.size());

			if (resultList == null || resultList.size() == 0) {
				break;
			}
			
			pageNum++;
			System.out.println("Page num : " + pageNum + ", pageSize : " + pageSize + ", size:" + resultList.size());

			try {
				threadPool.init();
				CommonUtils.initThreads(1);
				
				DisSiteCrawlDoc1Entity[] disSiteCrawlDoc1EntityArray = resultList.toArray(new DisSiteCrawlDoc1Entity[resultList.size()]);
				
			 	do {
		
					String ipAddress = CacheModleFactory.getInstance().getAliveIpAddress();
					if (ipAddress == null) {
						Thread.sleep(1 * 1000);
						continue;
					}
					
					if (disSiteCrawlDoc1EntityArray == null || disSiteCrawlDoc1EntityArray.length == 0) {
						break;
					}
					
					DisSiteCrawlDoc1Entity[] commandData = (DisSiteCrawlDoc1Entity[]) ArrayUtils.subarray(disSiteCrawlDoc1EntityArray, 0, 1);
					disSiteCrawlDoc1EntityArray = (DisSiteCrawlDoc1Entity[]) ArrayUtils.remove(disSiteCrawlDoc1EntityArray, 0);
				
					Clarity360EmbeddingCrawlerCommand crawCommand = getUpdateCommand(ipAddress, commandData[0]);
					try {
						threadPool.execute(crawCommand); 
					} catch (Exception e) {
						e.printStackTrace();
					}
					
					
				} while (true);
				
				do {
					try {
						Thread.sleep(5000);
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
				} while (threadPool.getThreadPool().getActiveCount() > 0);
				break;
			} catch (Exception e) {
				e.printStackTrace();
			} finally {
				threadPool.destroy();
			}
			
			List<EmbeddingEntity> embeddingEntities = CacheModleFactory.getInstance().getEmbeddingMap();
			
			System.out.println(new Gson().toJson(embeddingEntities.get(0)));
			
//			List<EmbeddingEntity> embeddingEntities = CacheModleFactory.getInstance().getEmbeddingMap();
//			if (CollectionUtils.isNotEmpty(embeddingEntities)) {
//				
//				try {
//					if (CollectionUtils.isNotEmpty(embeddingEntities)) {
//		          		clarity360LwebVector05DAO.insertBatch(embeddingEntities, finalTableName);
//		              	System.out.println("finish insert for left count :" + embeddingEntities.size());
//		          	}
//				} catch (Exception e) {
//					e.printStackTrace();
//				}
//				
//				embeddingEntities.clear();
//				CacheModleFactory.getInstance().clearEmbeddingMapCache();
//			}
			
		}
	}
	
	private Clarity360EmbeddingCrawlerCommand getUpdateCommand(String ipAddress, DisSiteCrawlDoc1Entity disSiteCrawlDoc1Entity) {
		Clarity360EmbeddingCrawlerCommand crawlCommand = new Clarity360EmbeddingCrawlerCommand(ipAddress, disSiteCrawlDoc1Entity);
		crawlCommand.setStatus(true);
		return crawlCommand;
	}
	

}
