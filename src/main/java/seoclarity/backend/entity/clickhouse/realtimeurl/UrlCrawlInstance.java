package seoclarity.backend.entity.clickhouse.realtimeurl;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/8/30 11:36
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UrlCrawlInstance {

    public static final String TOPIC_REAL_TIME_PAGE_CRAWL = "real_time_page_crawl";

    public static final String TOPIC_URL_CRAWL = "url_crawl";

    private Integer ownDomainId;

    private Integer crawlInstanceId;

    private Integer crawlInstanceIdMod;

    private String url;

    private Long urlMurmur3Hash;

    private Integer responseCode;

    private String responseMsg;

    private Integer indexable;

    private Integer blockedByRobots;

    private Integer indexFlg;

    private Integer indexFlgXTag;

    private String canonicalType;

    private String canonicalHeaderType;

    private LocalDateTime createDate;

}
