package seoclarity.backend.dao.clickhouse;

import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public class SplitTestCentralDAO<T> extends CICentralBaseJdbcSupport<T> {
    @Override
    public String getTableName() {
        return "";
    }

    public List<String> retrieveMetrics(String sql) {
        return this.queryForStringList(sql);
    }

    public Integer retrieveCount(String sql) {
        return this.queryForInteger(sql);
    }

    public List<T> retrieveData(String sql, Class<T> clazz) {
        return super.getJdbcTemplate().query(sql, new BeanPropertyRowMapper<T>(clazz));
    }

}
