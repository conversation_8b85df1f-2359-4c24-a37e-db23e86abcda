package seoclarity.backend.dao.clickhouse.realtimeurl;

import seoclarity.backend.entity.clickhouse.realtimeurl.UrlCrawlInstance;

import java.util.List;

public class UrlCrawlInstanceDAO extends RealtimeUrlBaseJdbcSupport<UrlCrawlInstance> {
    @Override
    public String getTableName() {
        return "dis_url_crawl_instance";
    }

    public static final String[] columns = new String[] {
            "ownDomainId",
            "crawlInstanceId",
            "url",
            "responseCode",
            "responseMsg",
            "indexable",
            "blockedByRobots",
            "indexFlg",
            "indexFlgXTag",
            "canonicalType",
            "canonicalHeaderType"
    };

    public Integer count() {
        return queryForInteger("select count(*) from " + getTableName());
    }

    public Integer countByDomainIdAndCrawlInstanceId(int domainId, Integer crawlInstanceId) {
        return queryForInteger("select count(*) from " + getTableName() + " where domainId = ? and crawlInstanceId = ?", domainId, crawlInstanceId);
    }

    public List<UrlCrawlInstance> queryByDomainIdAndCrawlInstanceId(int domainId, Integer crawlInstanceId) {
        return super.findBySql("select url, responseCode, indexable from " + getTableName() + " where domainId = ? and crawlInstanceId = ?", domainId, crawlInstanceId);
    }

}
