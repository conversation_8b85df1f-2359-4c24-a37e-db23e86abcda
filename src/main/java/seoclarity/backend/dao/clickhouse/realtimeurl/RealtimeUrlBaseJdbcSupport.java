package seoclarity.backend.dao.clickhouse.realtimeurl;

import seoclarity.backend.dao.BaseJdbcSupport;

import javax.annotation.Resource;
import javax.sql.DataSource;

public abstract class RealtimeUrlBaseJdbcSupport<T> extends BaseJdbcSupport<T> {

    @Resource(name = "realtimeUrlDataSource")
    private DataSource dataSource;

    @Override
    public DataSource getDataSource() {
        return dataSource;
    }
}
