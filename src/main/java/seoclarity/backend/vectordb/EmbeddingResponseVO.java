package seoclarity.backend.vectordb;

public class EmbeddingResponseVO {

	private DataVO[] data;
	
	public DataVO[] getData() {
		return data;
	}

	public void setData(DataVO[] data) {
		this.data = data;
	}

	public class DataVO {

		private Integer index;
		private Float[] embedding;

		public Integer getIndex() {
			return index;
		}

		public void setIndex(Integer index) {
			this.index = index;
		}

		public Float[] getEmbedding() {
			return embedding;
		}

		public void setEmbedding(Float[] embedding) {
			this.embedding = embedding;
		}

	}

}
