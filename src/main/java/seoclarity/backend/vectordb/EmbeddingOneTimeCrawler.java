package seoclarity.backend.vectordb;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;

import com.google.gson.Gson;

import seoclarity.backend.utils.HttpRequestUtils;


//mvn exec:java -Dexec.mainClass="seoclarity.backend.vectordb.EmbeddingOneTimeCrawler" -Dexec.args=""
public class EmbeddingOneTimeCrawler {

	
	public EmbeddingOneTimeCrawler() {
		// TODO Auto-generated constructor stub
	}
	
	private static String API_OPENAI_EMBEDDING = "https://api.openai.com/v1/embeddings";
	private static Map<String, String> OPENAI_DEFAULT_HEADER = new HashMap<String, String>();
	
    private static String crawlingFolderPath = "/home/<USER>/embedding/crawling";
    private static String doneFolderPath = "/home/<USER>/embedding/needUpload";
    public static final char FILE_SPLIT = '\t';
    public static final String ENCODING = "UTF-8";
    
    private static Gson gson = new Gson();
	static {
		OPENAI_DEFAULT_HEADER.put("Content-Type", "application/json");
		OPENAI_DEFAULT_HEADER.put("Authorization", "Bearer ***************************************************");
	}
	
	public static final String[] DB_COLUMNS = new String[]{
			"url_murmur_hash", "crawl_request_id", "crawl_request_id_mod", "crawl_request_date", "domain_id", "url", "title", 
			"meta", "h1", "h2", "custom_data_1", "custom_data_2", "custom_data_3", "custom_data_4", 
			"custom_data_5", "title_embedding", "meta_embedding", "h1_embedding", "h2_embedding", 
			"custom_data_1_embedding", "custom_data_2_embedding", "custom_data_3_embedding", 
			"custom_data_4_embedding", "custom_data_5_embedding"
    };
	
	private static String OPENAI_MODEL = "text-embedding-ada-002";
	
	public static void main(String[] args) {
		
//		if (args != null && args.length >= 4) {
//			
//			Integer ownDomainId = NumberUtils.toInt(args[0]);
//			Integer crawlRequestId = NumberUtils.toInt(args[1]);
//			Integer pageNumber = NumberUtils.toInt(args[2]);
//			Integer pageSize = NumberUtils.toInt(args[3]);
//			
//			EmbeddingCrawler embeddingCrawler = new EmbeddingCrawler();
//			embeddingCrawler.processByPage(ownDomainId, crawlRequestId, pageSize, pageNumber);
//		}
		
		EmbeddingOneTimeCrawler embeddingOneTimeCrawler = new EmbeddingOneTimeCrawler();
		embeddingOneTimeCrawler.process();
		
	}

	private void process() {
		
		Map<String, String[]> urlMap = new HashMap<>();
		
		try {
			int num = 0;
			// create a reader for tmpFile
			BufferedReader in = new BufferedReader(new FileReader("/home/<USER>/kp_out.txt"));
			String str;
			while ((str = in.readLine()) != null) {
				System.out.println("processing on :" + num);
				long startTime = System.currentTimeMillis();
				num++;
				String[] arrays = StringUtils.split(str, "\t");
				if (arrays.length != 3) {
					System.out.println("ERROR arrays.length: " + arrays.length );
					System.out.println("ERROR: " + str );
					continue;
				} else {
					urlMap.put("https://www.seoclarity.net/url" + arrays[0], new String[] {arrays[1], arrays[2]});
				}

				long endTimeo = System.currentTimeMillis();
				int elapsedSecondos = (int) (endTimeo - startTime);
				System.out.println("cost: " + elapsedSecondos + " sec");

			}
			in.close();
		} catch (Exception e) {
			// TODO: handle exception
		}
		
		try {
			File crawlingFolder = new File(crawlingFolderPath);
	        if (crawlingFolder == null || !crawlingFolder.exists() || !crawlingFolder.isDirectory()) {

	            System.out.println("folder crawlering is not exist, creating now!! ");
	            crawlingFolder.mkdirs();
	        }
	        String prefix = "embedding_test_";
			File tempfile = File.createTempFile(prefix, ".processing", crawlingFolder);
			String targetFileName = StringUtils.replace(tempfile.getName(), ".processing", ".txt");
	        
            File doneFolder = new File(doneFolderPath);
            File targetFile = new File(doneFolderPath + "/" + targetFileName);
            if (doneFolder == null || !doneFolder.isDirectory()) {
                System.out.println("Target folder is not exist!!! folder:" + doneFolder.getAbsolutePath());
                doneFolder.mkdirs();
            }
			List<String> outputLines = new ArrayList<>();
			
			System.out.println("siteCrawlList size:" + urlMap.size()); 
			int num = 0;
			for(String url : urlMap.keySet()) {
				num++;
				if (num % 10 == 0) {
					System.out.println("processing on :" + num + ", url: " + url);
				}
				List<String> resultList = processByUrl(url, urlMap.get(url));
				if (resultList != null && resultList.size() > 0) {
					System.out.println("Adding :" + resultList.size());
					outputLines.addAll(resultList);
				}
			}
			
			if (outputLines.size() >= 0) {
				output(tempfile, outputLines);
				FileUtils.moveFile(tempfile, targetFile);
				outputLines.clear();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	private List<String> processByUrl(String url, String[] contentArray) throws SQLException {
		
		if (contentArray == null || contentArray.length == 0) {
			return null;
		}
		
		
		ParamVO paramVO = new ParamVO();
		
		paramVO.setInput(contentArray);
		paramVO.setModel(OPENAI_MODEL);
		
//		String dataJson = \
		
		List<String> resultList = new ArrayList<>();
		
		Map<String, String> response = HttpRequestUtils.queryWebServiceFunctionPostMap(API_OPENAI_EMBEDDING, new Gson().toJson(paramVO), null, OPENAI_DEFAULT_HEADER);
		if (response!= null) {
			String responseText = response.get("response");
			
			EmbeddingResponseVO embeddingResponseVO = new Gson().fromJson(responseText, EmbeddingResponseVO.class);
			
			if (embeddingResponseVO == null || embeddingResponseVO.getData() == null || contentArray.length == embeddingResponseVO.getData().length) {
				String[] row = new String[5];
				row[0] = url;
				row[1] = contentArray[0];
				row[2] = contentArray[1];
				row[3] = gson.toJson(embeddingResponseVO.getData()[0].getEmbedding());
				row[4] = gson.toJson(embeddingResponseVO.getData()[1].getEmbedding());
				resultList.add(StringUtils.join(row, FILE_SPLIT));
			}
			return resultList;
		} else {
			System.out.println("Resp is empty!");
			return null;
		}
		
	}
	
	private static void output(File file, List<String> outLines) throws Exception{
        FileUtils.writeLines(file, "utf-8", outLines, true);
    }
	
	
}
