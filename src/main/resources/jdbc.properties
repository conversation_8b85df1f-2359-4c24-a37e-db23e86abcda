##########################
##    JDBC Setting      ##
##########################

## MySQL
#jdbc.driver=com.mysql.cj.jdbc.Driver
#jdbc.url=*************************************************************************************************
#jdbc.username=root
#jdbc.password=java@server

#jdbc.driver=com.mysql.cj.jdbc.Driver
#jdbc.url=********************************************************************************************************************************************************
#jdbc.username=actoniaadmin
#jdbc.password=q8V$iWLsaLeVY

jdbc.driver=com.mysql.cj.jdbc.Driver
jdbc.url=************************************************************************************************************************************************
jdbc.username=shinetech
jdbc.password=%nKdm=pbjACu

api.jdbc.driver=com.mysql.cj.jdbc.Driver
api.jdbc.url=*******************************************************************************************
api.jdbc.username=shinetech
api.jdbc.password=%nKdm=pbjACu

jdbc.bot.driver=com.mysql.cj.jdbc.Driver
jdbc.bot.url=*******************************************************************************************
jdbc.bot.username=shinetech
jdbc.bot.password=%nKdm=pbjACu

## rankcheck
rankcheck.jdbc.driver=com.mysql.cj.jdbc.Driver
rankcheck.jdbc.url=****************************************************************************************************
rankcheck.jdbc.username=shinetech
rankcheck.jdbc.password=%nKdm=pbjACu

## rankcheck backup DB46
## rankcheck backup DB52
rankcheck.backup.jdbc.driver=org.gjt.mm.mysql.Driver
#rankcheck.backup.jdbc.url=**********************************************************************************************************************
rankcheck.backup.jdbc.url=**********************************************************************************************************
rankcheck.backup.jdbc.username=shinetech
rankcheck.backup.jdbc.password=%nKdm=pbjACu

## clickhouse
claritydb.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.jdbc.url=*********************************************************************
claritydb.jdbc.username=default
claritydb.jdbc.password=clarity99!

## clickhouse - tiktok master
tiktok.master.claritydb.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
tiktok.master.claritydb.jdbc.url=*****************************************************************
tiktok.master.claritydb.jdbc.username=default
tiktok.master.claritydb.jdbc.password=clarity99!

## clickhouse - tiktok backup
tiktok.backup.claritydb.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
tiktok.backup.claritydb.jdbc.url=*****************************************************************
tiktok.backup.claritydb.jdbc.username=default
tiktok.backup.claritydb.jdbc.password=clarity99!

## clickhouse - amazon master
amazon.master.claritydb.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
amazon.master.claritydb.jdbc.url=*****************************************************************
amazon.master.claritydb.jdbc.username=default
amazon.master.claritydb.jdbc.password=clarity99!

## clickhouse - amazon backup
amazon.backup.claritydb.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
amazon.backup.claritydb.jdbc.url=*****************************************************************
amazon.backup.claritydb.jdbc.username=default
amazon.backup.claritydb.jdbc.password=clarity99!

## clickhouse ads
claritydbads.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbads.jdbc.url=*********************************************************************
claritydbads.jdbc.username=default
claritydbads.jdbc.password=clarity99!

## clickhouse - bot upload
claritydb.bot.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
##claritydb.bot.jdbc.url=************************************************
claritydb.bot.jdbc.url=************************************************************************
claritydb.bot.jdbc.username=default
claritydb.bot.jdbc.password=clarity99!

## clickhouse - bot query
claritydb.bot.query.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.bot.query.jdbc.url=***********************************************************************
claritydb.bot.query.jdbc.username=default
claritydb.bot.query.jdbc.password=clarity99!

## clickhouse - GSC
claritydb.gsc.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
#claritydb.gsc.jdbc.url=*************************************************
claritydb.gsc.jdbc.url=**********************************************************************
claritydb.gsc.jdbc.username=default
claritydb.gsc.jdbc.password=clarity99!

## clickhouse - gsc standby server
claritydb.gscstandby.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.gscstandby.jdbc.url=************************************************
claritydb.gscstandby.jdbc.username=default
claritydb.gscstandby.jdbc.password=clarity99!

## clickhouse - Content Idea
claritydb.contentidea.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.contentidea.jdbc.url=*******************************************************************************************************************************
claritydb.contentidea2.jdbc.url=********************************************************
claritydb.contentidea3.jdbc.url=********************************************************
claritydb.contentidea4.jdbc.url=jdbc:clickhouse://************:8123/actonia_content_idea
claritydb.contentidea.jdbc.username=default
claritydb.contentidea.jdbc.password=clarity99!

## clickhouse - ga
claritydb.ga.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.ga.jdbc.url=*********************************************************
claritydb.ga001.jdbc.url=********************************************************************************
claritydb.ga.jdbc.username=default
claritydb.ga.jdbc.password=clarity99!

## clickhouse - gagsc
claritydb.gagsc.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.gagsc.jdbc.url=***************************************
claritydb.gagsc.jdbc.username=default
claritydb.gagsc.jdbc.password=clarity99!


## clickhouse - links ga
claritydb.linksga.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.linksga.jdbc.url=*************************************************
claritydb.linksga.jdbc.username=default
claritydb.linksga.jdbc.password=clarity99!


## clickhouse - pagespeed
claritydb.pagespeed.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.pagespeed.jdbc.url=jdbc:clickhouse://************:8123/actonia_pagespeed
claritydb.pagespeed.jdbc.username=default
claritydb.pagespeed.jdbc.password=clarity99!

## clickhouse - actonia_ranking_forecast
claritydb.rankingforecast.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.rankingforecast.jdbc.url=jdbc:clickhouse://*************:8123/actonia_ranking_forecast
claritydb.rankingforecast.jdbc.username=default
claritydb.rankingforecast.jdbc.password=clarity99!

#mdb010 inner host server11
## daily ranking monitor
mdb001.jdbc.driver=com.mysql.cj.jdbc.Driver
mdb001.jdbc.url=*********************************************************************************************************
mdb001.jdbc.username=root
mdb001.jdbc.password=fixyfoxy

## mdb001 actonia
mdb001.actonia.jdbc.driver=com.mysql.cj.jdbc.Driver
mdb001.actonia.jdbc.url=*************************************************************************************************
mdb001.actonia.jdbc.username=shinetech
mdb001.actonia.jdbc.password=%nKdm=pbjACu

## clickhouse - monthly ranking
claritydb.monthlyranking.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.monthlyranking.jdbc.url=****************************************************?socket_timeout=6000000
claritydb.monthlyranking.jdbc.username=default
claritydb.monthlyranking.jdbc.password=clarity99!

## clickhouse - monthly video ranking
claritydb.monthlyvideoranking.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.monthlyvideoranking.jdbc.url=************************************************************
claritydb.monthlyvideoranking.jdbc.username=default
claritydb.monthlyvideoranking.jdbc.password=clarity99!

## clickhouse - monthly video ranking
claritydb.monthlyvideorankingbackup.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.monthlyvideorankingbackup.jdbc.url=**********************************************************
claritydb.monthlyvideorankingbackup.jdbc.username=default
claritydb.monthlyvideorankingbackup.jdbc.password=clarity99!

## clickhouse - pixel height ranking
claritydb.pixel.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.pixel.jdbc.url=*****************************************************************
claritydb.pixel.jdbc.username=default
claritydb.pixel.jdbc.password=clarity99!

## clickhouse - keyword_tokenizer
claritydb.keywordTokenizer.sv.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.keywordTokenizer.sv.jdbc.url=jdbc:clickhouse://*************:8123/keyword_searchvolume
claritydb.keywordTokenizer.sv.jdbc.username=default
claritydb.keywordTokenizer.sv.jdbc.password=clarity99!

claritydb.keywordTokenizer.sv.lweb.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.keywordTokenizer.sv.lweb.jdbc.url=jdbc:clickhouse://*************:8123/keyword_searchvolume
claritydb.keywordTokenizer.sv.lweb.jdbc.username=default
claritydb.keywordTokenizer.sv.lweb.jdbc.password=clarity99!

## ch01
claritydb.keywordTokenizer.sv.lweb2.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.keywordTokenizer.sv.lweb2.jdbc.url=**********************************************************
claritydb.keywordTokenizer.sv.lweb2.jdbc.username=default
claritydb.keywordTokenizer.sv.lweb2.jdbc.password=clarity99!

claritydb.dirty.keyword.pc.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.dirty.keyword.pc.jdbc.url=*****************************************************
claritydb.dirty.keyword.pc.jdbc.username=default
claritydb.dirty.keyword.pc.jdbc.password=clarity99!

claritydb.keywordTokenizer.sv.lweb212.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.keywordTokenizer.sv.lweb212.jdbc.url=*********************************************************
claritydb.keywordTokenizer.sv.lweb212.jdbc.username=default
claritydb.keywordTokenizer.sv.lweb212.jdbc.password=clarity99!

## clickhouse - monthly ranking test
claritydb.monthly.test.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.monthly.test.jdbc.url=****************************************************
claritydb.monthly.test.jdbc.username=default
claritydb.monthly.test.jdbc.password=clarity99!

## clickhouse - internal link
claritydb.internallink.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.internallink.server1.jdbc.url=*********************************************************************************
claritydb.internallink.server2.jdbc.url=*********************************************************************************
claritydb.internallink.server3.jdbc.url=**********************************************************************************
claritydb.internallink.newcluster.server1.jdbc.url=*********************************************************************************
claritydb.internallink.newcluster.server2.jdbc.url=*********************************************************************************
claritydb.internallink.newcluster.server3.jdbc.url=**********************************************************************************
claritydb.internallink.newcluster.server4.jdbc.url=**********************************************************************************
claritydb.internallink.newcluster.server5.jdbc.url=**********************************************************************************
claritydb.internallink.jdbc.username=default
claritydb.internallink.jdbc.password=clarity99!

## clickhouse - similar keywords
claritydb.similarkeywords.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.similarkeywords.jdbc.url=*****************************************************
claritydb.similarkeywords.jdbc.username=default
claritydb.similarkeywords.jdbc.password=clarity99!

## clickhouse - ved
claritydb.ved.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.ved.jdbc.url=*****************************************
claritydb.ved.jdbc.username=default
claritydb.ved.jdbc.password=

## clickhouse - html crawl
claritydb.crawlurl.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.crawlurl.jdbc.url=******************************************
claritydb.crawlurl.jdbc.username=default
claritydb.crawlurl.jdbc.password=clarity99!

## clickhouse - adhoc ranking
claritydb.adhoc.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.adhoc.jdbc.url=***************************************************
claritydb.adhoc.jdbc.username=actoniacdb
claritydb.adhoc.jdbc.password=0%z=Kft99003k#Y6

## clickhouse cold
claritydb.cold.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.cold.jdbc.url=******************************************************
claritydb.cold.jdbc.username=default
claritydb.cold.jdbc.password=clarity99!

claritydbCdbRi307.youtube.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRi307.youtube.jdbc.url=*******************************************************
claritydbCdbRi307.youtube.jdbc.username=default
claritydbCdbRi307.youtube.jdbc.password=clarity99!

## clickhouse - youtube ranking -CdbRi07
claritydbCdbRi07.youtube.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRi07.youtube.jdbc.url=******************************************************
claritydbCdbRi07.youtube.jdbc.username=default
claritydbCdbRi07.youtube.jdbc.password=clarity99!

## clickhouse - youtube ranking -CdbRi117
claritydb.nj114.youtube.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.nj114.youtube.jdbc.url=**********************************************************
claritydb.nj114.youtube.jdbc.username=default
claritydb.nj114.youtube.jdbc.password=clarity99!



## clickhouse - keyword ranking -CdbRi201
claritydbCdbRi201.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRi201.keyword.jdbc.url=*******************************************************
claritydbCdbRi201.keyword.jdbc.username=default
claritydbCdbRi201.keyword.jdbc.password=clarity99!

## clickhouse - keyword ranking -CdbRi202
claritydbCdbRi202.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRi202.keyword.jdbc.url=**********************************************************
claritydbCdbRi202.keyword.jdbc.username=default
claritydbCdbRi202.keyword.jdbc.password=clarity99!

## clickhouse - keyword ranking -CdbRi203
claritydbCdbRi203.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRi203.keyword.jdbc.url=**********************************************************
claritydbCdbRi203.keyword.jdbc.username=default
claritydbCdbRi203.keyword.jdbc.password=clarity99!

## clickhouse - keyword ranking -CdbRi204
claritydbCdbRi204.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRi204.keyword.jdbc.url=********************************************************
claritydbCdbRi204.keyword.jdbc.username=default
claritydbCdbRi204.keyword.jdbc.password=clarity99!
## clickhouse - keyword ranking -CdbRi205
claritydbCdbRi205.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRi205.keyword.jdbc.url=**********************************************************
claritydbCdbRi205.keyword.jdbc.username=default
claritydbCdbRi205.keyword.jdbc.password=clarity99!
## clickhouse - keyword ranking -CdbRi206
claritydbCdbRi206.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRi206.keyword.jdbc.url=**********************************************************
claritydbCdbRi206.keyword.jdbc.username=default
claritydbCdbRi206.keyword.jdbc.password=clarity99!
## clickhouse - keyword ranking -CdbRi207
claritydbCdbRi207.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRi207.keyword.jdbc.url=***********************************************************************
claritydbCdbRi207.keyword.jdbc.username=default
claritydbCdbRi207.keyword.jdbc.password=clarity99!

## clickhouse - keyword ranking -nj001
claritydbCdbRiNj001.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRiNj001.keyword.jdbc.url=********************************************************
claritydbCdbRiNj001.keyword.jdbc.username=default
claritydbCdbRiNj001.keyword.jdbc.password=clarity99!

## clickhouse - keyword ranking -nj002
claritydbCdbRiNj002.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRiNj002.keyword.jdbc.url=*********************************************************
claritydbCdbRiNj002.keyword.jdbc.username=default
claritydbCdbRiNj002.keyword.jdbc.password=clarity99!

## clickhouse - keyword ranking -nj003
claritydbCdbRiNj003.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRiNj003.keyword.jdbc.url=*********************************************************
claritydbCdbRiNj003.keyword.jdbc.username=default
claritydbCdbRiNj003.keyword.jdbc.password=clarity99!

## clickhouse - keyword ranking -nj004
claritydbCdbRiNj004.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRiNj004.keyword.jdbc.url=*********************************************************
claritydbCdbRiNj004.keyword.jdbc.username=default
claritydbCdbRiNj004.keyword.jdbc.password=clarity99!

## clickhouse - keyword ranking -nj005
claritydbCdbRiNj005.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRiNj005.keyword.jdbc.url=*********************************************************
claritydbCdbRiNj005.keyword.jdbc.username=default
claritydbCdbRiNj005.keyword.jdbc.password=clarity99!

## clickhouse - keyword ranking -nj006
claritydbCdbRiNj006.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRiNj006.keyword.jdbc.url=*********************************************************
claritydbCdbRiNj006.keyword.jdbc.username=default
claritydbCdbRiNj006.keyword.jdbc.password=clarity99!


## clickhouse - keyword ranking -nj007
claritydbCdbRiNj007.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydbCdbRiNj007.keyword.jdbc.url=***********************************************************************
claritydbCdbRiNj007.keyword.jdbc.username=default
claritydbCdbRiNj007.keyword.jdbc.password=clarity99!


## clickhouse - clarity360 summary 01
clarity360Lweb01.summary.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
clarity360Lweb01.summary.jdbc.url=*******************************************************************
clarity360Lweb01.summary.jdbc.username=actoniacdb
clarity360Lweb01.summary.jdbc.password=0%z=Kft99003k#Y6

## clickhouse - clarity360 summary 02
clarity360Lweb02.summary.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
clarity360Lweb02.summary.jdbc.url=***********************************************************************
clarity360Lweb02.summary.jdbc.username=actoniacdb
clarity360Lweb02.summary.jdbc.password=0%z=Kft99003k#Y6

## clickhouse - clarity360 summary 03
clarity360Lweb03.summary.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
clarity360Lweb03.summary.jdbc.url=***********************************************************************
clarity360Lweb03.summary.jdbc.username=actoniacdb
clarity360Lweb03.summary.jdbc.password=0%z=Kft99003k#Y6

## clickhouse - clarity360 summary 04
clarity360Lweb04.summary.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
clarity360Lweb04.summary.jdbc.url=*********************************************************************
clarity360Lweb04.summary.jdbc.username=actoniacdb
clarity360Lweb04.summary.jdbc.password=0%z=Kft99003k#Y6

## clickhouse - clarity360 summary 05
clarity360Lweb05.summary.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
clarity360Lweb05.summary.jdbc.url=************************************************************************
clarity360Lweb05.vector.jdbc.url=***********************************************************************
clarity360Lweb05.summary.jdbc.username=actoniacdb
clarity360Lweb05.summary.jdbc.password=0%z=Kft99003k#Y6


## clickhouse - clarity360 summary merge 01
clarity360LwebMerge01.summary.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
clarity360LwebMerge01.summary.jdbc.url=***********************************************************************
clarity360LwebMerge01.summary.jdbc.username=actoniacdb
clarity360LwebMerge01.summary.jdbc.password=0%z=Kft99003k#Y6

## clickhouse - clarity360 summary merge 02
clarity360LwebMerge02.summary.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
clarity360LwebMerge02.summary.jdbc.url=*************************************************************************
clarity360LwebMerge02.summary.jdbc.username=actoniacdb
clarity360LwebMerge02.summary.jdbc.password=0%z=Kft99003k#Y6

## clickhouse - clarity360 summary merge 03
clarity360LwebMerge03.summary.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
clarity360LwebMerge03.summary.jdbc.url=***********************************************************************
clarity360LwebMerge03.summary.jdbc.username=actoniacdb
clarity360LwebMerge03.summary.jdbc.password=0%z=Kft99003k#Y6

## clickhouse - clarity360 summary merge 04
clarity360LwebMerge04.summary.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
clarity360LwebMerge04.summary.jdbc.url=***********************************************************************
clarity360LwebMerge04.summary.jdbc.username=actoniacdb
clarity360LwebMerge04.summary.jdbc.password=0%z=Kft99003k#Y6

## clickhouse - clarity360 summary merge 05
clarity360LwebMerge05.summary.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
clarity360LwebMerge05.summary.jdbc.url=*************************************************************************
clarity360LwebMerge05.summary.jdbc.username=actoniacdb
clarity360LwebMerge05.summary.jdbc.password=0%z=Kft99003k#Y6


## clickhouse - adhoc sv
claritydb.adhoc.sv.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.adhoc.sv.jdbc.url=**********************************************
claritydb.adhoc.sv.jdbc.username=actoniacdb
claritydb.adhoc.sv.jdbc.password=0%z=Kft99003k#Y6

## clickhouse - suggest keyword
claritydb.suggest.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.suggest.keyword.jdbc.url=***********************************************************
claritydb.suggest.keyword.jdbc.username=actoniacdb
claritydb.suggest.keyword.jdbc.password=0%z=Kft99003k#Y6

## clickhouse - google suggest keyword(RI cluster)
claritydb.google.suggest.keyword.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.google.suggest.keyword.jdbc.url=*****************************************************
claritydb.google.suggest.keyword.jdbc.username=default
claritydb.google.suggest.keyword.jdbc.password=clarity99!

## clickhouse - ppc
claritydb.ppc.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.ppc.jdbc.url=*************************************************************
claritydb.ppc.jdbc.username=default
claritydb.ppc.jdbc.password=clarity99!

## clickhouse - ppc cluster b
claritydb.ppcclusterb.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.ppcclusterb.jdbc.url=****************************************************************
claritydb.ppcclusterb.jdbc.username=default
claritydb.ppcclusterb.jdbc.password=clarity99!

##
thg.gsc.click.steam.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
thg.gsc.click.steam.jdbc.url=******************************************************
thg.gsc.click.steam.jdbc.username=default
thg.gsc.click.steam.jdbc.password=clarity99!


## clickhouse - default
claritydb.default.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.default.jdbc.url=********************************************
claritydb.default.jdbc.username=default
claritydb.default.jdbc.password=clarity99!


## clickhouse - gsc click steam
claritydb.gsc.click.steam.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.gsc.click.steam.jdbc.url=****************************************************
claritydb.gsc.click.steam.jdbc.username=default
claritydb.gsc.click.steam.jdbc.password=clarity99!

## actonia_monitor
actonia.monitor.jdbc.driver=com.mysql.cj.jdbc.Driver
actonia.monitor.jdbc.url=***********************************************************************************************
actonia.monitor.jdbc.username=shinetech
actonia.monitor.jdbc.password=%nKdm=pbjACu

## actonia_sitemap
sitemap.jdbc.driver=com.mysql.cj.jdbc.Driver
sitemap.jdbc.url=***********************************************************************************************
sitemap.jdbc.username=shinetech
sitemap.jdbc.password=%nKdm=pbjACu


## monthly_ranking RG
claritydb.monthlyRankingRG.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.monthlyRankingRG.jdbc.url=******************************************************
claritydb.lweb.monthlyRankingRG.jdbc.url=****************************************************
claritydb.monthlyRankingRG.jdbc.username=default
claritydb.monthlyRankingRG.jdbc.password=clarity99!

## daily ranking qc
claritydb.rankingQc.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.rankingQc.jdbc.url=*********************************************
claritydb.rankingQc.jdbc.username=default
claritydb.rankingQc.jdbc.password=clarity99!

## clickhouse kp hot 201
claritydb.newri.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.newri.jdbc.url=*********************************************************************
claritydb.newri.jdbc.username=default
claritydb.newri.jdbc.password=clarity99!

## clickhouse central02
claritydb.central.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.central.jdbc.url=*******************************************
claritydb.central.jdbc.username=default
claritydb.central.jdbc.password=clarity99!

## site-analytics-01
cdb.site.analytics1.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
cdb.site.analytics1.jdbc.url=*******************************************
cdb.site.analytics1.jdbc.username=default
cdb.site.analytics1.jdbc.password=clarity99!

## site-analytics-02
cdb.site.analytics2.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
cdb.site.analytics2.jdbc.url=**********************************************
cdb.site.analytics2.username=default
cdb.site.analytics2.password=clarity99!

## site-analytics-03
cdb.site.analytics3.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
cdb.site.analytics3.jdbc.url=*********************************************
cdb.site.analytics3.jdbc.username=default
cdb.site.analytics3.jdbc.password=clarity99!

## site-analytics-04
cdb.site.analytics4.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
cdb.site.analytics4.jdbc.url=*******************************************
cdb.site.analytics4.jdbc.username=default
cdb.site.analytics4.jdbc.password=clarity99!

##  clickhouse keyword_ranking
claritydb.keywordRanking.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.keywordRanking.jdbc.url=****************************************************
claritydb.keywordRanking.jdbc.username=default
claritydb.keywordRanking.jdbc.password=clarity99!

## clickhouse s3 daily ranking file detail
claritydb.s3fileDetail.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.s3fileDetail.jdbc.url=******************************************
claritydb.s3fileDetail.jdbc.username=default
claritydb.s3fileDetail.jdbc.password=clarity99!

## ri07 keyword_searchvolume
claritydb.ri07.keyword.searchvolume.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.ri07.keyword.searchvolume.jdbc.url=*******************************************************
claritydb.ri07.keyword.searchvolume.jdbc.username=default
claritydb.ri07.keyword.searchvolume.jdbc.password=clarity99!

## site clarity
claritydb.siteclarity.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.siteclarity.jdbc.url=******************************************
claritydb.siteclarity.jdbc.username=default
claritydb.siteclarity.jdbc.password=clarity99!
## site prod
claritydb.prodclarity.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.prodclarity.jdbc.url=******************************************************************
claritydb.prodclarity.jdbc.username=default
claritydb.prodclarity.jdbc.password=clarity99!

## site prod -> server12
claritydb.prodclarity12.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.prodclarity12.jdbc.url=****************************************************************
claritydb.prodclarity12.jdbc.username=default
claritydb.prodclarity12.jdbc.password=clarity99!

## site prod -> server3
claritydb.prodclarity3.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.prodclarity3.jdbc.url=*****************************************************************
claritydb.prodclarity3.jdbc.username=default
claritydb.prodclarity3.jdbc.password=clarity99!

## site clarity, java.sql.st
database=prod
db_user=default
db_pass=clarity99!
db_url=*************************************/
jdbc_driver=ru.yandex.clickhouse.ClickHouseDriver

## clickhouse - pixel height ranking
claritydb.pixel.ved.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.pixel.ved.jdbc.url=*************************************************
claritydb.pixel.ved.jdbc.username=default
claritydb.pixel.ved.jdbc.password=clarity99!

## clickhouse - keyword expand
claritydb.keyword.click.steam.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.keyword.click.steam.jdbc.url=****************************************************
claritydb.keyword.click.steam.jdbc.username=default
claritydb.keyword.click.steam.jdbc.password=clarity99!

## clickhouse - adhoc ranking
claritydb.keywordcluster.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.keywordcluster.jdbc.url=*****************************************************
claritydb.keywordcluster.jdbc.username=actoniacdb
claritydb.keywordcluster.jdbc.password=0%z=Kft99003k#Y6

claritydb.lwebkeywordsearchvolume.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.lwebkeywordsearchvolume.jdbc.url=*********************************************************
claritydb.lwebkeywordsearchvolume.jdbc.username=default
claritydb.lwebkeywordsearchvolume.jdbc.password=clarity99!

claritydb.cdb21bot.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.cdb21bot.jdbc.url=************************************************
claritydb.cdb21bot.jdbc.username=default
claritydb.cdb21bot.jdbc.password=clarity99!

## clickhouse - monthly video ranking
claritydb.monthlyvideoranking.rgch.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.monthlyvideoranking.rgch.jdbc.url=**********************************************************
claritydb.monthlyvideoranking.rgch.jdbc.username=default
claritydb.monthlyvideoranking.rgch.jdbc.password=clarity99!


## clickhouse - poc02
claritydb.poc02.default.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.poc02.default.jdbc.url=********************************************
claritydb.poc02.default.jdbc.username=default
claritydb.poc02.default.jdbc.password=clarity99!

## nj114 keyword_searchvolume
claritydb.nj114.keyword.searchvolume.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.nj114.keyword.searchvolume.jdbc.url=***********************************************************
claritydb.nj114.keyword.searchvolume.jdbc.username=default
claritydb.nj114.keyword.searchvolume.jdbc.password=clarity99!

## clickhouse - adhoc ranking
claritydb.cloudflare.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.cloudflare.jdbc.url=************************************************
claritydb.cloudflare.jdbc.username=actoniacdb
claritydb.cloudflare.jdbc.password=0%z=Kft99003k#Y6


lweb.rankcheck01.jdbc.driver=com.mysql.cj.jdbc.Driver
lweb.rankcheck01.jdbc.url=**************************************************************************************************************
lweb.rankcheck01.jdbc.username=lwebrc_writer
lweb.rankcheck01.jdbc.password=%nKdm=pbjACu

## clickhouse - gsc/realtime_url
claritydb.realtime_url.jdbc.driver=ru.yandex.clickhouse.ClickHouseDriver
claritydb.realtime_url.jdbc.url=***********************************************************************
claritydb.realtime_url.jdbc.username=default
claritydb.realtime_url.jdbc.password=clarity99!