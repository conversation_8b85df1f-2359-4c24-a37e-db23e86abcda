<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="
          http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
          http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
          http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd
          http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">

	<bean id="stat-filter" class="com.alibaba.druid.filter.stat.StatFilter">
		<property name="slowSqlMillis" value="600000" />
		<property name="logSlowSql" value="true" />
		<property name="slowSqlLogLevel" value="error" />
		<property name="mergeSql" value="false" />
	</bean>

	<bean id="actoniaDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		destroy-method="close" >
		<property name="driverClassName" value="${jdbc.driver}" />
		<property name="url" value="${jdbc.url}" />
		<property name="username" value="${jdbc.username}" />
		<property name="password" value="${jdbc.password}" />
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select NOW() " />
		<property name="socketTimeout" value="180000"/>
		<property name="connectTimeout" value="180000"/>
		<property name="filters" value="com.alibaba.druid.filter.mysql8datetime.MySQL8DateTimeSqlTypeFilter"/>
		<property name="maxActive" value="16" />
		<property name="minIdle" value="2" />
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<property name="minEvictableIdleTimeMillis" value="400000"/>
		<property name="maxEvictableIdleTimeMillis" value="490000"/>
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />
		<property name="testWhileIdle" value="true" />
		<property name="keepAlive" value="true" />
		<property name="proxyFilters">
			<list>
				<ref bean="stat-filter" />
			</list>
		</property>
		<property name="timeBetweenLogStatsMillis" value="300000" />
	</bean>

	<bean id="actoniaApiDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		destroy-method="close" >
		<property name="driverClassName" value="${api.jdbc.driver}" />
		<property name="url" value="${api.jdbc.url}" />
		<property name="username" value="${api.jdbc.username}" />
		<property name="password" value="${api.jdbc.password}" />
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select NOW() " />
		<property name="socketTimeout" value="180000"/>
		<property name="connectTimeout" value="180000"/>
		<property name="filters" value="com.alibaba.druid.filter.mysql8datetime.MySQL8DateTimeSqlTypeFilter"/>
		<property name="maxActive" value="16" />
		<property name="minIdle" value="2" />
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<property name="minEvictableIdleTimeMillis" value="400000"/>
		<property name="maxEvictableIdleTimeMillis" value="490000"/>
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />
		<property name="testWhileIdle" value="true" />
		<property name="keepAlive" value="true" />
		<property name="proxyFilters">
			<list>
				<ref bean="stat-filter" />
			</list>
		</property>
		<property name="timeBetweenLogStatsMillis" value="300000" />
	</bean>

	<bean id="actoniaBotDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close" >
		<property name="driverClassName" value="${jdbc.bot.driver}" />
		<property name="url" value="${jdbc.bot.url}" />
		<property name="username" value="${jdbc.bot.username}" />
		<property name="password" value="${jdbc.bot.password}" />
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select NOW() " />
		<property name="socketTimeout" value="180000"/>
		<property name="connectTimeout" value="180000"/>
		<property name="filters" value="com.alibaba.druid.filter.mysql8datetime.MySQL8DateTimeSqlTypeFilter"/>
		<property name="maxActive" value="16" />
		<property name="minIdle" value="2" />
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<property name="minEvictableIdleTimeMillis" value="400000"/>
		<property name="maxEvictableIdleTimeMillis" value="490000"/>
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />
		<property name="testWhileIdle" value="true" />
		<property name="keepAlive" value="true" />
		<property name="proxyFilters">
			<list>
				<ref bean="stat-filter" />
			</list>
		</property>
		<property name="timeBetweenLogStatsMillis" value="300000" />
	</bean>

	<bean id="rankCheckAPIDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		destroy-method="close" >
		<property name="driverClassName" value="${rankcheck.jdbc.driver}" />
		<property name="url" value="${rankcheck.jdbc.url}" />
		<property name="username" value="${rankcheck.jdbc.username}" />
		<property name="password" value="${rankcheck.jdbc.password}" />
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select NOW() " />
		<property name="socketTimeout" value="180000"/>
		<property name="connectTimeout" value="180000"/>
		<property name="filters" value="com.alibaba.druid.filter.mysql8datetime.MySQL8DateTimeSqlTypeFilter"/>
		<property name="maxActive" value="16" />
		<property name="minIdle" value="2" />
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<property name="minEvictableIdleTimeMillis" value="180000"/>
		<property name="maxEvictableIdleTimeMillis" value="190000"/>
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />
		<property name="testWhileIdle" value="true" />
		<property name="keepAlive" value="true" />
		<property name="proxyFilters">
			<list>
				<ref bean="stat-filter" />
			</list>
		</property>
		<property name="timeBetweenLogStatsMillis" value="300000" />
	</bean>

	<bean id="rankCheckBackupDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		destroy-method="close" >
		<property name="driverClassName" value="${rankcheck.backup.jdbc.driver}" />
		<property name="url" value="${rankcheck.backup.jdbc.url}" />
		<property name="username" value="${rankcheck.backup.jdbc.username}" />
		<property name="password" value="${rankcheck.backup.jdbc.password}" />
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select NOW() " />
		<property name="socketTimeout" value="180000"/>
		<property name="connectTimeout" value="180000"/>
		<property name="filters" value="com.alibaba.druid.filter.mysql8datetime.MySQL8DateTimeSqlTypeFilter"/>
		<property name="maxActive" value="16" />
		<property name="minIdle" value="2" />
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<property name="minEvictableIdleTimeMillis" value="180000"/>
		<property name="maxEvictableIdleTimeMillis" value="190000"/>
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />
		<property name="testWhileIdle" value="true" />
		<property name="keepAlive" value="true" />
		<property name="proxyFilters">
			<list>
				<ref bean="stat-filter" />
			</list>
		</property>
		<property name="timeBetweenLogStatsMillis" value="300000" />
	</bean>

    <bean id="siteMapDataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close" >
        <property name="driverClassName" value="${sitemap.jdbc.driver}"/>
		<property name="url" value="${sitemap.jdbc.url}"/>
		<property name="username" value="${sitemap.jdbc.username}"/>
		<property name="password" value="${sitemap.jdbc.password}"/>
		<property name="initialSize" value="2"/>
		<property name="validationQuery" value="select NOW() " />
		<property name="socketTimeout" value="180000"/>
		<property name="connectTimeout" value="180000"/>
		<property name="filters" value="com.alibaba.druid.filter.mysql8datetime.MySQL8DateTimeSqlTypeFilter"/>
		<property name="maxActive" value="16" />
		<property name="minIdle" value="2" />
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<property name="minEvictableIdleTimeMillis" value="400000"/>
		<property name="maxEvictableIdleTimeMillis" value="490000"/>
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />
		<property name="testWhileIdle" value="true" />
		<property name="keepAlive" value="true" />
		<property name="proxyFilters">
			<list>
				<ref bean="stat-filter" />
			</list>
		</property>
		<property name="timeBetweenLogStatsMillis" value="300000" />
    </bean>

	<bean id="clarityDBDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		destroy-method="close">
		<property name="driverClassName" value="${claritydb.jdbc.driver}" />
		<property name="url" value="${claritydb.jdbc.url}" />
		<property name="username" value="${claritydb.jdbc.username}" />
		<property name="password" value="${claritydb.jdbc.password}" />
		<property name="connectionProperties">
		<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBAdsDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbads.jdbc.driver}" />
		<property name="url" value="${claritydbads.jdbc.url}" />
		<property name="username" value="${claritydbads.jdbc.username}" />
		<property name="password" value="${claritydbads.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Leo - for bot upload -->
	<bean id="clarityDBBotUploadDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.bot.jdbc.driver}" />
		<property name="url" value="${claritydb.bot.jdbc.url}" />
		<property name="username" value="${claritydb.bot.jdbc.username}" />
		<property name="password" value="${claritydb.bot.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!--for bot query-->
	<bean id="clarityDBBotQueryDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.bot.query.jdbc.driver}" />
		<property name="url" value="${claritydb.bot.query.jdbc.url}" />
		<property name="username" value="${claritydb.bot.query.jdbc.username}" />
		<property name="password" value="${claritydb.bot.query.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Leo - for pageSpeed -->
	<bean id="clarityDBPageSpeedDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.pagespeed.jdbc.driver}" />
		<property name="url" value="${claritydb.pagespeed.jdbc.url}" />
		<property name="username" value="${claritydb.pagespeed.jdbc.username}" />
		<property name="password" value="${claritydb.pagespeed.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Leo - for actonia_ranking_forecast -->
	<bean id="clarityDBRankingForecastDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.rankingforecast.jdbc.driver}" />
		<property name="url" value="${claritydb.rankingforecast.jdbc.url}" />
		<property name="username" value="${claritydb.rankingforecast.jdbc.username}" />
		<property name="password" value="${claritydb.rankingforecast.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Leo - for gsc -->
	<bean id="clarityDBGscDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.gsc.jdbc.driver}" />
		<property name="url" value="${claritydb.gsc.jdbc.url}" />
		<property name="username" value="${claritydb.gsc.jdbc.username}" />
		<property name="password" value="${claritydb.gsc.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBGscStandbyDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.gscstandby.jdbc.driver}" />
		<property name="url" value="${claritydb.gscstandby.jdbc.url}" />
		<property name="username" value="${claritydb.gscstandby.jdbc.username}" />
		<property name="password" value="${claritydb.gscstandby.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Sunny - for content idea -->
	<bean id="clarityDBContentIdeaDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.contentidea.jdbc.driver}" />
		<property name="url" value="${claritydb.contentidea.jdbc.url}" />
		<property name="username" value="${claritydb.contentidea.jdbc.username}" />
		<property name="password" value="${claritydb.contentidea.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3600000;socket_timeout=3600000;dataTransferTimeout=3600000;receive_timeout=3600000;</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBContentIdeaSever2DataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.contentidea.jdbc.driver}" />
		<property name="url" value="${claritydb.contentidea2.jdbc.url}" />
		<property name="username" value="${claritydb.contentidea.jdbc.username}" />
		<property name="password" value="${claritydb.contentidea.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3600000;socket_timeout=3600000;dataTransferTimeout=3600000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBContentIdeaSever3DataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.contentidea.jdbc.driver}" />
		<property name="url" value="${claritydb.contentidea3.jdbc.url}" />
		<property name="username" value="${claritydb.contentidea.jdbc.username}" />
		<property name="password" value="${claritydb.contentidea.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3600000;socket_timeout=3600000;dataTransferTimeout=3600000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBContentIdeaSever4DataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.contentidea.jdbc.driver}" />
		<property name="url" value="${claritydb.contentidea4.jdbc.url}" />
		<property name="username" value="${claritydb.contentidea.jdbc.username}" />
		<property name="password" value="${claritydb.contentidea.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3600000;socket_timeout=3600000;dataTransferTimeout=3600000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	 <bean id="clarityDBGaDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.ga.jdbc.driver}" />
		<property name="url" value="${claritydb.ga.jdbc.url}" />
		<property name="username" value="${claritydb.ga.jdbc.username}" />
		<property name="password" value="${claritydb.ga.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	 <bean id="clarityDBGa001DataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.ga.jdbc.driver}" />
		<property name="url" value="${claritydb.ga001.jdbc.url}" />
		<property name="username" value="${claritydb.ga.jdbc.username}" />
		<property name="password" value="${claritydb.ga.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	 <bean id="clarityDBGaGscDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.gagsc.jdbc.driver}" />
		<property name="url" value="${claritydb.gagsc.jdbc.url}" />
		<property name="username" value="${claritydb.gagsc.jdbc.username}" />
		<property name="password" value="${claritydb.gagsc.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>
	 <bean id="clarityDBPpcDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.ppc.jdbc.driver}" />
		<property name="url" value="${claritydb.ppc.jdbc.url}" />
		<property name="username" value="${claritydb.ppc.jdbc.username}" />
		<property name="password" value="${claritydb.ppc.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	 <bean id="clarityDBLinksGaDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.linksga.jdbc.driver}" />
		<property name="url" value="${claritydb.linksga.jdbc.url}" />
		<property name="username" value="${claritydb.linksga.jdbc.username}" />
		<property name="password" value="${claritydb.linksga.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	 <bean id="clarityDBPpcClusterbDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.ppcclusterb.jdbc.driver}" />
		<property name="url" value="${claritydb.ppcclusterb.jdbc.url}" />
		<property name="username" value="${claritydb.ppcclusterb.jdbc.username}" />
		<property name="password" value="${claritydb.ppcclusterb.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Scott - for InternalLink -->
	<bean id="clarityDBInternalLinkDataSourceServer1" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.internallink.jdbc.driver}" />
		<property name="url" value="${claritydb.internallink.server1.jdbc.url}" />
		<property name="username" value="${claritydb.internallink.jdbc.username}" />
		<property name="password" value="${claritydb.internallink.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=24000000;socket_timeout=24000000;dataTransferTimeout=24000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBInternalLinkDataSourceServer2" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.internallink.jdbc.driver}" />
		<property name="url" value="${claritydb.internallink.server2.jdbc.url}" />
		<property name="username" value="${claritydb.internallink.jdbc.username}" />
		<property name="password" value="${claritydb.internallink.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=24000000;socket_timeout=24000000;dataTransferTimeout=24000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBInternalLinkDataSourceServer3" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.internallink.jdbc.driver}" />
		<property name="url" value="${claritydb.internallink.server3.jdbc.url}" />
		<property name="username" value="${claritydb.internallink.jdbc.username}" />
		<property name="password" value="${claritydb.internallink.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=24000000;socket_timeout=24000000;dataTransferTimeout=24000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- new internal cluster -->

	<bean id="clarityDBInternalLinkNewClusterDataSourceServer1" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.internallink.jdbc.driver}" />
		<property name="url" value="${claritydb.internallink.newcluster.server1.jdbc.url}" />
		<property name="username" value="${claritydb.internallink.jdbc.username}" />
		<property name="password" value="${claritydb.internallink.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=24000000;socket_timeout=24000000;dataTransferTimeout=24000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBInternalLinkNewClusterDataSourceServer2" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.internallink.jdbc.driver}" />
		<property name="url" value="${claritydb.internallink.newcluster.server2.jdbc.url}" />
		<property name="username" value="${claritydb.internallink.jdbc.username}" />
		<property name="password" value="${claritydb.internallink.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=24000000;socket_timeout=24000000;dataTransferTimeout=24000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBInternalLinkNewClusterDataSourceServer3" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.internallink.jdbc.driver}" />
		<property name="url" value="${claritydb.internallink.newcluster.server3.jdbc.url}" />
		<property name="username" value="${claritydb.internallink.jdbc.username}" />
		<property name="password" value="${claritydb.internallink.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=24000000;socket_timeout=24000000;dataTransferTimeout=24000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBInternalLinkNewClusterDataSourceServer4" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.internallink.jdbc.driver}" />
		<property name="url" value="${claritydb.internallink.newcluster.server4.jdbc.url}" />
		<property name="username" value="${claritydb.internallink.jdbc.username}" />
		<property name="password" value="${claritydb.internallink.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=24000000;socket_timeout=24000000;dataTransferTimeout=24000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBInternalLinkNewClusterDataSourceServer5" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.internallink.jdbc.driver}" />
		<property name="url" value="${claritydb.internallink.newcluster.server5.jdbc.url}" />
		<property name="username" value="${claritydb.internallink.jdbc.username}" />
		<property name="password" value="${claritydb.internallink.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=24000000;socket_timeout=24000000;dataTransferTimeout=24000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Leo - for gsc
	<bean id="clarityDBGscOldDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.gsc.old.jdbc.driver}" />
		<property name="url" value="${claritydb.gsc.old.jdbc.url}" />
		<property name="username" value="${claritydb.gsc.old.jdbc.username}" />
		<property name="password" value="${claritydb.gsc.old.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>-->

	<!-- Meo test for search visibility -->
	<bean id="clarityDBMonthlyTestDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.monthly.test.jdbc.driver}" />
		<property name="url" value="${claritydb.monthly.test.jdbc.url}" />
		<property name="username" value="${claritydb.monthly.test.jdbc.username}" />
		<property name="password" value="${claritydb.monthly.test.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

    <bean id="mdb001MonitorDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		destroy-method="close" >
		<property name="driverClassName" value="${mdb001.jdbc.driver}" />
		<property name="url" value="${mdb001.jdbc.url}" />
		<property name="username" value="${mdb001.jdbc.username}" />
		<property name="password" value="${mdb001.jdbc.password}" />
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select NOW() " />
		<property name="socketTimeout" value="180000"/>
		<property name="connectTimeout" value="180000"/>
		<property name="filters" value="com.alibaba.druid.filter.mysql8datetime.MySQL8DateTimeSqlTypeFilter"/>
		<property name="maxActive" value="16" />
		<property name="minIdle" value="2" />
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<property name="minEvictableIdleTimeMillis" value="400000"/>
		<property name="maxEvictableIdleTimeMillis" value="890000"/>
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />
		<property name="testWhileIdle" value="true" />
		<property name="keepAlive" value="true" />
		<property name="proxyFilters">
			<list>
				<ref bean="stat-filter" />
			</list>
		</property>
		<property name="timeBetweenLogStatsMillis" value="300000" />
	</bean>

	<bean id="mdb001ActoniaDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close" >
		<property name="driverClassName" value="${mdb001.actonia.jdbc.driver}" />
		<property name="url" value="${mdb001.actonia.jdbc.url}" />
		<property name="username" value="${mdb001.actonia.jdbc.username}" />
		<property name="password" value="${mdb001.actonia.jdbc.password}" />
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select NOW() " />
		<property name="socketTimeout" value="180000"/>
		<property name="connectTimeout" value="180000"/>
		<property name="filters" value="com.alibaba.druid.filter.mysql8datetime.MySQL8DateTimeSqlTypeFilter"/>
		<property name="maxActive" value="16" />
		<property name="minIdle" value="2" />
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<property name="minEvictableIdleTimeMillis" value="400000"/>
		<property name="maxEvictableIdleTimeMillis" value="890000"/>
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />
		<property name="testWhileIdle" value="true" />
		<property name="keepAlive" value="true" />
		<property name="proxyFilters">
			<list>
				<ref bean="stat-filter" />
			</list>
		</property>
		<property name="timeBetweenLogStatsMillis" value="300000" />
	</bean>

	<!-- Jason - for monthly ranking -->
	<bean id="clarityDBMonthlyRankingDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.monthlyranking.jdbc.driver}" />
		<property name="url" value="${claritydb.monthlyranking.jdbc.url}" />
		<property name="username" value="${claritydb.monthlyranking.jdbc.username}" />
		<property name="password" value="${claritydb.monthlyranking.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Leo - for monthly video ranking -->
	<bean id="clarityDBMonthlyVideoRankingDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.monthlyvideoranking.jdbc.driver}" />
		<property name="url" value="${claritydb.monthlyvideoranking.jdbc.url}" />
		<property name="username" value="${claritydb.monthlyvideoranking.jdbc.username}" />
		<property name="password" value="${claritydb.monthlyvideoranking.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>
	<bean id="clarityDBMonthlyVideoRankingBackupDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.monthlyvideorankingbackup.jdbc.driver}" />
		<property name="url" value="${claritydb.monthlyvideorankingbackup.jdbc.url}" />
		<property name="username" value="${claritydb.monthlyvideorankingbackup.jdbc.username}" />
		<property name="password" value="${claritydb.monthlyvideorankingbackup.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>
	<bean id="clarityDBMonthlyVideoRankingRGCHDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.monthlyvideoranking.rgch.jdbc.driver}" />
		<property name="url" value="${claritydb.monthlyvideoranking.rgch.jdbc.url}" />
		<property name="username" value="${claritydb.monthlyvideoranking.rgch.jdbc.username}" />
		<property name="password" value="${claritydb.monthlyvideoranking.rgch.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- GoogleSuggestKeyword -->
	<bean id="clarityDBGoogleSuggestKeywordUploadDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.keywordTokenizer.sv.lweb212.jdbc.driver}" />
		<property name="url" value="${claritydb.keywordTokenizer.sv.lweb212.jdbc.url}" />
		<property name="username" value="${claritydb.keywordTokenizer.sv.lweb212.jdbc.username}" />
		<property name="password" value="${claritydb.keywordTokenizer.sv.lweb212.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Meo - monthly keyword tokenizer -->
	<bean id="clarityDBKeywordTokenizerDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.keywordTokenizer.sv.jdbc.driver}" />
		<property name="url" value="${claritydb.keywordTokenizer.sv.jdbc.url}" />
		<property name="username" value="${claritydb.keywordTokenizer.sv.jdbc.username}" />
		<property name="password" value="${claritydb.keywordTokenizer.sv.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

<bean id="clarityDBKeywordTokenizerDataSourceLWeb" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.keywordTokenizer.sv.lweb.jdbc.driver}" />
		<property name="url" value="${claritydb.keywordTokenizer.sv.lweb.jdbc.url}" />
		<property name="username" value="${claritydb.keywordTokenizer.sv.lweb.jdbc.username}" />
		<property name="password" value="${claritydb.keywordTokenizer.sv.lweb.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBKeywordTokenizerDataSourceLWeb2" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.keywordTokenizer.sv.lweb2.jdbc.driver}" />
		<property name="url" value="${claritydb.keywordTokenizer.sv.lweb2.jdbc.url}" />
		<property name="username" value="${claritydb.keywordTokenizer.sv.lweb2.jdbc.username}" />
		<property name="password" value="${claritydb.keywordTokenizer.sv.lweb2.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBDirtyKeywordParentAndChildDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.dirty.keyword.pc.jdbc.driver}" />
		<property name="url" value="${claritydb.dirty.keyword.pc.jdbc.url}" />
		<property name="username" value="${claritydb.dirty.keyword.pc.jdbc.username}" />
		<property name="password" value="${claritydb.dirty.keyword.pc.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Leo - for pixel height ranking -->
	<bean id="clarityDBPixelHeightRankingDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.pixel.jdbc.driver}" />
		<property name="url" value="${claritydb.pixel.jdbc.url}" />
		<property name="username" value="${claritydb.pixel.jdbc.username}" />
		<property name="password" value="${claritydb.pixel.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Alex - for youtube ranking -->
<!--	<bean id="clarityDBYoutubeRankingDataSourceCdbCs005" class="com.alibaba.druid.pool.DruidDataSource"-->
<!--		  destroy-method="close">-->
<!--		<property name="driverClassName" value="${claritydbCdbCs005.youtube.jdbc.driver}" />-->
<!--		<property name="url" value="${claritydbCdbCs005.youtube.jdbc.url}" />-->
<!--		<property name="username" value="${claritydbCdbCs005.youtube.jdbc.username}" />-->
<!--		<property name="password" value="${claritydbCdbCs005.youtube.jdbc.password}" />-->
<!--		<property name="connectionProperties">-->
<!--			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>-->
<!--		</property>-->
<!--		<property name="initialSize" value="2" />-->
<!--		<property name="validationQuery" value="select 1" />-->
<!--	</bean>-->


	<bean id="clarityDBYoutubeRankingDataSourceCdbRi307" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRi307.youtube.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRi307.youtube.jdbc.url}" />
		<property name="username" value="${claritydbCdbRi307.youtube.jdbc.username}" />
		<property name="password" value="${claritydbCdbRi307.youtube.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBKeywordDataSourceCdbRi201" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRi201.keyword.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRi201.keyword.jdbc.url}" />
		<property name="username" value="${claritydbCdbRi201.keyword.jdbc.username}" />
		<property name="password" value="${claritydbCdbRi201.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBKeywordDataSourceCdbRi202" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRi202.keyword.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRi202.keyword.jdbc.url}" />
		<property name="username" value="${claritydbCdbRi202.keyword.jdbc.username}" />
		<property name="password" value="${claritydbCdbRi202.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBKeywordDataSourceCdbRi203" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRi203.keyword.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRi203.keyword.jdbc.url}" />
		<property name="username" value="${claritydbCdbRi203.keyword.jdbc.username}" />
		<property name="password" value="${claritydbCdbRi203.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>


	<bean id="clarityDBKeywordDataSourceCdbRi204" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRi204.keyword.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRi204.keyword.jdbc.url}" />
		<property name="username" value="${claritydbCdbRi204.keyword.jdbc.username}" />
		<property name="password" value="${claritydbCdbRi204.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>
	<bean id="clarityDBKeywordDataSourceCdbRi205" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRi205.keyword.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRi205.keyword.jdbc.url}" />
		<property name="username" value="${claritydbCdbRi205.keyword.jdbc.username}" />
		<property name="password" value="${claritydbCdbRi205.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>
	<bean id="clarityDBKeywordDataSourceCdbRi206" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRi206.keyword.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRi206.keyword.jdbc.url}" />
		<property name="username" value="${claritydbCdbRi206.keyword.jdbc.username}" />
		<property name="password" value="${claritydbCdbRi206.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>


	<bean id="clarityDBKeywordDataSourceCdbRi207" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRi207.keyword.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRi207.keyword.jdbc.url}" />
		<property name="username" value="${claritydbCdbRi207.keyword.jdbc.username}" />
		<property name="password" value="${claritydbCdbRi207.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

		<bean id="clarityDB360SummaryDataSourceCdb001" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${clarity360Lweb01.summary.jdbc.driver}" />
		<property name="url" value="${clarity360Lweb01.summary.jdbc.url}" />
		<property name="username" value="${clarity360Lweb01.summary.jdbc.username}" />
		<property name="password" value="${clarity360Lweb01.summary.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDB360SummaryDataSourceCdb002" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${clarity360Lweb02.summary.jdbc.driver}" />
		<property name="url" value="${clarity360Lweb02.summary.jdbc.url}" />
		<property name="username" value="${clarity360Lweb02.summary.jdbc.username}" />
		<property name="password" value="${clarity360Lweb02.summary.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDB360SummaryDataSourceCdb003" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${clarity360Lweb03.summary.jdbc.driver}" />
		<property name="url" value="${clarity360Lweb03.summary.jdbc.url}" />
		<property name="username" value="${clarity360Lweb03.summary.jdbc.username}" />
		<property name="password" value="${clarity360Lweb03.summary.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDB360SummaryDataSourceCdb004" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${clarity360Lweb04.summary.jdbc.driver}" />
		<property name="url" value="${clarity360Lweb04.summary.jdbc.url}" />
		<property name="username" value="${clarity360Lweb04.summary.jdbc.username}" />
		<property name="password" value="${clarity360Lweb04.summary.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDB360SummaryDataSourceCdb005" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${clarity360Lweb05.summary.jdbc.driver}" />
		<property name="url" value="${clarity360Lweb05.summary.jdbc.url}" />
		<property name="username" value="${clarity360Lweb05.summary.jdbc.username}" />
		<property name="password" value="${clarity360Lweb05.summary.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3600000;socket_timeout=3600000;dataTransferTimeout=3600000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDB360SummaryDataSourceCdbMerge001" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${clarity360LwebMerge01.summary.jdbc.driver}" />
		<property name="url" value="${clarity360LwebMerge01.summary.jdbc.url}" />
		<property name="username" value="${clarity360LwebMerge01.summary.jdbc.username}" />
		<property name="password" value="${clarity360LwebMerge01.summary.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3600000;socket_timeout=3600000;dataTransferTimeout=3600000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDB360SummaryDataSourceCdbMerge002" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${clarity360LwebMerge02.summary.jdbc.driver}" />
		<property name="url" value="${clarity360LwebMerge02.summary.jdbc.url}" />
		<property name="username" value="${clarity360LwebMerge02.summary.jdbc.username}" />
		<property name="password" value="${clarity360LwebMerge02.summary.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3600000;socket_timeout=3600000;dataTransferTimeout=3600000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDB360SummaryDataSourceCdbMerge003" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${clarity360LwebMerge03.summary.jdbc.driver}" />
		<property name="url" value="${clarity360LwebMerge03.summary.jdbc.url}" />
		<property name="username" value="${clarity360LwebMerge03.summary.jdbc.username}" />
		<property name="password" value="${clarity360LwebMerge03.summary.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3600000;socket_timeout=3600000;dataTransferTimeout=3600000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDB360SummaryDataSourceCdbMerge004" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${clarity360LwebMerge04.summary.jdbc.driver}" />
		<property name="url" value="${clarity360LwebMerge04.summary.jdbc.url}" />
		<property name="username" value="${clarity360LwebMerge04.summary.jdbc.username}" />
		<property name="password" value="${clarity360LwebMerge04.summary.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3600000;socket_timeout=3600000;dataTransferTimeout=3600000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDB360SummaryDataSourceCdbMerge005" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${clarity360LwebMerge05.summary.jdbc.driver}" />
		<property name="url" value="${clarity360LwebMerge05.summary.jdbc.url}" />
		<property name="username" value="${clarity360LwebMerge05.summary.jdbc.username}" />
		<property name="password" value="${clarity360LwebMerge05.summary.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3600000;socket_timeout=3600000;dataTransferTimeout=3600000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBVectorDataSourceCdb005" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${clarity360Lweb05.summary.jdbc.driver}" />
		<property name="url" value="${clarity360Lweb05.vector.jdbc.url}" />
		<property name="username" value="${clarity360Lweb05.summary.jdbc.username}" />
		<property name="password" value="${clarity360Lweb05.summary.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>



	<bean id="clarityDBKeywordDataSourceCdbRiNj001" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRiNj001.keyword.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRiNj001.keyword.jdbc.url}" />
		<property name="username" value="${claritydbCdbRiNj001.keyword.jdbc.username}" />
		<property name="password" value="${claritydbCdbRiNj001.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBKeywordDataSourceCdbRiNj002" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRiNj002.keyword.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRiNj002.keyword.jdbc.url}" />
		<property name="username" value="${claritydbCdbRiNj002.keyword.jdbc.username}" />
		<property name="password" value="${claritydbCdbRiNj002.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBKeywordDataSourceCdbRiNj003" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRiNj003.keyword.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRiNj003.keyword.jdbc.url}" />
		<property name="username" value="${claritydbCdbRiNj003.keyword.jdbc.username}" />
		<property name="password" value="${claritydbCdbRiNj003.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBKeywordDataSourceCdbRiNj004" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRiNj004.keyword.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRiNj004.keyword.jdbc.url}" />
		<property name="username" value="${claritydbCdbRiNj004.keyword.jdbc.username}" />
		<property name="password" value="${claritydbCdbRiNj004.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBKeywordDataSourceCdbRiNj005" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRiNj005.keyword.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRiNj005.keyword.jdbc.url}" />
		<property name="username" value="${claritydbCdbRiNj005.keyword.jdbc.username}" />
		<property name="password" value="${claritydbCdbRiNj005.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBKeywordDataSourceCdbRiNj006" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRiNj006.keyword.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRiNj006.keyword.jdbc.url}" />
		<property name="username" value="${claritydbCdbRiNj006.keyword.jdbc.username}" />
		<property name="password" value="${claritydbCdbRiNj006.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBKeywordDataSourceCdbRiNj007" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRiNj007.keyword.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRiNj007.keyword.jdbc.url}" />
		<property name="username" value="${claritydbCdbRiNj007.keyword.jdbc.username}" />
		<property name="password" value="${claritydbCdbRiNj007.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>


	<bean id="clarityDBYoutubeRankingDataSourceCdbRi07" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydbCdbRi07.youtube.jdbc.driver}" />
		<property name="url" value="${claritydbCdbRi07.youtube.jdbc.url}" />
		<property name="username" value="${claritydbCdbRi07.youtube.jdbc.username}" />
		<property name="password" value="${claritydbCdbRi07.youtube.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Sunny - for similar keywords -->
	<bean id="clarityDBSimilarKeywordDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.similarkeywords.jdbc.driver}" />
		<property name="url" value="${claritydb.similarkeywords.jdbc.url}" />
		<property name="username" value="${claritydb.similarkeywords.jdbc.username}" />
		<property name="password" value="${claritydb.similarkeywords.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Jason - for ved -->
	<bean id="clarityDBVedDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.ved.jdbc.driver}" />
		<property name="url" value="${claritydb.ved.jdbc.url}" />
		<property name="username" value="${claritydb.ved.jdbc.username}" />
		<property name="password" value="${claritydb.ved.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Jason - for apple serp extract -->
	<bean id="clarityDBCrawlUrlDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.crawlurl.jdbc.driver}" />
		<property name="url" value="${claritydb.crawlurl.jdbc.url}" />
		<property name="username" value="${claritydb.crawlurl.jdbc.username}" />
		<property name="password" value="${claritydb.crawlurl.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Jason - for adhoc -->
	<bean id="clarityDBAdhocDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.adhoc.jdbc.driver}" />
		<property name="url" value="${claritydb.adhoc.jdbc.url}" />
		<property name="username" value="${claritydb.adhoc.jdbc.username}" />
		<property name="password" value="${claritydb.adhoc.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- kp cold -->
	<bean id="clarityDBColdDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.cold.jdbc.driver}" />
		<property name="url" value="${claritydb.cold.jdbc.url}" />
		<property name="username" value="${claritydb.cold.jdbc.username}" />
		<property name="password" value="${claritydb.cold.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Jason - for adhoc sv-->
	<bean id="clarityDBAdhocSvDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.adhoc.sv.jdbc.driver}" />
		<property name="url" value="${claritydb.adhoc.sv.jdbc.url}" />
		<property name="username" value="${claritydb.adhoc.sv.jdbc.username}" />
		<property name="password" value="${claritydb.adhoc.sv.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBKeywordLiveResearchDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.suggest.keyword.jdbc.driver}" />
		<property name="url" value="${claritydb.suggest.keyword.jdbc.url}" />
		<property name="username" value="${claritydb.suggest.keyword.jdbc.username}" />
		<property name="password" value="${claritydb.suggest.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="googleKeywordSuggestDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.google.suggest.keyword.jdbc.driver}" />
		<property name="url" value="${claritydb.google.suggest.keyword.jdbc.url}" />
		<property name="username" value="${claritydb.google.suggest.keyword.jdbc.username}" />
		<property name="password" value="${claritydb.google.suggest.keyword.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>


	<!-- Cil - for test -->
	<bean id="thgDBGscClickSteamDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${thg.gsc.click.steam.jdbc.driver}" />
		<property name="url" value="${thg.gsc.click.steam.jdbc.url}" />
		<property name="username" value="${thg.gsc.click.steam.jdbc.username}" />
		<property name="password" value="${thg.gsc.click.steam.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3000000;socket_timeout=3000000;dataTransferTimeout=3000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>


	<!-- Jason - for default click steam-->
	<bean id="clarityDB01DefaultDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.default.jdbc.driver}" />
		<property name="url" value="${claritydb.default.jdbc.url}" />
		<property name="username" value="${claritydb.default.jdbc.username}" />
		<property name="password" value="${claritydb.default.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3000000;socket_timeout=3000000;dataTransferTimeout=3000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!-- Jason - for gsc click steam-->
	<bean id="clarityDBGscClickSteamDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.gsc.click.steam.jdbc.driver}" />
		<property name="url" value="${claritydb.gsc.click.steam.jdbc.url}" />
		<property name="username" value="${claritydb.gsc.click.steam.jdbc.username}" />
		<property name="password" value="${claritydb.gsc.click.steam.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3000000;socket_timeout=3000000;dataTransferTimeout=3000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="actoniaMonitorDataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close" >
		<property name="driverClassName" value="${actonia.monitor.jdbc.driver}" />
		<property name="url" value="${actonia.monitor.jdbc.url}" />
		<property name="username" value="${actonia.monitor.jdbc.username}" />
		<property name="password" value="${actonia.monitor.jdbc.password}" />
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select NOW() " />
		<property name="socketTimeout" value="180000"/>
		<property name="connectTimeout" value="180000"/>
		<property name="filters" value="com.alibaba.druid.filter.mysql8datetime.MySQL8DateTimeSqlTypeFilter"/>
		<property name="maxActive" value="16" />
		<property name="minIdle" value="2" />
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<property name="minEvictableIdleTimeMillis" value="400000"/>
		<property name="maxEvictableIdleTimeMillis" value="490000"/>
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />
		<property name="testWhileIdle" value="true" />
		<property name="keepAlive" value="true" />
		<property name="proxyFilters">
			<list>
				<ref bean="stat-filter" />
			</list>
		</property>
		<property name="timeBetweenLogStatsMillis" value="300000" />
	</bean>

	<bean id="monthlyRankingGRDataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close" >
		<property name="driverClassName" value="${claritydb.monthlyRankingRG.jdbc.driver}" />
		<property name="url" value="${claritydb.monthlyRankingRG.jdbc.url}" />
		<property name="username" value="${claritydb.monthlyRankingRG.jdbc.username}" />
		<property name="password" value="${claritydb.monthlyRankingRG.jdbc.password}" />
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="lwebMonthlyRankingGRDataSource" class="com.alibaba.druid.pool.DruidDataSource" destroy-method="close" >
		<property name="driverClassName" value="${claritydb.monthlyRankingRG.jdbc.driver}" />
		<property name="url" value="${claritydb.lweb.monthlyRankingRG.jdbc.url}" />
		<property name="username" value="${claritydb.monthlyRankingRG.jdbc.username}" />
		<property name="password" value="${claritydb.monthlyRankingRG.jdbc.password}" />
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
		<property name="connectionProperties">
			<value>connection_timeout=3000000;socket_timeout=3000000;dataTransferTimeout=3000000</value>
		</property>
	</bean>

	<!-- for daily ranking qc -->
	<bean id="clarityDBDailyRankingQCDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.rankingQc.jdbc.driver}" />
		<property name="url" value="${claritydb.rankingQc.jdbc.url}" />
		<property name="username" value="${claritydb.rankingQc.jdbc.username}" />
		<property name="password" value="${claritydb.rankingQc.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBNewRIDao" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.newri.jdbc.driver}" />
		<property name="url" value="${claritydb.newri.jdbc.url}" />
		<property name="username" value="${claritydb.newri.jdbc.username}" />
		<property name="password" value="${claritydb.newri.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBCentralDao" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.central.jdbc.driver}" />
		<property name="url" value="${claritydb.central.jdbc.url}" />
		<property name="username" value="${claritydb.central.jdbc.username}" />
		<property name="password" value="${claritydb.central.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>


	<bean id="thgCkRgDao1" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${cdb.site.analytics1.jdbc.driver}" />
		<property name="url" value="${cdb.site.analytics1.jdbc.url}" />
		<property name="username" value="${cdb.site.analytics1.jdbc.username}" />
		<property name="password" value="${cdb.site.analytics1.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="thgCkRgDao2" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${cdb.site.analytics2.jdbc.driver}" />
		<property name="url" value="${cdb.site.analytics2.jdbc.url}" />
		<property name="username" value="${cdb.site.analytics3.jdbc.username}" />
		<property name="password" value="${cdb.site.analytics3.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="thgCkRgDao3" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${cdb.site.analytics3.jdbc.driver}" />
		<property name="url" value="${cdb.site.analytics3.jdbc.url}" />
		<property name="username" value="${cdb.site.analytics3.jdbc.username}" />
		<property name="password" value="${cdb.site.analytics3.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="thgCkRgDao4" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${cdb.site.analytics4.jdbc.driver}" />
		<property name="url" value="${cdb.site.analytics4.jdbc.url}" />
		<property name="username" value="${cdb.site.analytics4.jdbc.username}" />
		<property name="password" value="${cdb.site.analytics4.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="thgCkRgDao" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${thg.ck.rg.jdbc.driver}" />
		<property name="url" value="${thg.ck.rg.jdbc.url}" />
		<property name="username" value="${thg.ck.rg.jdbc.username}" />
		<property name="password" value="${thg.ck.rg.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
	</bean>

	<bean id="rankingDateDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.keywordRanking.jdbc.driver}" />
		<property name="url" value="${claritydb.keywordRanking.jdbc.url}" />
		<property name="username" value="${claritydb.keywordRanking.jdbc.username}" />
		<property name="password" value="${claritydb.keywordRanking.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBS3FileDetailDateSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.s3fileDetail.jdbc.driver}" />
		<property name="url" value="${claritydb.s3fileDetail.jdbc.url}" />
		<property name="username" value="${claritydb.s3fileDetail.jdbc.username}" />
		<property name="password" value="${claritydb.s3fileDetail.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBRi07KeywordSearchVolumeDateSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.ri07.keyword.searchvolume.jdbc.driver}" />
		<property name="url" value="${claritydb.ri07.keyword.searchvolume.jdbc.url}" />
		<property name="username" value="${claritydb.ri07.keyword.searchvolume.jdbc.username}" />
		<property name="password" value="${claritydb.ri07.keyword.searchvolume.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="siteClarityDateSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.siteclarity.jdbc.driver}" />
		<property name="url" value="${claritydb.siteclarity.jdbc.url}" />
		<property name="username" value="${claritydb.siteclarity.jdbc.username}" />
		<property name="password" value="${claritydb.siteclarity.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>
	<bean id="prodClarityDateSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.prodclarity.jdbc.driver}" />
		<property name="url" value="${claritydb.prodclarity.jdbc.url}" />
		<property name="username" value="${claritydb.prodclarity.jdbc.username}" />
		<property name="password" value="${claritydb.prodclarity.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>
	<bean id="prodClarityDateSource12" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.prodclarity12.jdbc.driver}" />
		<property name="url" value="${claritydb.prodclarity12.jdbc.url}" />
		<property name="username" value="${claritydb.prodclarity12.jdbc.username}" />
		<property name="password" value="${claritydb.prodclarity12.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>
	<bean id="prodClarityDateSource3" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.prodclarity3.jdbc.driver}" />
		<property name="url" value="${claritydb.prodclarity3.jdbc.url}" />
		<property name="username" value="${claritydb.prodclarity3.jdbc.username}" />
		<property name="password" value="${claritydb.prodclarity3.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="prodClarityFor360SummaryOnlyDateSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.prodclarity.jdbc.driver}" />
		<property name="url" value="${claritydb.prodclarity.jdbc.url}" />
		<property name="username" value="${claritydb.prodclarity.jdbc.username}" />
		<property name="password" value="${claritydb.prodclarity.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3600000;socket_timeout=3600000;dataTransferTimeout=3600000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="pixelVedClarityDateSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.pixel.ved.jdbc.driver}" />
		<property name="url" value="${claritydb.pixel.ved.jdbc.url}" />
		<property name="username" value="${claritydb.pixel.ved.jdbc.username}" />
		<property name="password" value="${claritydb.pixel.ved.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!--rade for keyword expand-->
	<bean id="clarityDBKeywordClickSteamDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.keyword.click.steam.jdbc.driver}" />
		<property name="url" value="${claritydb.keyword.click.steam.jdbc.url}" />
		<property name="username" value="${claritydb.keyword.click.steam.jdbc.username}" />
		<property name="password" value="${claritydb.keyword.click.steam.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3000000;socket_timeout=3000000;dataTransferTimeout=3000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<!--keyword cluster-->
	<bean id="clarityDBKeywordClusterDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.keywordcluster.jdbc.driver}" />
		<property name="url" value="${claritydb.keywordcluster.jdbc.url}" />
		<property name="username" value="${claritydb.keywordcluster.jdbc.username}" />
		<property name="password" value="${claritydb.keywordcluster.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3000000;socket_timeout=3000000;dataTransferTimeout=3000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBKeywordSearchVolumeDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.lwebkeywordsearchvolume.jdbc.driver}" />
		<property name="url" value="${claritydb.lwebkeywordsearchvolume.jdbc.url}" />
		<property name="username" value="${claritydb.lwebkeywordsearchvolume.jdbc.username}" />
		<property name="password" value="${claritydb.lwebkeywordsearchvolume.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3000000;socket_timeout=3000000;dataTransferTimeout=3000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBCdb21botDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.cdb21bot.jdbc.driver}" />
		<property name="url" value="${claritydb.cdb21bot.jdbc.url}" />
		<property name="username" value="${claritydb.cdb21bot.jdbc.username}" />
		<property name="password" value="${claritydb.cdb21bot.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=3000000;socket_timeout=3000000;dataTransferTimeout=3000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBPoc02DataSourceServer2" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.poc02.default.jdbc.driver}" />
		<property name="url" value="${claritydb.poc02.default.jdbc.url}" />
		<property name="username" value="${claritydb.poc02.default.jdbc.username}" />
		<property name="password" value="${claritydb.poc02.default.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=24000000;socket_timeout=24000000;dataTransferTimeout=24000000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBNj114KeywordSearchVolumeDateSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.nj114.keyword.searchvolume.jdbc.driver}" />
		<property name="url" value="${claritydb.nj114.keyword.searchvolume.jdbc.url}" />
		<property name="username" value="${claritydb.nj114.keyword.searchvolume.jdbc.username}" />
		<property name="password" value="${claritydb.nj114.keyword.searchvolume.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBNj114YoutubeDateSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.nj114.youtube.jdbc.driver}" />
		<property name="url" value="${claritydb.nj114.youtube.jdbc.url}" />
		<property name="username" value="${claritydb.nj114.youtube.jdbc.username}" />
		<property name="password" value="${claritydb.nj114.youtube.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="clarityDBCloudflareDateSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
		<property name="driverClassName" value="${claritydb.cloudflare.jdbc.driver}" />
		<property name="url" value="${claritydb.cloudflare.jdbc.url}" />
		<property name="username" value="${claritydb.cloudflare.jdbc.username}" />
		<property name="password" value="${claritydb.cloudflare.jdbc.password}" />
		<property name="connectionProperties">
			<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="socialEngineMasterBaseJdbcSupport" class="com.alibaba.druid.pool.DruidDataSource"
		destroy-method="close">
		<property name="driverClassName" value="${tiktok.master.claritydb.jdbc.driver}" />
		<property name="url" value="${tiktok.master.claritydb.jdbc.url}" />
		<property name="username" value="${tiktok.master.claritydb.jdbc.username}" />
		<property name="password" value="${tiktok.master.claritydb.jdbc.password}" />
		<property name="connectionProperties">
		<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="socialEngineColdBaseJdbcSupport" class="com.alibaba.druid.pool.DruidDataSource"
		destroy-method="close">
		<property name="driverClassName" value="${tiktok.backup.claritydb.jdbc.driver}" />
		<property name="url" value="${tiktok.backup.claritydb.jdbc.url}" />
		<property name="username" value="${tiktok.backup.claritydb.jdbc.username}" />
		<property name="password" value="${tiktok.backup.claritydb.jdbc.password}" />
		<property name="connectionProperties">
		<value>connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000</value>
		</property>
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select 1" />
	</bean>

	<bean id="lwebRankCheck01DataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close" >
		<property name="driverClassName" value="${lweb.rankcheck01.jdbc.driver}" />
		<property name="url" value="${lweb.rankcheck01.jdbc.url}" />
		<property name="username" value="${lweb.rankcheck01.jdbc.username}" />
		<property name="password" value="${lweb.rankcheck01.jdbc.password}" />
		<property name="initialSize" value="2" />
		<property name="validationQuery" value="select NOW() " />
		<property name="socketTimeout" value="180000"/>
		<property name="connectTimeout" value="180000"/>
		<property name="filters" value="com.alibaba.druid.filter.mysql8datetime.MySQL8DateTimeSqlTypeFilter"/>
		<property name="maxActive" value="16" />
		<property name="minIdle" value="2" />
		<property name="timeBetweenEvictionRunsMillis" value="60000" />
		<property name="minEvictableIdleTimeMillis" value="180000"/>
		<property name="maxEvictableIdleTimeMillis" value="190000"/>
		<property name="testOnBorrow" value="false" />
		<property name="testOnReturn" value="false" />
		<property name="testWhileIdle" value="true" />
		<property name="keepAlive" value="true" />
		<property name="proxyFilters">
			<list>
				<ref bean="stat-filter" />
			</list>
		</property>
		<property name="timeBetweenLogStatsMillis" value="300000" />
	</bean>

	<bean id="realtimeUrlDataSource" class="com.alibaba.druid.pool.DruidDataSource"
		  destroy-method="close">
        <property name="driverClassName" value="${claritydb.realtime_url.jdbc.driver}" />
        <property name="url" value="${claritydb.realtime_url.jdbc.url}" />
        <property name="username" value="${claritydb.realtime_url.jdbc.username}" />
        <property name="password" value="${claritydb.realtime_url.jdbc.password}" />
        <property name="connectionProperties"
                  value="connection_timeout=1200000;socket_timeout=1200000;dataTransferTimeout=1200000"/>
        <property name="initialSize" value="2" />
        <property name="validationQuery" value="select 1" />
    </bean>

</beans>

