# SplitTest 类重构总结

## 重构概述
成功将 `SplitTest.java` 类拆分为两个类，提高了代码的可维护性和组织结构。

## 重构内容

### 1. 创建的新文件
- **`src/main/java/seoclarity/backend/onetime/SplitTestHelper.java`**
  - 包含所有原本在 `SplitTest` 中的静态常量和方法
  - 作为工具类提供静态功能支持

### 2. 迁移到 SplitTestHelper 的内容

#### 静态常量
- 数据库配置常量：`DB_URL`, `CH_DB_NAME`, `USER`, `PSW`, `DB_QUERY_URL`
- API 配置常量：`endpoint`, `accessToken`, `httpClient`
- 应用程序常量：`urlMetricsMap`, `today`, `MIN_URLS_SIZE`, `MIN_DAYS`, `DEFAULT_LOOP_COUNT`, `DEFAULT_OFF_DAYS`
- SQL 查询模板：`distinctURLSql`, `GSC_INNER_SELECT_SQL`, `queryGSCInnerSql`, `GSC_SQL_PATTERN`, `GA_SQL_PATTERN`, `GA_URL_COLUMN_NAME`, `GA_INNER_SELECT_SQL`, `queryGAInnerSql`, `QUERY_SQL`
- SQL 特殊字符处理：`SQL_SPECIAL_CHAR`, `SQL_REGEX_ESCAPED_CHAR`
- 数据源类型常量：`DATA_SOURCE_TYPE_*` 系列常量

#### 静态方法
- `createUrlMetrics(String url, String data)` - 创建 UrlMetrics 对象
- `filterUrlByContentTypes(int domainId, int metricsType, UrlFilterParam urlFilter)` - 按内容类型过滤 URL
- `textTypeFilterToSql(String columnName, TextTypeFilterVO textTypeFilterVO)` - 文本类型过滤转 SQL
- `filterUrlByPageTagId(int domainId, int metricsType, UrlFilterParam urlFilter)` - 按页面标签 ID 过滤 URL
- `filterUrlByPages(String urlColumnName, String actionType, String searchInput)` - 按页面过滤 URL
- `getReplacedEscapedSpecialCharacterForRegx(String val)` - 替换正则表达式转义字符
- `checkSqlSpecialCharacter(String decodedStr)` - 检查和转义 SQL 特殊字符
- `httpQuerySql(String queryUrl)` - 执行 HTTP SQL 查询
- `getTable(Integer trafficType, Integer googleAnalyticsVersion)` - 获取表名

#### 静态初始化块
- 初始化 `SQL_REGEX_ESCAPED_CHAR` 列表
- 创建 HTTP 客户端连接池
- 构建数据库查询 URL

### 3. 保留在 SplitTest 中的内容

#### 实例变量
- `splitTestCentralDAO` - 数据访问对象
- `prePeriodStartDate`, `prePeriodEndDate`, `postPeriodStartDate`, `postPeriodEndDate` - 时间段变量
- `metricsParams` - 指标参数
- `distinctUrlList` - 不同 URL 列表

#### 实例方法
- `SplitTest(MetricsParams metricsParams)` - 构造函数
- `main(String[] args)` - 主方法
- `getDistinctUrlList(String innerSql)` - 获取不同 URL 列表
- `start(String innerSql)` - 启动分析
- `runShuffleSplitStrategy(String sql)` - 运行随机分割策略
- `runRandomDbSplitStrategy(String sql)` - 运行随机数据库分割策略
- 其他业务逻辑方法

#### 内部类
- `MetricsParams` - 指标参数类
- `UrlFilterParam` - URL 过滤参数类
- `UrlMetrics` - URL 指标类
- `CorrelationCoefficientRequest` - 相关系数请求类
- `CorrelationCoefficientResponse` - 相关系数响应类
- `SummaryMetricsResult` - 汇总指标结果类
- `CorrelationReport` - 相关性报告类

### 4. 更新的引用
所有对静态成员的引用都已更新为通过 `SplitTestHelper` 类访问，例如：
- `DEFAULT_OFF_DAYS` → `SplitTestHelper.DEFAULT_OFF_DAYS`
- `createUrlMetrics(url, data)` → `SplitTestHelper.createUrlMetrics(url, data)`
- `urlMetricsMap` → `SplitTestHelper.urlMetricsMap`

### 5. 清理的导入语句
移除了不再需要的导入语句，保持代码整洁。

## 重构优势

1. **职责分离**：静态工具方法与业务逻辑分离
2. **代码复用**：SplitTestHelper 可以被其他类复用
3. **可维护性**：更小的类文件，更容易理解和维护
4. **测试友好**：静态方法更容易进行单元测试
5. **符合单一职责原则**：每个类都有明确的职责

## 验证
重构后的代码保持了完全相同的功能，所有方法调用都已正确更新，确保向后兼容性。
